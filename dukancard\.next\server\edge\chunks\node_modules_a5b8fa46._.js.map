{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/@supabase/node-fetch/browser.js"], "sourcesContent": ["\"use strict\";\n\n// ref: https://github.com/tc39/proposal-global\nvar getGlobal = function() {\n    // the only reliable means to get the global object is\n    // `Function('return this')()`\n    // However, this causes CSP violations in Chrome apps.\n    if (typeof self !== 'undefined') { return self; }\n    if (typeof window !== 'undefined') { return window; }\n    if (typeof global !== 'undefined') { return global; }\n    throw new Error('unable to locate global object');\n}\n\nvar globalObject = getGlobal();\n\nexport const fetch = globalObject.fetch;\n\nexport default globalObject.fetch.bind(globalObject);\n\nexport const Headers = globalObject.Headers;\nexport const Request = globalObject.Request;\nexport const Response = globalObject.Response;\n"], "names": [], "mappings": ";;;;;;;AAAA;AAEA,+CAA+C;AAC/C,IAAI,YAAY;IACZ,sDAAsD;IACtD,8BAA8B;IAC9B,sDAAsD;IACtD,IAAI,OAAO,SAAS,aAAa;QAAE,OAAO;IAAM;IAChD,uCAAmC;;IAAiB;IACpD,IAAI,OAAO,WAAW,aAAa;QAAE,OAAO;IAAQ;IACpD,MAAM,IAAI,MAAM;AACpB;AAEA,IAAI,eAAe;AAEZ,MAAM,QAAQ,aAAa,KAAK;uCAExB,aAAa,KAAK,CAAC,IAAI,CAAC;AAEhC,MAAM,UAAU,aAAa,OAAO;AACpC,MAAM,UAAU,aAAa,OAAO;AACpC,MAAM,WAAW,aAAa,QAAQ", "ignoreList": [0]}}, {"offset": {"line": 43, "column": 0}, "map": {"version": 3, "file": "helper.js", "sources": ["../../src/helper.ts"], "sourceRoot": "", "names": [], "mappings": ";;;AAEO,MAAM,YAAY,GAAG,CAAC,WAAmB,EAAS,EAAE;IACzD,IAAI,MAAa,CAAA;IACjB,IAAI,WAAW,EAAE;QACf,MAAM,GAAG,WAAW,CAAA;KACrB,MAAM,IAAI,OAAO,KAAK,KAAK,WAAW,EAAE;QACvC,MAAM,GAAG,CAAC,GAAG,IAAI,EAAE,CACjB,CADmB,KACb,CAAC,sBAA6B,CAAC,+GAAC,IAAI,CAAC,CAAC,EAAE,OAAO,EAAE,MAAK,EAAE,EAAE,CAAG,CAAD,IAAM,CAAC,IAAG,IAAI,CAAC,CAAC,CAAA;KACrF,MAAM;QACL,MAAM,GAAG,KAAK,CAAA;KACf;IACD,OAAO,CAAC,GAAG,IAAI,EAAE,CAAG,CAAD,KAAO,CAAC,GAAG,IAAI,CAAC,CAAA;AACrC,CAAC,CAAA"}}, {"offset": {"line": 63, "column": 0}, "map": {"version": 3, "file": "types.js", "sources": ["../../src/types.ts"], "sourceRoot": "", "names": [], "mappings": ";;;;;;;AAgBM,MAAO,cAAe,SAAQ,KAAK;IAEvC,YAAY,OAAe,EAAE,IAAI,GAAG,gBAAgB,EAAE,OAAa,CAAA;QACjE,KAAK,CAAC,OAAO,CAAC,CAAA;QACd,IAAI,CAAC,IAAI,GAAG,IAAI,CAAA;QAChB,IAAI,CAAC,OAAO,GAAG,OAAO,CAAA;IACxB,CAAC;CACF;AAEK,MAAO,mBAAoB,SAAQ,cAAc;IACrD,YAAY,OAAY,CAAA;QACtB,KAAK,CAAC,+CAA+C,EAAE,qBAAqB,EAAE,OAAO,CAAC,CAAA;IACxF,CAAC;CACF;AAEK,MAAO,mBAAoB,SAAQ,cAAc;IACrD,YAAY,OAAY,CAAA;QACtB,KAAK,CAAC,wCAAwC,EAAE,qBAAqB,EAAE,OAAO,CAAC,CAAA;IACjF,CAAC;CACF;AAEK,MAAO,kBAAmB,SAAQ,cAAc;IACpD,YAAY,OAAY,CAAA;QACtB,KAAK,CAAC,8CAA8C,EAAE,oBAAoB,EAAE,OAAO,CAAC,CAAA;IACtF,CAAC;CACF;AAED,IAAY,cAgBX;AAhBD,CAAA,SAAY,cAAc;IACxB,cAAA,CAAA,MAAA,GAAA,KAAW,CAAA;IACX,cAAA,CAAA,eAAA,GAAA,gBAA+B,CAAA;IAC/B,cAAA,CAAA,eAAA,GAAA,gBAA+B,CAAA;IAC/B,cAAA,CAAA,WAAA,GAAA,YAAuB,CAAA;IACvB,cAAA,CAAA,eAAA,GAAA,gBAA+B,CAAA;IAC/B,cAAA,CAAA,eAAA,GAAA,gBAA+B,CAAA;IAC/B,cAAA,CAAA,aAAA,GAAA,cAA2B,CAAA;IAC3B,cAAA,CAAA,aAAA,GAAA,cAA2B,CAAA;IAC3B,cAAA,CAAA,UAAA,GAAA,WAAqB,CAAA;IACrB,cAAA,CAAA,UAAA,GAAA,WAAqB,CAAA;IACrB,cAAA,CAAA,UAAA,GAAA,WAAqB,CAAA;IACrB,cAAA,CAAA,UAAA,GAAA,WAAqB,CAAA;IACrB,cAAA,CAAA,UAAA,GAAA,WAAqB,CAAA;IACrB,cAAA,CAAA,UAAA,GAAA,WAAqB,CAAA;IACrB,cAAA,CAAA,UAAA,GAAA,WAAqB,CAAA;AACvB,CAAC,EAhBW,cAAc,IAAA,CAAd,cAAc,GAAA,CAAA,CAAA,GAgBzB"}}, {"offset": {"line": 116, "column": 0}, "map": {"version": 3, "file": "FunctionsClient.js", "sources": ["../../src/FunctionsClient.ts"], "sourceRoot": "", "names": [], "mappings": ";;;AAAA,OAAO,EAAE,YAAY,EAAE,MAAM,UAAU,CAAA;AACvC,OAAO,EAEL,mBAAmB,EACnB,kBAAkB,EAClB,mBAAmB,EAGnB,cAAc,GACf,MAAM,SAAS,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEV,MAAO,eAAe;IAM1B,YACE,GAAW,EACX,EACE,OAAO,GAAG,CAAA,CAAE,EACZ,WAAW,EACX,MAAM,oLAAG,iBAAc,CAAC,GAAG,EAAA,GAKzB,CAAA,CAAE,CAAA;QAEN,IAAI,CAAC,GAAG,GAAG,GAAG,CAAA;QACd,IAAI,CAAC,OAAO,GAAG,OAAO,CAAA;QACtB,IAAI,CAAC,MAAM,GAAG,MAAM,CAAA;QACpB,IAAI,CAAC,KAAK,GAAG,qMAAA,AAAY,EAAC,WAAW,CAAC,CAAA;IACxC,CAAC;IAED;;;OAGG,CACH,OAAO,CAAC,KAAa,EAAA;QACnB,IAAI,CAAC,OAAO,CAAC,aAAa,GAAG,CAAA,OAAA,EAAU,KAAK,EAAE,CAAA;IAChD,CAAC;IAED;;;;OAIG,CACG,MAAM,CACV,YAAoB,EACpB,UAAiC,CAAA,CAAE,EAAA;;;YAEnC,IAAI;gBACF,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,IAAI,EAAE,YAAY,EAAE,GAAG,OAAO,CAAA;gBACvD,IAAI,QAAQ,GAA2B,CAAA,CAAE,CAAA;gBACzC,IAAI,EAAE,MAAM,EAAE,GAAG,OAAO,CAAA;gBACxB,IAAI,CAAC,MAAM,EAAE;oBACX,MAAM,GAAG,IAAI,CAAC,MAAM,CAAA;iBACrB;gBACD,IAAI,MAAM,IAAI,MAAM,KAAK,KAAK,EAAE;oBAC9B,QAAQ,CAAC,UAAU,CAAC,GAAG,MAAM,CAAA;iBAC9B;gBACD,IAAI,IAAS,CAAA;gBACb,IACE,YAAY,IACZ,CAAC,AAAC,OAAO,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,OAAO,EAAE,cAAc,CAAC,CAAC,GAAI,CAAC,OAAO,CAAC,EACzF;oBACA,IACE,AAAC,OAAO,IAAI,KAAK,WAAW,IAAI,YAAY,YAAY,IAAI,CAAC,GAC7D,YAAY,YAAY,WAAW,EACnC;wBACA,2CAA2C;wBAC3C,8EAA8E;wBAC9E,QAAQ,CAAC,cAAc,CAAC,GAAG,0BAA0B,CAAA;wBACrD,IAAI,GAAG,YAAY,CAAA;qBACpB,MAAM,IAAI,OAAO,YAAY,KAAK,QAAQ,EAAE;wBAC3C,eAAe;wBACf,QAAQ,CAAC,cAAc,CAAC,GAAG,YAAY,CAAA;wBACvC,IAAI,GAAG,YAAY,CAAA;qBACpB,MAAM,IAAI,OAAO,QAAQ,KAAK,WAAW,IAAI,YAAY,YAAY,QAAQ,EAAE;wBAC9E,iCAAiC;wBACjC,0DAA0D;wBAC1D,IAAI,GAAG,YAAY,CAAA;qBACpB,MAAM;wBACL,+BAA+B;wBAC/B,QAAQ,CAAC,cAAc,CAAC,GAAG,kBAAkB,CAAA;wBAC7C,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,CAAA;qBACpC;iBACF;gBAED,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,GAAG,CAAA,CAAA,EAAI,YAAY,EAAE,EAAE;oBAC/D,MAAM,EAAE,MAAM,IAAI,MAAM;oBACxB,qCAAqC;oBACrC,0BAA0B;oBAC1B,0BAA0B;oBAC1B,iCAAiC;oBACjC,OAAO,EAAA,OAAA,MAAA,CAAA,OAAA,MAAA,CAAA,OAAA,MAAA,CAAA,CAAA,GAAO,QAAQ,GAAK,IAAI,CAAC,OAAO,GAAK,OAAO,CAAE;oBACrD,IAAI;iBACL,CAAC,CAAC,KAAK,CAAC,CAAC,UAAU,EAAE,EAAE;oBACtB,MAAM,qLAAI,sBAAmB,CAAC,UAAU,CAAC,CAAA;gBAC3C,CAAC,CAAC,CAAA;gBAEF,MAAM,YAAY,GAAG,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC,CAAA;gBAC1D,IAAI,YAAY,IAAI,YAAY,KAAK,MAAM,EAAE;oBAC3C,MAAM,qLAAI,sBAAmB,CAAC,QAAQ,CAAC,CAAA;iBACxC;gBAED,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE;oBAChB,MAAM,qLAAI,qBAAkB,CAAC,QAAQ,CAAC,CAAA;iBACvC;gBAED,IAAI,YAAY,GAAG,CAAC,CAAA,KAAA,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,MAAA,QAAA,OAAA,KAAA,IAAA,KAAI,YAAY,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAA;gBAC9F,IAAI,IAAS,CAAA;gBACb,IAAI,YAAY,KAAK,kBAAkB,EAAE;oBACvC,IAAI,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAA;iBAC7B,MAAM,IAAI,YAAY,KAAK,0BAA0B,EAAE;oBACtD,IAAI,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAA;iBAC7B,MAAM,IAAI,YAAY,KAAK,mBAAmB,EAAE;oBAC/C,IAAI,GAAG,QAAQ,CAAA;iBAChB,MAAM,IAAI,YAAY,KAAK,qBAAqB,EAAE;oBACjD,IAAI,GAAG,MAAM,QAAQ,CAAC,QAAQ,EAAE,CAAA;iBACjC,MAAM;oBACL,kBAAkB;oBAClB,IAAI,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAA;iBAC7B;gBAED,OAAO;oBAAE,IAAI;oBAAE,KAAK,EAAE,IAAI;gBAAA,CAAE,CAAA;aAC7B,CAAC,OAAO,KAAK,EAAE;gBACd,OAAO;oBAAE,IAAI,EAAE,IAAI;oBAAE,KAAK;gBAAA,CAAE,CAAA;aAC7B;;KACF;CACF"}}, {"offset": {"line": 251, "column": 0}, "map": {"version": 3, "file": "PostgrestError.js", "sources": ["../../src/PostgrestError.ts"], "sourceRoot": "", "names": [], "mappings": ";;;;AAAA;;;;GAIG,CACH,MAAqB,cAAe,SAAQ,KAAK;IAK/C,YAAY,OAAyE,CAAA;QACnF,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,CAAA;QACtB,IAAI,CAAC,IAAI,GAAG,gBAAgB,CAAA;QAC5B,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO,CAAA;QAC9B,IAAI,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,CAAA;QACxB,IAAI,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,CAAA;IAC1B,CAAC;CACF;AAZD,QAAA,OAAA,GAAA,eAYC"}}, {"offset": {"line": 274, "column": 0}, "map": {"version": 3, "file": "PostgrestBuilder.js", "sources": ["../../src/PostgrestBuilder.ts"], "sourceRoot": "", "names": [], "mappings": ";;;;;;;;;AAAA,aAAa;AACb,MAAA,eAAA,iDAA4C;AAU5C,MAAA,mBAAA,6CAA6C;AAG7C,MAA8B,gBAAgB;IAgB5C,YAAY,OAAiC,CAAA;QALnC,IAAA,CAAA,kBAAkB,GAAG,KAAK,CAAA;QAMlC,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,CAAA;QAC5B,IAAI,CAAC,GAAG,GAAG,OAAO,CAAC,GAAG,CAAA;QACtB,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO,CAAA;QAC9B,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,CAAA;QAC5B,IAAI,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,CAAA;QACxB,IAAI,CAAC,kBAAkB,GAAG,OAAO,CAAC,kBAAkB,CAAA;QACpD,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,CAAA;QAC5B,IAAI,CAAC,aAAa,GAAG,OAAO,CAAC,aAAa,CAAA;QAE1C,IAAI,OAAO,CAAC,KAAK,EAAE;YACjB,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,CAAA;SAC3B,MAAM,IAAI,OAAO,KAAK,KAAK,WAAW,EAAE;YACvC,IAAI,CAAC,KAAK,GAAG,aAAA,OAAS,CAAA;SACvB,MAAM;YACL,IAAI,CAAC,KAAK,GAAG,KAAK,CAAA;SACnB;IACH,CAAC;IAED;;;;;OAKG,CACH,YAAY,GAAA;QACV,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAA;QAC9B,OAAO,IAA6C,CAAA;IACtD,CAAC;IAED;;OAEG,CACH,SAAS,CAAC,IAAY,EAAE,KAAa,EAAA;QACnC,IAAI,CAAC,OAAO,GAAA,OAAA,MAAA,CAAA,CAAA,GAAQ,IAAI,CAAC,OAAO,CAAE,CAAA;QAClC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,KAAK,CAAA;QAC1B,OAAO,IAAI,CAAA;IACb,CAAC;IAED,IAAI,CAMF,WAOQ,EACR,UAAmF,EAAA;QAEnF,6DAA6D;QAC7D,IAAI,IAAI,CAAC,MAAM,KAAK,SAAS,EAAE;QAC7B,OAAO;SACR,MAAM,IAAI;YAAC,KAAK;YAAE,MAAM;SAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE;YAChD,IAAI,CAAC,OAAO,CAAC,gBAAgB,CAAC,GAAG,IAAI,CAAC,MAAM,CAAA;SAC7C,MAAM;YACL,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,GAAG,IAAI,CAAC,MAAM,CAAA;SAC9C;QACD,IAAI,IAAI,CAAC,MAAM,KAAK,KAAK,IAAI,IAAI,CAAC,MAAM,KAAK,MAAM,EAAE;YACnD,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,GAAG,kBAAkB,CAAA;SAClD;QAED,6DAA6D;QAC7D,oDAAoD;QACpD,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAA;QACzB,IAAI,GAAG,GAAG,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE,EAAE;YACpC,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC;YAC/B,MAAM,EAAE,IAAI,CAAC,MAAM;SACpB,CAAC,CAAC,IAAI,CAAC,KAAK,EAAE,GAAG,EAAE,EAAE;;YACpB,IAAI,KAAK,GAAG,IAAI,CAAA;YAChB,IAAI,IAAI,GAAG,IAAI,CAAA;YACf,IAAI,KAAK,GAAkB,IAAI,CAAA;YAC/B,IAAI,MAAM,GAAG,GAAG,CAAC,MAAM,CAAA;YACvB,IAAI,UAAU,GAAG,GAAG,CAAC,UAAU,CAAA;YAE/B,IAAI,GAAG,CAAC,EAAE,EAAE;gBACV,IAAI,IAAI,CAAC,MAAM,KAAK,MAAM,EAAE;oBAC1B,MAAM,IAAI,GAAG,MAAM,GAAG,CAAC,IAAI,EAAE,CAAA;oBAC7B,IAAI,IAAI,KAAK,EAAE,EAAE;oBACf,yBAAyB;qBAC1B,MAAM,IAAI,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,UAAU,EAAE;wBAChD,IAAI,GAAG,IAAI,CAAA;qBACZ,MAAM,IACL,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,IACtB,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,QAAQ,CAAC,iCAAiC,CAAC,EAClE;wBACA,IAAI,GAAG,IAAI,CAAA;qBACZ,MAAM;wBACL,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;qBACxB;iBACF;gBAED,MAAM,WAAW,GAAG,CAAA,KAAA,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAE,KAAK,CAAC,iCAAiC,CAAC,CAAA;gBACpF,MAAM,YAAY,GAAG,CAAA,KAAA,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAE,KAAK,CAAC,GAAG,CAAC,CAAA;gBACjE,IAAI,WAAW,IAAI,YAAY,IAAI,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE;oBAC1D,KAAK,GAAG,QAAQ,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAA;iBAClC;gBAED,gFAAgF;gBAChF,kEAAkE;gBAClE,IAAI,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,MAAM,KAAK,KAAK,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;oBACtE,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE;wBACnB,KAAK,GAAG;4BACN,mHAAmH;4BACnH,IAAI,EAAE,UAAU;4BAChB,OAAO,EAAE,CAAA,gBAAA,EAAmB,IAAI,CAAC,MAAM,CAAA,uDAAA,CAAyD;4BAChG,IAAI,EAAE,IAAI;4BACV,OAAO,EAAE,uDAAuD;yBACjE,CAAA;wBACD,IAAI,GAAG,IAAI,CAAA;wBACX,KAAK,GAAG,IAAI,CAAA;wBACZ,MAAM,GAAG,GAAG,CAAA;wBACZ,UAAU,GAAG,gBAAgB,CAAA;qBAC9B,MAAM,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;wBAC5B,IAAI,GAAG,IAAI,CAAC,CAAC,CAAC,CAAA;qBACf,MAAM;wBACL,IAAI,GAAG,IAAI,CAAA;qBACZ;iBACF;aACF,MAAM;gBACL,MAAM,IAAI,GAAG,MAAM,GAAG,CAAC,IAAI,EAAE,CAAA;gBAE7B,IAAI;oBACF,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;oBAExB,qEAAqE;oBACrE,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,GAAG,CAAC,MAAM,KAAK,GAAG,EAAE;wBAC9C,IAAI,GAAG,EAAE,CAAA;wBACT,KAAK,GAAG,IAAI,CAAA;wBACZ,MAAM,GAAG,GAAG,CAAA;wBACZ,UAAU,GAAG,IAAI,CAAA;qBAClB;iBACF,CAAC,OAAA,IAAM;oBACN,qEAAqE;oBACrE,IAAI,GAAG,CAAC,MAAM,KAAK,GAAG,IAAI,IAAI,KAAK,EAAE,EAAE;wBACrC,MAAM,GAAG,GAAG,CAAA;wBACZ,UAAU,GAAG,YAAY,CAAA;qBAC1B,MAAM;wBACL,KAAK,GAAG;4BACN,OAAO,EAAE,IAAI;yBACd,CAAA;qBACF;iBACF;gBAED,IAAI,KAAK,IAAI,IAAI,CAAC,aAAa,IAAA,CAAI,CAAA,KAAA,KAAK,KAAA,QAAL,KAAK,KAAA,KAAA,IAAA,KAAA,IAAL,KAAK,CAAE,OAAO,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAE,QAAQ,CAAC,QAAQ,CAAC,CAAA,EAAE;oBACrE,KAAK,GAAG,IAAI,CAAA;oBACZ,MAAM,GAAG,GAAG,CAAA;oBACZ,UAAU,GAAG,IAAI,CAAA;iBAClB;gBAED,IAAI,KAAK,IAAI,IAAI,CAAC,kBAAkB,EAAE;oBACpC,MAAM,IAAI,iBAAA,OAAc,CAAC,KAAK,CAAC,CAAA;iBAChC;aACF;YAED,MAAM,iBAAiB,GAAG;gBACxB,KAAK;gBACL,IAAI;gBACJ,KAAK;gBACL,MAAM;gBACN,UAAU;aACX,CAAA;YAED,OAAO,iBAAiB,CAAA;QAC1B,CAAC,CAAC,CAAA;QACF,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE;YAC5B,GAAG,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC,UAAU,EAAE,EAAE;;gBAAC,OAAA,AAAC;oBAC/B,KAAK,EAAE;wBACL,OAAO,EAAE,GAAG,CAAA,KAAA,UAAU,KAAA,QAAV,UAAU,KAAA,KAAA,IAAA,KAAA,IAAV,UAAU,CAAE,IAAI,MAAA,QAAA,OAAA,KAAA,IAAA,KAAI,YAAY,CAAA,EAAA,EAAK,UAAU,KAAA,QAAV,UAAU,KAAA,KAAA,IAAA,KAAA,IAAV,UAAU,CAAE,OAAO,EAAE;wBACtE,OAAO,EAAE,GAAG,CAAA,KAAA,UAAU,KAAA,QAAV,UAAU,KAAA,KAAA,IAAA,KAAA,IAAV,UAAU,CAAE,KAAK,MAAA,QAAA,OAAA,KAAA,IAAA,KAAI,EAAE,EAAE;wBACrC,IAAI,EAAE,EAAE;wBACR,IAAI,EAAE,GAAG,CAAA,KAAA,UAAU,KAAA,QAAV,UAAU,KAAA,KAAA,IAAA,KAAA,IAAV,UAAU,CAAE,IAAI,MAAA,QAAA,OAAA,KAAA,IAAA,KAAI,EAAE,EAAE;qBAClC;oBACD,IAAI,EAAE,IAAI;oBACV,KAAK,EAAE,IAAI;oBACX,MAAM,EAAE,CAAC;oBACT,UAAU,EAAE,EAAE;iBACf,CAAC,CAAA;aAAA,CAAC,CAAA;SACJ;QAED,OAAO,GAAG,CAAC,IAAI,CAAC,WAAW,EAAE,UAAU,CAAC,CAAA;IAC1C,CAAC;IAED;;;;;OAKG,CACH,OAAO,GAAA;QACL,wBAAA,EAA0B,CAC1B,OAAO,IAGN,CAAA;IACH,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;OAqBG,CACH,aAAa,GAAA;QAYX,OAAO,IAQN,CAAA;IACH,CAAC;CACF;AAxQD,QAAA,OAAA,GAAA,iBAwQC"}}, {"offset": {"line": 488, "column": 0}, "map": {"version": 3, "file": "PostgrestTransformBuilder.js", "sources": ["../../src/PostgrestTransformBuilder.ts"], "sourceRoot": "", "names": [], "mappings": ";;;;;;;;;AAAA,MAAA,qBAAA,+CAAiD;AAIjD,MAAqB,yBAMnB,SAAQ,mBAAA,OAAwB;IAChC;;;;;;;;OAQG,CACH,MAAM,CAIJ,OAAe,EAAA;QAEf,wCAAwC;QACxC,IAAI,MAAM,GAAG,KAAK,CAAA;QAClB,MAAM,cAAc,GAAG,CAAC,OAAO,KAAA,QAAP,OAAO,KAAA,KAAA,IAAP,OAAO,GAAI,GAAG,CAAC,CACpC,KAAK,CAAC,EAAE,CAAC,CACT,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE;YACT,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,EAAE;gBAC3B,OAAO,EAAE,CAAA;aACV;YACD,IAAI,CAAC,KAAK,GAAG,EAAE;gBACb,MAAM,GAAG,CAAC,MAAM,CAAA;aACjB;YACD,OAAO,CAAC,CAAA;QACV,CAAC,CAAC,CACD,IAAI,CAAC,EAAE,CAAC,CAAA;QACX,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,QAAQ,EAAE,cAAc,CAAC,CAAA;QACnD,IAAI,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;YAC1B,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,GAAG,CAAA;SAC9B;QACD,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,uBAAuB,CAAA;QACjD,OAAO,IAMN,CAAA;IACH,CAAC;IAwBD;;;;;;;;;;;;;;;;;OAiBG,CACH,KAAK,CACH,MAAc,EACd,EACE,SAAS,GAAG,IAAI,EAChB,UAAU,EACV,YAAY,EACZ,eAAe,GAAG,YAAY,EAAA,GAM5B,CAAA,CAAE,EAAA;QAEN,MAAM,GAAG,GAAG,eAAe,CAAC,CAAC,CAAC,GAAG,eAAe,CAAA,MAAA,CAAQ,CAAC,CAAC,CAAC,OAAO,CAAA;QAClE,MAAM,aAAa,GAAG,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,GAAG,CAAC,CAAA;QAEpD,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,GAAG,CACvB,GAAG,EACH,GAAG,aAAa,CAAC,CAAC,CAAC,GAAG,aAAa,CAAA,CAAA,CAAG,CAAC,CAAC,CAAC,EAAE,GAAG,MAAM,CAAA,CAAA,EAAI,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,GAChF,UAAU,KAAK,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,YAC/D,EAAE,CACH,CAAA;QACD,OAAO,IAAI,CAAA;IACb,CAAC;IAED;;;;;;;;;OASG,CACH,KAAK,CACH,KAAa,EACb,EACE,YAAY,EACZ,eAAe,GAAG,YAAY,EAAA,GACyB,CAAA,CAAE,EAAA;QAE3D,MAAM,GAAG,GAAG,OAAO,eAAe,KAAK,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,eAAe,CAAA,MAAA,CAAQ,CAAA;QACzF,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,KAAK,EAAE,CAAC,CAAA;QAC1C,OAAO,IAAI,CAAA;IACb,CAAC;IAED;;;;;;;;;;;;;;OAcG,CACH,KAAK,CACH,IAAY,EACZ,EAAU,EACV,EACE,YAAY,EACZ,eAAe,GAAG,YAAY,EAAA,GACyB,CAAA,CAAE,EAAA;QAE3D,MAAM,SAAS,GACb,OAAO,eAAe,KAAK,WAAW,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,eAAe,CAAA,OAAA,CAAS,CAAA;QACjF,MAAM,QAAQ,GAAG,OAAO,eAAe,KAAK,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,eAAe,CAAA,MAAA,CAAQ,CAAA;QAC9F,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,SAAS,EAAE,GAAG,IAAI,EAAE,CAAC,CAAA;QAC/C,+BAA+B;QAC/B,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,QAAQ,EAAE,GAAG,EAAE,GAAG,IAAI,GAAG,CAAC,EAAE,CAAC,CAAA;QACvD,OAAO,IAAI,CAAA;IACb,CAAC;IAED;;;;OAIG,CACH,WAAW,CAAC,MAAmB,EAAA;QAC7B,IAAI,CAAC,MAAM,GAAG,MAAM,CAAA;QACpB,OAAO,IAAI,CAAA;IACb,CAAC;IAED;;;;;OAKG,CACH,MAAM,GAAA;QAGJ,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,mCAAmC,CAAA;QAC5D,OAAO,IAA8C,CAAA;IACvD,CAAC;IAED;;;;;OAKG,CACH,WAAW,GAAA;QAGT,gFAAgF;QAChF,kEAAkE;QAClE,IAAI,IAAI,CAAC,MAAM,KAAK,KAAK,EAAE;YACzB,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,kBAAkB,CAAA;SAC5C,MAAM;YACL,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,mCAAmC,CAAA;SAC7D;QACD,IAAI,CAAC,aAAa,GAAG,IAAI,CAAA;QACzB,OAAO,IAAqD,CAAA;IAC9D,CAAC;IAED;;OAEG,CACH,GAAG,GAAA;QACD,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,UAAU,CAAA;QACnC,OAAO,IAA2C,CAAA;IACpD,CAAC;IAED;;OAEG,CACH,OAAO,GAAA;QACL,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,sBAAsB,CAAA;QAC/C,OAAO,IAA4D,CAAA;IACrE,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;OAwBG,CACH,OAAO,CAAC,EACN,OAAO,GAAG,KAAK,EACf,OAAO,GAAG,KAAK,EACf,QAAQ,GAAG,KAAK,EAChB,OAAO,GAAG,KAAK,EACf,GAAG,GAAG,KAAK,EACX,MAAM,GAAG,MAAM,EAAA,GAQb,CAAA,CAAE,EAAA;;QACJ,MAAM,OAAO,GAAG;YACd,OAAO,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI;YAC1B,OAAO,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI;YAC1B,QAAQ,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI;YAC5B,OAAO,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI;YAC1B,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI;SACnB,CACE,MAAM,CAAC,OAAO,CAAC,CACf,IAAI,CAAC,GAAG,CAAC,CAAA;QACZ,oFAAoF;QACpF,MAAM,YAAY,GAAG,CAAA,KAAA,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,MAAA,QAAA,OAAA,KAAA,IAAA,KAAI,kBAAkB,CAAA;QACjE,IAAI,CAAC,OAAO,CACV,QAAQ,CACT,GAAG,CAAA,2BAAA,EAA8B,MAAM,CAAA,OAAA,EAAU,YAAY,CAAA,WAAA,EAAc,OAAO,CAAA,CAAA,CAAG,CAAA;QACtF,IAAI,MAAM,KAAK,MAAM,EAAE,OAAO,IAA8D,CAAA;aACvF,OAAO,IAA2C,CAAA;IACzD,CAAC;IAED;;;;OAIG,CACH,QAAQ,GAAA;;QACN,IAAI,CAAC,CAAA,KAAA,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,MAAA,QAAA,OAAA,KAAA,IAAA,KAAI,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,MAAM,GAAG,CAAC,EAAE;YACpD,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,cAAc,CAAA;SACzC,MAAM;YACL,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,aAAa,CAAA;SACvC;QACD,OAAO,IAAI,CAAA;IACb,CAAC;IAED;;;;;OAKG,CACH,OAAO,GAAA;QAOL,OAAO,IAMN,CAAA;IACH,CAAC;CACF;AAlUD,QAAA,OAAA,GAAA,0BAkUC"}}, {"offset": {"line": 697, "column": 0}, "map": {"version": 3, "file": "PostgrestFilterBuilder.js", "sources": ["../../src/PostgrestFilterBuilder.ts"], "sourceRoot": "", "names": [], "mappings": ";;;;;;;;;AAAA,MAAA,8BAAA,wDAAmE;AAuEnE,MAAqB,sBAMnB,SAAQ,4BAAA,OAA2E;IACnF;;;;;;;OAOG,CACH,EAAE,CACA,MAAkB,EAClB,KAOS,EAAA;QAET,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,MAAM,CAAC,MAAM,EAAE,CAAA,GAAA,EAAM,KAAK,EAAE,CAAC,CAAA;QACnD,OAAO,IAAI,CAAA;IACb,CAAC;IAED;;;;;OAKG,CACH,GAAG,CACD,MAAkB,EAClB,KAIS,EAAA;QAET,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,MAAM,CAAC,MAAM,EAAE,CAAA,IAAA,EAAO,KAAK,EAAE,CAAC,CAAA;QACpD,OAAO,IAAI,CAAA;IACb,CAAC;IAID;;;;;OAKG,CACH,EAAE,CAAC,MAAc,EAAE,KAAc,EAAA;QAC/B,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,MAAM,CAAC,MAAM,EAAE,CAAA,GAAA,EAAM,KAAK,EAAE,CAAC,CAAA;QACnD,OAAO,IAAI,CAAA;IACb,CAAC;IAID;;;;;OAKG,CACH,GAAG,CAAC,MAAc,EAAE,KAAc,EAAA;QAChC,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,MAAM,CAAC,MAAM,EAAE,CAAA,IAAA,EAAO,KAAK,EAAE,CAAC,CAAA;QACpD,OAAO,IAAI,CAAA;IACb,CAAC;IAID;;;;;OAKG,CACH,EAAE,CAAC,MAAc,EAAE,KAAc,EAAA;QAC/B,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,MAAM,CAAC,MAAM,EAAE,CAAA,GAAA,EAAM,KAAK,EAAE,CAAC,CAAA;QACnD,OAAO,IAAI,CAAA;IACb,CAAC;IAID;;;;;OAKG,CACH,GAAG,CAAC,MAAc,EAAE,KAAc,EAAA;QAChC,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,MAAM,CAAC,MAAM,EAAE,CAAA,IAAA,EAAO,KAAK,EAAE,CAAC,CAAA;QACpD,OAAO,IAAI,CAAA;IACb,CAAC;IAID;;;;;OAKG,CACH,IAAI,CAAC,MAAc,EAAE,OAAe,EAAA;QAClC,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,MAAM,CAAC,MAAM,EAAE,CAAA,KAAA,EAAQ,OAAO,EAAE,CAAC,CAAA;QACvD,OAAO,IAAI,CAAA;IACb,CAAC;IAOD;;;;;OAKG,CACH,SAAS,CAAC,MAAc,EAAE,QAA2B,EAAA;QACnD,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,MAAM,CAAC,MAAM,EAAE,CAAA,WAAA,EAAc,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA,CAAA,CAAG,CAAC,CAAA;QACzE,OAAO,IAAI,CAAA;IACb,CAAC;IAOD;;;;;OAKG,CACH,SAAS,CAAC,MAAc,EAAE,QAA2B,EAAA;QACnD,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,MAAM,CAAC,MAAM,EAAE,CAAA,WAAA,EAAc,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA,CAAA,CAAG,CAAC,CAAA;QACzE,OAAO,IAAI,CAAA;IACb,CAAC;IAID;;;;;OAKG,CACH,KAAK,CAAC,MAAc,EAAE,OAAe,EAAA;QACnC,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,MAAM,CAAC,MAAM,EAAE,CAAA,MAAA,EAAS,OAAO,EAAE,CAAC,CAAA;QACxD,OAAO,IAAI,CAAA;IACb,CAAC;IAOD;;;;;OAKG,CACH,UAAU,CAAC,MAAc,EAAE,QAA2B,EAAA;QACpD,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,MAAM,CAAC,MAAM,EAAE,CAAA,YAAA,EAAe,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA,CAAA,CAAG,CAAC,CAAA;QAC1E,OAAO,IAAI,CAAA;IACb,CAAC;IAOD;;;;;OAKG,CACH,UAAU,CAAC,MAAc,EAAE,QAA2B,EAAA;QACpD,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,MAAM,CAAC,MAAM,EAAE,CAAA,YAAA,EAAe,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA,CAAA,CAAG,CAAC,CAAA;QAC1E,OAAO,IAAI,CAAA;IACb,CAAC;IAOD;;;;;;;;;;;OAWG,CACH,EAAE,CAAC,MAAc,EAAE,KAAqB,EAAA;QACtC,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,MAAM,CAAC,MAAM,EAAE,CAAA,GAAA,EAAM,KAAK,EAAE,CAAC,CAAA;QACnD,OAAO,IAAI,CAAA;IACb,CAAC;IAED;;;;;OAKG,CACH,EAAE,CACA,MAAkB,EAClB,MASC,EAAA;QAED,MAAM,aAAa,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,MAAM,CAAC,CAAC,CAC9C,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE;YACT,uCAAuC;YACvC,+DAA+D;YAC/D,IAAI,OAAO,CAAC,KAAK,QAAQ,IAAI,IAAI,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,OAAO,CAAA,CAAA,EAAI,CAAC,CAAA,CAAA,CAAG,CAAA;iBACpE,OAAO,GAAG,CAAC,EAAE,CAAA;QACpB,CAAC,CAAC,CACD,IAAI,CAAC,GAAG,CAAC,CAAA;QACZ,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,MAAM,CAAC,MAAM,EAAE,CAAA,IAAA,EAAO,aAAa,CAAA,CAAA,CAAG,CAAC,CAAA;QAC7D,OAAO,IAAI,CAAA;IACb,CAAC;IAOD;;;;;;OAMG,CACH,QAAQ,CAAC,MAAc,EAAE,KAA4D,EAAA;QACnF,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;YAC7B,sEAAsE;YACtE,qCAAqC;YACrC,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,MAAM,CAAC,MAAM,EAAE,CAAA,GAAA,EAAM,KAAK,EAAE,CAAC,CAAA;SACpD,MAAM,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;YAC/B,QAAQ;YACR,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,MAAM,CAAC,MAAM,EAAE,CAAA,IAAA,EAAO,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA,CAAA,CAAG,CAAC,CAAA;SAChE,MAAM;YACL,OAAO;YACP,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,MAAM,CAAC,MAAM,EAAE,CAAA,GAAA,EAAM,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE,CAAC,CAAA;SACpE;QACD,OAAO,IAAI,CAAA;IACb,CAAC;IAOD;;;;;;OAMG,CACH,WAAW,CAAC,MAAc,EAAE,KAA4D,EAAA;QACtF,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;YAC7B,QAAQ;YACR,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,MAAM,CAAC,MAAM,EAAE,CAAA,GAAA,EAAM,KAAK,EAAE,CAAC,CAAA;SACpD,MAAM,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;YAC/B,QAAQ;YACR,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,MAAM,CAAC,MAAM,EAAE,CAAA,IAAA,EAAO,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA,CAAA,CAAG,CAAC,CAAA;SAChE,MAAM;YACL,OAAO;YACP,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,MAAM,CAAC,MAAM,EAAE,CAAA,GAAA,EAAM,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE,CAAC,CAAA;SACpE;QACD,OAAO,IAAI,CAAA;IACb,CAAC;IAID;;;;;;OAMG,CACH,OAAO,CAAC,MAAc,EAAE,KAAa,EAAA;QACnC,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,MAAM,CAAC,MAAM,EAAE,CAAA,GAAA,EAAM,KAAK,EAAE,CAAC,CAAA;QACnD,OAAO,IAAI,CAAA;IACb,CAAC;IAID;;;;;;;OAOG,CACH,QAAQ,CAAC,MAAc,EAAE,KAAa,EAAA;QACpC,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,MAAM,CAAC,MAAM,EAAE,CAAA,IAAA,EAAO,KAAK,EAAE,CAAC,CAAA;QACpD,OAAO,IAAI,CAAA;IACb,CAAC;IAID;;;;;;OAMG,CACH,OAAO,CAAC,MAAc,EAAE,KAAa,EAAA;QACnC,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,MAAM,CAAC,MAAM,EAAE,CAAA,GAAA,EAAM,KAAK,EAAE,CAAC,CAAA;QACnD,OAAO,IAAI,CAAA;IACb,CAAC;IAID;;;;;;;OAOG,CACH,QAAQ,CAAC,MAAc,EAAE,KAAa,EAAA;QACpC,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,MAAM,CAAC,MAAM,EAAE,CAAA,IAAA,EAAO,KAAK,EAAE,CAAC,CAAA;QACpD,OAAO,IAAI,CAAA;IACb,CAAC;IAID;;;;;;;OAOG,CACH,aAAa,CAAC,MAAc,EAAE,KAAa,EAAA;QACzC,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,MAAM,CAAC,MAAM,EAAE,CAAA,IAAA,EAAO,KAAK,EAAE,CAAC,CAAA;QACpD,OAAO,IAAI,CAAA;IACb,CAAC;IAOD;;;;;;OAMG,CACH,QAAQ,CAAC,MAAc,EAAE,KAAkC,EAAA;QACzD,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;YAC7B,QAAQ;YACR,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,MAAM,CAAC,MAAM,EAAE,CAAA,GAAA,EAAM,KAAK,EAAE,CAAC,CAAA;SACpD,MAAM;YACL,QAAQ;YACR,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,MAAM,CAAC,MAAM,EAAE,CAAA,IAAA,EAAO,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA,CAAA,CAAG,CAAC,CAAA;SAChE;QACD,OAAO,IAAI,CAAA;IACb,CAAC;IAYD;;;;;;;;;OASG,CACH,UAAU,CACR,MAAc,EACd,KAAa,EACb,EAAE,MAAM,EAAE,IAAI,EAAA,GAAmE,CAAA,CAAE,EAAA;QAEnF,IAAI,QAAQ,GAAG,EAAE,CAAA;QACjB,IAAI,IAAI,KAAK,OAAO,EAAE;YACpB,QAAQ,GAAG,IAAI,CAAA;SAChB,MAAM,IAAI,IAAI,KAAK,QAAQ,EAAE;YAC5B,QAAQ,GAAG,IAAI,CAAA;SAChB,MAAM,IAAI,IAAI,KAAK,WAAW,EAAE;YAC/B,QAAQ,GAAG,GAAG,CAAA;SACf;QACD,MAAM,UAAU,GAAG,MAAM,KAAK,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAA,CAAA,EAAI,MAAM,CAAA,CAAA,CAAG,CAAA;QAC5D,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,MAAM,CAAC,MAAM,EAAE,GAAG,QAAQ,CAAA,GAAA,EAAM,UAAU,CAAA,CAAA,EAAI,KAAK,EAAE,CAAC,CAAA;QAC5E,OAAO,IAAI,CAAA;IACb,CAAC;IAID;;;;;;OAMG,CACH,KAAK,CAAC,KAA8B,EAAA;QAClC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,EAAE,KAAK,CAAC,EAAE,EAAE;YAChD,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,MAAM,CAAC,MAAM,EAAE,CAAA,GAAA,EAAM,KAAK,EAAE,CAAC,CAAA;QACrD,CAAC,CAAC,CAAA;QACF,OAAO,IAAI,CAAA;IACb,CAAC;IAQD;;;;;;;;;;;;OAYG,CACH,GAAG,CAAC,MAAc,EAAE,QAAgB,EAAE,KAAc,EAAA;QAClD,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,MAAM,CAAC,MAAM,EAAE,CAAA,IAAA,EAAO,QAAQ,CAAA,CAAA,EAAI,KAAK,EAAE,CAAC,CAAA;QAChE,OAAO,IAAI,CAAA;IACb,CAAC;IAED;;;;;;;;;;;;;;OAcG,CACH,EAAE,CACA,OAAe,EACf,EACE,YAAY,EACZ,eAAe,GAAG,YAAY,EAAA,GACyB,CAAA,CAAE,EAAA;QAE3D,MAAM,GAAG,GAAG,eAAe,CAAC,CAAC,CAAC,GAAG,eAAe,CAAA,GAAA,CAAK,CAAC,CAAC,CAAC,IAAI,CAAA;QAC5D,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG,EAAE,CAAA,CAAA,EAAI,OAAO,CAAA,CAAA,CAAG,CAAC,CAAA;QACjD,OAAO,IAAI,CAAA;IACb,CAAC;IAQD;;;;;;;;;;;;OAYG,CACH,MAAM,CAAC,MAAc,EAAE,QAAgB,EAAE,KAAc,EAAA;QACrD,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,MAAM,CAAC,MAAM,EAAE,GAAG,QAAQ,CAAA,CAAA,EAAI,KAAK,EAAE,CAAC,CAAA;QAC5D,OAAO,IAAI,CAAA;IACb,CAAC;CACF;AAxgBD,QAAA,OAAA,GAAA,uBAwgBC"}}, {"offset": {"line": 1048, "column": 0}, "map": {"version": 3, "file": "PostgrestQueryBuilder.js", "sources": ["../../src/PostgrestQueryBuilder.ts"], "sourceRoot": "", "names": [], "mappings": ";;;;;;;;;AACA,MAAA,2BAAA,qDAA6D;AAI7D,MAAqB,qBAAqB;IAYxC,YACE,GAAQ,EACR,EACE,OAAO,GAAG,CAAA,CAAE,EACZ,MAAM,EACN,KAAK,EAKN,CAAA;QAED,IAAI,CAAC,GAAG,GAAG,GAAG,CAAA;QACd,IAAI,CAAC,OAAO,GAAG,OAAO,CAAA;QACtB,IAAI,CAAC,MAAM,GAAG,MAAM,CAAA;QACpB,IAAI,CAAC,KAAK,GAAG,KAAK,CAAA;IACpB,CAAC;IAED;;;;;;;;;;;;;;;;;;;;OAoBG,CACH,MAAM,CAIJ,OAAe,EACf,EACE,IAAI,GAAG,KAAK,EACZ,KAAK,EAAA,GAIH,CAAA,CAAE,EAAA;QAEN,MAAM,MAAM,GAAG,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAA;QACpC,wCAAwC;QACxC,IAAI,MAAM,GAAG,KAAK,CAAA;QAClB,MAAM,cAAc,GAAG,CAAC,OAAO,KAAA,QAAP,OAAO,KAAA,KAAA,IAAP,OAAO,GAAI,GAAG,CAAC,CACpC,KAAK,CAAC,EAAE,CAAC,CACT,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE;YACT,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,EAAE;gBAC3B,OAAO,EAAE,CAAA;aACV;YACD,IAAI,CAAC,KAAK,GAAG,EAAE;gBACb,MAAM,GAAG,CAAC,MAAM,CAAA;aACjB;YACD,OAAO,CAAC,CAAA;QACV,CAAC,CAAC,CACD,IAAI,CAAC,EAAE,CAAC,CAAA;QACX,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,QAAQ,EAAE,cAAc,CAAC,CAAA;QACnD,IAAI,KAAK,EAAE;YACT,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAA,MAAA,EAAS,KAAK,EAAE,CAAA;SAC1C;QAED,OAAO,IAAI,yBAAA,OAAsB,CAAC;YAChC,MAAM;YACN,GAAG,EAAE,IAAI,CAAC,GAAG;YACb,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,UAAU,EAAE,KAAK;SAC0B,CAAC,CAAA;IAChD,CAAC;IAgBD;;;;;;;;;;;;;;;;;;;;;;;;;OAyBG,CACH,MAAM,CACJ,MAAmB,EACnB,EACE,KAAK,EACL,aAAa,GAAG,IAAI,EAAA,GAIlB,CAAA,CAAE,EAAA;QAEN,MAAM,MAAM,GAAG,MAAM,CAAA;QAErB,MAAM,cAAc,GAAG,EAAE,CAAA;QACzB,IAAI,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;YAC1B,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAA;SAC5C;QACD,IAAI,KAAK,EAAE;YACT,cAAc,CAAC,IAAI,CAAC,CAAA,MAAA,EAAS,KAAK,EAAE,CAAC,CAAA;SACtC;QACD,IAAI,CAAC,aAAa,EAAE;YAClB,cAAc,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAA;SACvC;QACD,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,cAAc,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;QAEjD,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;YACzB,MAAM,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,CAAG,CAAD,EAAI,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,EAAc,CAAC,CAAA;YACrF,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE;gBACtB,MAAM,aAAa,GAAG,CAAC;uBAAG,IAAI,GAAG,CAAC,OAAO,CAAC;iBAAC,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,CAAG,CAAD,AAAC,CAAA,EAAI,MAAM,CAAA,CAAA,CAAG,CAAC,CAAA;gBAC1E,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,SAAS,EAAE,aAAa,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAA;aAC9D;SACF;QAED,OAAO,IAAI,yBAAA,OAAsB,CAAC;YAChC,MAAM;YACN,GAAG,EAAE,IAAI,CAAC,GAAG;YACb,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,IAAI,EAAE,MAAM;YACZ,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,UAAU,EAAE,KAAK;SACmB,CAAC,CAAA;IACzC,CAAC;IAoBD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAqCG,CACH,MAAM,CACJ,MAAmB,EACnB,EACE,UAAU,EACV,gBAAgB,GAAG,KAAK,EACxB,KAAK,EACL,aAAa,GAAG,IAAI,EAAA,GAMlB,CAAA,CAAE,EAAA;QAEN,MAAM,MAAM,GAAG,MAAM,CAAA;QAErB,MAAM,cAAc,GAAG;YAAC,CAAA,WAAA,EAAc,gBAAgB,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,OAAO,CAAA,WAAA,CAAa;SAAC,CAAA;QAEzF,IAAI,UAAU,KAAK,SAAS,EAAE,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,aAAa,EAAE,UAAU,CAAC,CAAA;QAClF,IAAI,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;YAC1B,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAA;SAC5C;QACD,IAAI,KAAK,EAAE;YACT,cAAc,CAAC,IAAI,CAAC,CAAA,MAAA,EAAS,KAAK,EAAE,CAAC,CAAA;SACtC;QACD,IAAI,CAAC,aAAa,EAAE;YAClB,cAAc,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAA;SACvC;QACD,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,cAAc,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;QAEjD,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;YACzB,MAAM,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,CAAG,CAAD,EAAI,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,EAAc,CAAC,CAAA;YACrF,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE;gBACtB,MAAM,aAAa,GAAG,CAAC;uBAAG,IAAI,GAAG,CAAC,OAAO,CAAC;iBAAC,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,CAAG,CAAD,AAAC,CAAA,EAAI,MAAM,CAAA,CAAA,CAAG,CAAC,CAAA;gBAC1E,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,SAAS,EAAE,aAAa,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAA;aAC9D;SACF;QAED,OAAO,IAAI,yBAAA,OAAsB,CAAC;YAChC,MAAM;YACN,GAAG,EAAE,IAAI,CAAC,GAAG;YACb,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,IAAI,EAAE,MAAM;YACZ,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,UAAU,EAAE,KAAK;SACmB,CAAC,CAAA;IACzC,CAAC;IAED;;;;;;;;;;;;;;;;;;;;OAoBG,CACH,MAAM,CACJ,MAAW,EACX,EACE,KAAK,EAAA,GAGH,CAAA,CAAE,EAAA;QAEN,MAAM,MAAM,GAAG,OAAO,CAAA;QACtB,MAAM,cAAc,GAAG,EAAE,CAAA;QACzB,IAAI,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;YAC1B,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAA;SAC5C;QACD,IAAI,KAAK,EAAE;YACT,cAAc,CAAC,IAAI,CAAC,CAAA,MAAA,EAAS,KAAK,EAAE,CAAC,CAAA;SACtC;QACD,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,cAAc,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;QAEjD,OAAO,IAAI,yBAAA,OAAsB,CAAC;YAChC,MAAM;YACN,GAAG,EAAE,IAAI,CAAC,GAAG;YACb,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,IAAI,EAAE,MAAM;YACZ,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,UAAU,EAAE,KAAK;SACmB,CAAC,CAAA;IACzC,CAAC;IAED;;;;;;;;;;;;;;;;;;OAkBG,CACH,MAAM,CAAC,EACL,KAAK,EAAA,GAGH,CAAA,CAAE,EAAA;QACJ,MAAM,MAAM,GAAG,QAAQ,CAAA;QACvB,MAAM,cAAc,GAAG,EAAE,CAAA;QACzB,IAAI,KAAK,EAAE;YACT,cAAc,CAAC,IAAI,CAAC,CAAA,MAAA,EAAS,KAAK,EAAE,CAAC,CAAA;SACtC;QACD,IAAI,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;YAC1B,cAAc,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAA;SAC/C;QACD,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,cAAc,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;QAEjD,OAAO,IAAI,yBAAA,OAAsB,CAAC;YAChC,MAAM;YACN,GAAG,EAAE,IAAI,CAAC,GAAG;YACb,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,UAAU,EAAE,KAAK;SACmB,CAAC,CAAA;IACzC,CAAC;CACF;AAvXD,QAAA,OAAA,GAAA,sBAuXC"}}, {"offset": {"line": 1324, "column": 0}, "map": {"version": 3, "file": "version.js", "sources": ["../../src/version.ts"], "sourceRoot": "", "names": [], "mappings": ";;;;;AAAa,QAAA,OAAO,GAAG,iBAAiB,CAAA"}}, {"offset": {"line": 1335, "column": 0}, "map": {"version": 3, "file": "constants.js", "sources": ["../../src/constants.ts"], "sourceRoot": "", "names": [], "mappings": ";;;;;AAAA,MAAA,iCAAmC;AACtB,QAAA,eAAe,GAAG;IAAE,eAAe,EAAE,CAAA,aAAA,EAAgB,UAAA,OAAO,EAAE;AAAA,CAAE,CAAA"}}, {"offset": {"line": 1349, "column": 0}, "map": {"version": 3, "file": "PostgrestClient.js", "sources": ["../../src/PostgrestClient.ts"], "sourceRoot": "", "names": [], "mappings": ";;;;;;;;;AAAA,MAAA,0BAAA,oDAA2D;AAC3D,MAAA,2BAAA,qDAA6D;AAE7D,MAAA,qCAA6C;AAG7C;;;;;;;;;GASG,CACH,MAAqB,eAAe;IAclC,mEAAmE;IACnE;;;;;;;;OAQG,CACH,YACE,GAAW,EACX,EACE,OAAO,GAAG,CAAA,CAAE,EACZ,MAAM,EACN,KAAK,EAAA,GAKH,CAAA,CAAE,CAAA;QAEN,IAAI,CAAC,GAAG,GAAG,GAAG,CAAA;QACd,IAAI,CAAC,OAAO,GAAA,OAAA,MAAA,CAAA,OAAA,MAAA,CAAA,CAAA,GAAQ,YAAA,eAAe,GAAK,OAAO,CAAE,CAAA;QACjD,IAAI,CAAC,UAAU,GAAG,MAAM,CAAA;QACxB,IAAI,CAAC,KAAK,GAAG,KAAK,CAAA;IACpB,CAAC;IASD;;;;OAIG,CACH,IAAI,CAAC,QAAgB,EAAA;QACnB,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAA,CAAA,EAAI,QAAQ,EAAE,CAAC,CAAA;QAC9C,OAAO,IAAI,wBAAA,OAAqB,CAAC,GAAG,EAAE;YACpC,OAAO,EAAA,OAAA,MAAA,CAAA,CAAA,GAAO,IAAI,CAAC,OAAO,CAAE;YAC5B,MAAM,EAAE,IAAI,CAAC,UAAU;YACvB,KAAK,EAAE,IAAI,CAAC,KAAK;SAClB,CAAC,CAAA;IACJ,CAAC;IAED;;;;;;OAMG,CACH,MAAM,CACJ,MAAqB,EAAA;QAMrB,OAAO,IAAI,eAAe,CAAC,IAAI,CAAC,GAAG,EAAE;YACnC,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,MAAM;YACN,KAAK,EAAE,IAAI,CAAC,KAAK;SAClB,CAAC,CAAA;IACJ,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;OAsBG,CACH,GAAG,CACD,EAAU,EACV,OAAmB,CAAA,CAAE,EACrB,EACE,IAAI,GAAG,KAAK,EACZ,GAAG,GAAG,KAAK,EACX,KAAK,EAAA,GAKH,CAAA,CAAE,EAAA;QAYN,IAAI,MAA+B,CAAA;QACnC,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAA,KAAA,EAAQ,EAAE,EAAE,CAAC,CAAA;QAC5C,IAAI,IAAyB,CAAA;QAC7B,IAAI,IAAI,IAAI,GAAG,EAAE;YACf,MAAM,GAAG,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAA;YAC9B,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,AAClB,wEAAwE;YACxE,gCAAgC;aAC/B,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,EAAE,CAAG,CAAD,IAAM,KAAK,SAAS,CAAC,AAC5C,mCAAmC;aAClC,GAAG,CAAC,CAAC,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,CAAG,CAAD;oBAAE,IAAI;oBAAE,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAA,CAAA,EAAI,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA,CAAA,CAAG,CAAC,CAAC,CAAC,GAAG,KAAK,EAAE;iBAAC,CAAC,CAC1F,OAAO,CAAC,CAAC,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,EAAE;gBACzB,GAAG,CAAC,YAAY,CAAC,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,CAAA;YACtC,CAAC,CAAC,CAAA;SACL,MAAM;YACL,MAAM,GAAG,MAAM,CAAA;YACf,IAAI,GAAG,IAAI,CAAA;SACZ;QAED,MAAM,OAAO,GAAA,OAAA,MAAA,CAAA,CAAA,GAAQ,IAAI,CAAC,OAAO,CAAE,CAAA;QACnC,IAAI,KAAK,EAAE;YACT,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAA,MAAA,EAAS,KAAK,EAAE,CAAA;SACrC;QAED,OAAO,IAAI,yBAAA,OAAsB,CAAC;YAChC,MAAM;YACN,GAAG;YACH,OAAO;YACP,MAAM,EAAE,IAAI,CAAC,UAAU;YACvB,IAAI;YACJ,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,UAAU,EAAE,KAAK;SAC4B,CAAC,CAAA;IAClD,CAAC;CACF;AApKD,QAAA,OAAA,GAAA,gBAoKC"}}, {"offset": {"line": 1473, "column": 0}, "map": {"version": 3, "file": "index.js", "sources": ["../../src/index.ts"], "sourceRoot": "", "names": [], "mappings": ";;;;;;;;;;AAAA,qDAAqD;AACrD,MAAA,oBAAA,8CAA+C;AAQ7C,QAAA,eAAA,GARK,kBAAA,OAAe,CAQL;AAPjB,MAAA,0BAAA,oDAA2D;AAQzD,QAAA,qBAAA,GARK,wBAAA,OAAqB,CAQL;AAPvB,MAAA,2BAAA,qDAA6D;AAQ3D,QAAA,sBAAA,GARK,yBAAA,OAAsB,CAQL;AAPxB,MAAA,8BAAA,wDAAmE;AAQjE,QAAA,yBAAA,GARK,4BAAA,OAAyB,CAQL;AAP3B,MAAA,qBAAA,+CAAiD;AAQ/C,QAAA,gBAAA,GARK,mBAAA,OAAgB,CAQL;AAPlB,MAAA,mBAAA,6CAA6C;AAQ3C,QAAA,cAAA,GARK,iBAAA,OAAc,CAQL;AAEhB,QAAA,OAAA,GAAe;IACb,eAAe,EAAf,kBAAA,OAAe;IACf,qBAAqB,EAArB,wBAAA,OAAqB;IACrB,sBAAsB,EAAtB,yBAAA,OAAsB;IACtB,yBAAyB,EAAzB,4BAAA,OAAyB;IACzB,gBAAgB,EAAhB,mBAAA,OAAgB;IAChB,cAAc,EAAd,iBAAA,OAAc;CACf,CAAA"}}, {"offset": {"line": 1510, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/@supabase/postgrest-js/dist/esm/wrapper.mjs"], "sourcesContent": ["import index from '../cjs/index.js'\nconst {\n  PostgrestClient,\n  PostgrestQueryBuilder,\n  PostgrestFilterBuilder,\n  PostgrestTransformBuilder,\n  PostgrestBuilder,\n  PostgrestError,\n} = index\n\nexport {\n  PostgrestBuilder,\n  PostgrestClient,\n  PostgrestFilterBuilder,\n  PostgrestQueryBuilder,\n  PostgrestTransformBuilder,\n  PostgrestError,\n}\n\n// compatibility with CJS output\nexport default {\n  PostgrestClient,\n  PostgrestQueryBuilder,\n  PostgrestFilterBuilder,\n  PostgrestTransformBuilder,\n  PostgrestBuilder,\n  PostgrestError,\n}\n"], "names": [], "mappings": ";;;;;;;;;AAAA;;AACA,MAAM,EACJ,eAAe,EACf,qBAAqB,EACrB,sBAAsB,EACtB,yBAAyB,EACzB,gBAAgB,EAChB,cAAc,EACf,GAAG,6KAAA,CAAA,UAAK;;uCAYM;IACb;IACA;IACA;IACA;IACA;IACA;AACF", "ignoreList": [0]}}, {"offset": {"line": 1536, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/ws/browser.js"], "sourcesContent": ["'use strict';\n\nmodule.exports = function () {\n  throw new Error(\n    'ws does not work in the browser. Browser clients must use the native ' +\n      'WebSocket object'\n  );\n};\n"], "names": [], "mappings": "AAAA;AAEA,OAAO,OAAO,GAAG;IACf,MAAM,IAAI,MACR,0EACE;AAEN", "ignoreList": [0]}}, {"offset": {"line": 1546, "column": 0}, "map": {"version": 3, "file": "WebSocket.js", "sources": ["../../src/WebSocket.ts"], "sourceRoot": "", "names": [], "mappings": "AAAA,gCAAgC;;;;AAEhC,IAAI,aAAkB,CAAA;AAEtB,IAAI,OAAO,MAAM,KAAK,WAAW,OAAE,CAAC;IAClC,sBAAsB;IACtB,8DAA8D;IAC9D,aAAa,GAAG,OAAO,CAAC,IAAI,CAAC,CAAA;AAC/B,CAAC,MAAM,CAAC;;AAGR,CAAC;uCAEc,aAAa,CAAA"}}, {"offset": {"line": 1566, "column": 0}, "map": {"version": 3, "file": "version.js", "sources": ["../../../src/lib/version.ts"], "sourceRoot": "", "names": [], "mappings": ";;;AAAO,MAAM,OAAO,GAAG,iBAAiB,CAAA"}}, {"offset": {"line": 1576, "column": 0}, "map": {"version": 3, "file": "constants.js", "sources": ["../../../src/lib/constants.ts"], "sourceRoot": "", "names": [], "mappings": ";;;;;;;;;;;;AAAA,OAAO,EAAE,OAAO,EAAE,MAAM,WAAW,CAAA;;AAE5B,MAAM,eAAe,GAAG;IAAE,eAAe,EAAE,CAAA,YAAA,2LAAe,UAAO,EAAE;AAAA,CAAE,CAAA;AAErE,MAAM,GAAG,GAAW,OAAO,CAAA;AAE3B,MAAM,OAAO,4LAAG,UAAO,CAAA;AAEvB,MAAM,eAAe,GAAG,KAAK,CAAA;AAE7B,MAAM,eAAe,GAAG,IAAI,CAAA;AAEnC,IAAY,aAKX;AALD,CAAA,SAAY,aAAa;IACvB,aAAA,CAAA,aAAA,CAAA,aAAA,GAAA,EAAA,GAAA,YAAc,CAAA;IACd,aAAA,CAAA,aAAA,CAAA,OAAA,GAAA,EAAA,GAAA,MAAQ,CAAA;IACR,aAAA,CAAA,aAAA,CAAA,UAAA,GAAA,EAAA,GAAA,SAAW,CAAA;IACX,aAAA,CAAA,aAAA,CAAA,SAAA,GAAA,EAAA,GAAA,QAAU,CAAA;AACZ,CAAC,EALW,aAAa,IAAA,CAAb,aAAa,GAAA,CAAA,CAAA,GAKxB;AAED,IAAY,cAMX;AAND,CAAA,SAAY,cAAc;IACxB,cAAA,CAAA,SAAA,GAAA,QAAiB,CAAA;IACjB,cAAA,CAAA,UAAA,GAAA,SAAmB,CAAA;IACnB,cAAA,CAAA,SAAA,GAAA,QAAiB,CAAA;IACjB,cAAA,CAAA,UAAA,GAAA,SAAmB,CAAA;IACnB,cAAA,CAAA,UAAA,GAAA,SAAmB,CAAA;AACrB,CAAC,EANW,cAAc,IAAA,CAAd,cAAc,GAAA,CAAA,CAAA,GAMzB;AAED,IAAY,cAOX;AAPD,CAAA,SAAY,cAAc;IACxB,cAAA,CAAA,QAAA,GAAA,WAAmB,CAAA;IACnB,cAAA,CAAA,QAAA,GAAA,WAAmB,CAAA;IACnB,cAAA,CAAA,OAAA,GAAA,UAAiB,CAAA;IACjB,cAAA,CAAA,QAAA,GAAA,WAAmB,CAAA;IACnB,cAAA,CAAA,QAAA,GAAA,WAAmB,CAAA;IACnB,cAAA,CAAA,eAAA,GAAA,cAA6B,CAAA;AAC/B,CAAC,EAPW,cAAc,IAAA,CAAd,cAAc,GAAA,CAAA,CAAA,GAOzB;AAED,IAAY,UAEX;AAFD,CAAA,SAAY,UAAU;IACpB,UAAA,CAAA,YAAA,GAAA,WAAuB,CAAA;AACzB,CAAC,EAFW,UAAU,IAAA,CAAV,UAAU,GAAA,CAAA,CAAA,GAErB;AAED,IAAY,gBAKX;AALD,CAAA,SAAY,gBAAgB;IAC1B,gBAAA,CAAA,aAAA,GAAA,YAAyB,CAAA;IACzB,gBAAA,CAAA,OAAA,GAAA,MAAa,CAAA;IACb,gBAAA,CAAA,UAAA,GAAA,SAAmB,CAAA;IACnB,gBAAA,CAAA,SAAA,GAAA,QAAiB,CAAA;AACnB,CAAC,EALW,gBAAgB,IAAA,CAAhB,gBAAgB,GAAA,CAAA,CAAA,GAK3B"}}, {"offset": {"line": 1638, "column": 0}, "map": {"version": 3, "file": "serializer.js", "sources": ["../../../src/lib/serializer.ts"], "sourceRoot": "", "names": [], "mappings": "AAAA,2HAA2H;AAC3H,8EAA8E;;;;AAEhE,MAAO,UAAU;IAA/B,aAAA;QACE,IAAA,CAAA,aAAa,GAAG,CAAC,CAAA;IA4CnB,CAAC;IA1CC,MAAM,CAAC,UAAgC,EAAE,QAAkB,EAAA;QACzD,IAAI,UAAU,CAAC,WAAW,KAAK,WAAW,EAAE,CAAC;YAC3C,OAAO,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC,CAAA;QACjD,CAAC;QAED,IAAI,OAAO,UAAU,KAAK,QAAQ,EAAE,CAAC;YACnC,OAAO,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,CAAA;QACzC,CAAC;QAED,OAAO,QAAQ,CAAC,CAAA,CAAE,CAAC,CAAA;IACrB,CAAC;IAEO,aAAa,CAAC,MAAmB,EAAA;QACvC,MAAM,IAAI,GAAG,IAAI,QAAQ,CAAC,MAAM,CAAC,CAAA;QACjC,MAAM,OAAO,GAAG,IAAI,WAAW,EAAE,CAAA;QAEjC,OAAO,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,IAAI,EAAE,OAAO,CAAC,CAAA;IACrD,CAAC;IAEO,gBAAgB,CACtB,MAAmB,EACnB,IAAc,EACd,OAAoB,EAAA;QAOpB,MAAM,SAAS,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAA;QAClC,MAAM,SAAS,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAA;QAClC,IAAI,MAAM,GAAG,IAAI,CAAC,aAAa,GAAG,CAAC,CAAA;QACnC,MAAM,KAAK,GAAG,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,EAAE,MAAM,GAAG,SAAS,CAAC,CAAC,CAAA;QACtE,MAAM,GAAG,MAAM,GAAG,SAAS,CAAA;QAC3B,MAAM,KAAK,GAAG,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,EAAE,MAAM,GAAG,SAAS,CAAC,CAAC,CAAA;QACtE,MAAM,GAAG,MAAM,GAAG,SAAS,CAAA;QAC3B,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CACrB,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,EAAE,MAAM,CAAC,UAAU,CAAC,CAAC,CACxD,CAAA;QAED,OAAO;YAAE,GAAG,EAAE,IAAI;YAAE,KAAK,EAAE,KAAK;YAAE,KAAK,EAAE,KAAK;YAAE,OAAO,EAAE,IAAI;QAAA,CAAE,CAAA;IACjE,CAAC;CACF"}}, {"offset": {"line": 1684, "column": 0}, "map": {"version": 3, "file": "timer.js", "sources": ["../../../src/lib/timer.ts"], "sourceRoot": "", "names": [], "mappings": "AAAA;;;;;;;;;;;GAWG;;;AACW,MAAO,KAAK;IAIxB,YAAmB,QAAkB,EAAS,SAAmB,CAAA;QAA9C,IAAA,CAAA,QAAQ,GAAR,QAAQ,CAAU;QAAS,IAAA,CAAA,SAAS,GAAT,SAAS,CAAU;QAHjE,IAAA,CAAA,KAAK,GAAuB,SAAS,CAAA;QACrC,IAAA,CAAA,KAAK,GAAW,CAAC,CAAA;QAGf,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAA;QACxB,IAAI,CAAC,SAAS,GAAG,SAAS,CAAA;IAC5B,CAAC;IAED,KAAK,GAAA;QACH,IAAI,CAAC,KAAK,GAAG,CAAC,CAAA;QACd,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;IAC1B,CAAC;IAED,8DAA8D;IAC9D,eAAe,GAAA;QACb,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;QAExB,IAAI,CAAC,KAAK,GAAQ,UAAU,CAAC,GAAG,EAAE;YAChC,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,GAAG,CAAC,CAAA;YAC3B,IAAI,CAAC,QAAQ,EAAE,CAAA;QACjB,CAAC,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAA;IACpC,CAAC;CACF"}}, {"offset": {"line": 1726, "column": 0}, "map": {"version": 3, "file": "transformers.js", "sources": ["../../../src/lib/transformers.ts"], "sourceRoot": "", "names": [], "mappings": "AAAA;;GAEG,CAEH,0EAA0E;AAC1E,yFAAyF;;;;;;;;;;;;;AAEzF,IAAY,aAyBX;AAzBD,CAAA,SAAY,aAAa;IACvB,aAAA,CAAA,UAAA,GAAA,SAAmB,CAAA;IACnB,aAAA,CAAA,OAAA,GAAA,MAAa,CAAA;IACb,aAAA,CAAA,OAAA,GAAA,MAAa,CAAA;IACb,aAAA,CAAA,YAAA,GAAA,WAAuB,CAAA;IACvB,aAAA,CAAA,SAAA,GAAA,QAAiB,CAAA;IACjB,aAAA,CAAA,SAAA,GAAA,QAAiB,CAAA;IACjB,aAAA,CAAA,OAAA,GAAA,MAAa,CAAA;IACb,aAAA,CAAA,OAAA,GAAA,MAAa,CAAA;IACb,aAAA,CAAA,YAAA,GAAA,WAAuB,CAAA;IACvB,aAAA,CAAA,OAAA,GAAA,MAAa,CAAA;IACb,aAAA,CAAA,YAAA,GAAA,WAAuB,CAAA;IACvB,aAAA,CAAA,OAAA,GAAA,MAAa,CAAA;IACb,aAAA,CAAA,QAAA,GAAA,OAAe,CAAA;IACf,aAAA,CAAA,QAAA,GAAA,OAAe,CAAA;IACf,aAAA,CAAA,UAAA,GAAA,SAAmB,CAAA;IACnB,aAAA,CAAA,MAAA,GAAA,KAAW,CAAA;IACX,aAAA,CAAA,UAAA,GAAA,SAAmB,CAAA;IACnB,aAAA,CAAA,OAAA,GAAA,MAAa,CAAA;IACb,aAAA,CAAA,OAAA,GAAA,MAAa,CAAA;IACb,aAAA,CAAA,YAAA,GAAA,WAAuB,CAAA;IACvB,aAAA,CAAA,cAAA,GAAA,aAA2B,CAAA;IAC3B,aAAA,CAAA,SAAA,GAAA,QAAiB,CAAA;IACjB,aAAA,CAAA,UAAA,GAAA,SAAmB,CAAA;IACnB,aAAA,CAAA,YAAA,GAAA,WAAuB,CAAA;AACzB,CAAC,EAzBW,aAAa,IAAA,CAAb,aAAa,GAAA,CAAA,CAAA,GAyBxB;AA4BM,MAAM,iBAAiB,GAAG,CAC/B,OAAgB,EAChB,MAAc,EACd,UAAoC,CAAA,CAAE,EAC9B,EAAE;;IACV,MAAM,SAAS,GAAG,CAAA,KAAA,OAAO,CAAC,SAAS,MAAA,QAAA,OAAA,KAAA,IAAA,KAAI,EAAE,CAAA;IAEzC,OAAO,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,OAAO,EAAE,EAAE;QACjD,GAAG,CAAC,OAAO,CAAC,GAAG,aAAa,CAAC,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,SAAS,CAAC,CAAA;QACjE,OAAO,GAAG,CAAA;IACZ,CAAC,EAAE,CAAA,CAAY,CAAC,CAAA;AAClB,CAAC,CAAA;AAgBM,MAAM,aAAa,GAAG,CAC3B,UAAkB,EAClB,OAAgB,EAChB,MAAc,EACd,SAAmB,EACN,EAAE;IACf,MAAM,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAG,CAAD,AAAE,CAAC,IAAI,KAAK,UAAU,CAAC,CAAA;IACzD,MAAM,OAAO,GAAG,MAAM,KAAA,QAAN,MAAM,KAAA,KAAA,IAAA,KAAA,IAAN,MAAM,CAAE,IAAI,CAAA;IAC5B,MAAM,KAAK,GAAG,MAAM,CAAC,UAAU,CAAC,CAAA;IAEhC,IAAI,OAAO,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;QAC5C,OAAO,WAAW,CAAC,OAAO,EAAE,KAAK,CAAC,CAAA;IACpC,CAAC;IAED,OAAO,IAAI,CAAC,KAAK,CAAC,CAAA;AACpB,CAAC,CAAA;AAeM,MAAM,WAAW,GAAG,CAAC,IAAY,EAAE,KAAkB,EAAe,EAAE;IAC3E,2BAA2B;IAC3B,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE,CAAC;QAC3B,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,CAAA;QAC3C,OAAO,OAAO,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAA;IACjC,CAAC;IAED,wCAAwC;IACxC,OAAQ,IAAI,EAAE,CAAC;QACb,KAAK,aAAa,CAAC,IAAI;YACrB,OAAO,SAAS,CAAC,KAAK,CAAC,CAAA;QACzB,KAAK,aAAa,CAAC,MAAM,CAAC;QAC1B,KAAK,aAAa,CAAC,MAAM,CAAC;QAC1B,KAAK,aAAa,CAAC,IAAI,CAAC;QACxB,KAAK,aAAa,CAAC,IAAI,CAAC;QACxB,KAAK,aAAa,CAAC,IAAI,CAAC;QACxB,KAAK,aAAa,CAAC,OAAO,CAAC;QAC3B,KAAK,aAAa,CAAC,GAAG;YACpB,OAAO,QAAQ,CAAC,KAAK,CAAC,CAAA;QACxB,KAAK,aAAa,CAAC,IAAI,CAAC;QACxB,KAAK,aAAa,CAAC,KAAK;YACtB,OAAO,MAAM,CAAC,KAAK,CAAC,CAAA;QACtB,KAAK,aAAa,CAAC,SAAS;YAC1B,OAAO,iBAAiB,CAAC,KAAK,CAAC,CAAA,CAAC,yCAAyC;QAC3E,KAAK,aAAa,CAAC,OAAO,CAAC,CAAC,8CAA8C;QAC1E,KAAK,aAAa,CAAC,IAAI,CAAC,CAAC,8CAA8C;QACvE,KAAK,aAAa,CAAC,SAAS,CAAC;QAC7B,KAAK,aAAa,CAAC,SAAS,CAAC;QAC7B,KAAK,aAAa,CAAC,SAAS,CAAC;QAC7B,KAAK,aAAa,CAAC,KAAK,CAAC;QACzB,KAAK,aAAa,CAAC,OAAO,CAAC,CAAC,8CAA8C;QAC1E,KAAK,aAAa,CAAC,IAAI,CAAC;QACxB,KAAK,aAAa,CAAC,IAAI,CAAC,CAAC,8CAA8C;QACvE,KAAK,aAAa,CAAC,WAAW,CAAC,CAAC,8CAA8C;QAC9E,KAAK,aAAa,CAAC,MAAM,CAAC,CAAC,8CAA8C;QACzE,KAAK,aAAa,CAAC,OAAO,CAAC;QAC3B,KAAK,aAAa,CAAC,SAAS;YAC1B,OAAO,IAAI,CAAC,KAAK,CAAC,CAAA;QACpB;YACE,uCAAuC;YACvC,OAAO,IAAI,CAAC,KAAK,CAAC,CAAA;IACtB,CAAC;AACH,CAAC,CAAA;AAED,MAAM,IAAI,GAAG,CAAC,KAAkB,EAAe,EAAE;IAC/C,OAAO,KAAK,CAAA;AACd,CAAC,CAAA;AACM,MAAM,SAAS,GAAG,CAAC,KAAkB,EAAe,EAAE;IAC3D,OAAQ,KAAK,EAAE,CAAC;QACd,KAAK,GAAG;YACN,OAAO,IAAI,CAAA;QACb,KAAK,GAAG;YACN,OAAO,KAAK,CAAA;QACd;YACE,OAAO,KAAK,CAAA;IAChB,CAAC;AACH,CAAC,CAAA;AACM,MAAM,QAAQ,GAAG,CAAC,KAAkB,EAAe,EAAE;IAC1D,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;QAC9B,MAAM,WAAW,GAAG,UAAU,CAAC,KAAK,CAAC,CAAA;QACrC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,WAAW,CAAC,EAAE,CAAC;YAC/B,OAAO,WAAW,CAAA;QACpB,CAAC;IACH,CAAC;IACD,OAAO,KAAK,CAAA;AACd,CAAC,CAAA;AACM,MAAM,MAAM,GAAG,CAAC,KAAkB,EAAe,EAAE;IACxD,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;QAC9B,IAAI,CAAC;YACH,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAA;QAC1B,CAAC,CAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,GAAG,CAAC,CAAA,kBAAA,EAAqB,KAAK,EAAE,CAAC,CAAA;YACzC,OAAO,KAAK,CAAA;QACd,CAAC;IACH,CAAC;IACD,OAAO,KAAK,CAAA;AACd,CAAC,CAAA;AAYM,MAAM,OAAO,GAAG,CAAC,KAAkB,EAAE,IAAY,EAAe,EAAE;IACvE,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;QAC9B,OAAO,KAAK,CAAA;IACd,CAAC;IAED,MAAM,OAAO,GAAG,KAAK,CAAC,MAAM,GAAG,CAAC,CAAA;IAChC,MAAM,UAAU,GAAG,KAAK,CAAC,OAAO,CAAC,CAAA;IACjC,MAAM,SAAS,GAAG,KAAK,CAAC,CAAC,CAAC,CAAA;IAE1B,+DAA+D;IAC/D,IAAI,SAAS,KAAK,GAAG,IAAI,UAAU,KAAK,GAAG,EAAE,CAAC;QAC5C,IAAI,GAAG,CAAA;QACP,MAAM,OAAO,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,OAAO,CAAC,CAAA;QAEvC,+DAA+D;QAC/D,IAAI,CAAC;YACH,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,OAAO,GAAG,GAAG,CAAC,CAAA;QACvC,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC;YACX,4DAA4D;YAC5D,GAAG,GAAG,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAA;QACzC,CAAC;QAED,OAAO,GAAG,CAAC,GAAG,CAAC,CAAC,GAAc,EAAE,CAAG,CAAD,UAAY,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC,CAAA;IAC5D,CAAC;IAED,OAAO,KAAK,CAAA;AACd,CAAC,CAAA;AASM,MAAM,iBAAiB,GAAG,CAAC,KAAkB,EAAe,EAAE;IACnE,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;QAC9B,OAAO,KAAK,CAAC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,CAAA;IAChC,CAAC;IAED,OAAO,KAAK,CAAA;AACd,CAAC,CAAA;AAEM,MAAM,eAAe,GAAG,CAAC,SAAiB,EAAU,EAAE;IAC3D,IAAI,GAAG,GAAG,SAAS,CAAA;IACnB,GAAG,GAAG,GAAG,CAAC,OAAO,CAAC,MAAM,EAAE,MAAM,CAAC,CAAA;IACjC,GAAG,GAAG,GAAG,CAAC,OAAO,CAAC,iDAAiD,EAAE,EAAE,CAAC,CAAA;IACxE,OAAO,GAAG,CAAC,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,CAAA;AAChC,CAAC,CAAA"}}, {"offset": {"line": 1901, "column": 0}, "map": {"version": 3, "file": "push.js", "sources": ["../../../src/lib/push.ts"], "sourceRoot": "", "names": [], "mappings": ";;;AAAA,OAAO,EAAE,eAAe,EAAE,MAAM,kBAAkB,CAAA;;AAGpC,MAAO,IAAI;IAcvB;;;;;;;OAOG,CACH,YACS,OAAwB,EACxB,KAAa,EACb,UAAkC,CAAA,CAAE,EACpC,qMAAkB,kBAAe,CAAA;QAHjC,IAAA,CAAA,OAAO,GAAP,OAAO,CAAiB;QACxB,IAAA,CAAA,KAAK,GAAL,KAAK,CAAQ;QACb,IAAA,CAAA,OAAO,GAAP,OAAO,CAA6B;QACpC,IAAA,CAAA,OAAO,GAAP,OAAO,CAA0B;QAzB1C,IAAA,CAAA,IAAI,GAAY,KAAK,CAAA;QACrB,IAAA,CAAA,YAAY,GAAuB,SAAS,CAAA;QAC5C,IAAA,CAAA,GAAG,GAAW,EAAE,CAAA;QAChB,IAAA,CAAA,YAAY,GAGD,IAAI,CAAA;QACf,IAAA,CAAA,QAAQ,GAGF,EAAE,CAAA;QACR,IAAA,CAAA,QAAQ,GAAkB,IAAI,CAAA;IAe3B,CAAC;IAEJ,MAAM,CAAC,OAAe,EAAA;QACpB,IAAI,CAAC,OAAO,GAAG,OAAO,CAAA;QACtB,IAAI,CAAC,eAAe,EAAE,CAAA;QACtB,IAAI,CAAC,GAAG,GAAG,EAAE,CAAA;QACb,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAA;QACpB,IAAI,CAAC,YAAY,GAAG,IAAI,CAAA;QACxB,IAAI,CAAC,IAAI,GAAG,KAAK,CAAA;QACjB,IAAI,CAAC,IAAI,EAAE,CAAA;IACb,CAAC;IAED,IAAI,GAAA;QACF,IAAI,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,EAAE,CAAC;YACjC,OAAM;QACR,CAAC;QACD,IAAI,CAAC,YAAY,EAAE,CAAA;QACnB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAA;QAChB,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC;YACvB,KAAK,EAAE,IAAI,CAAC,OAAO,CAAC,KAAK;YACzB,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,GAAG,EAAE,IAAI,CAAC,GAAG;YACb,QAAQ,EAAE,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE;SAClC,CAAC,CAAA;IACJ,CAAC;IAED,aAAa,CAAC,OAA+B,EAAA;QAC3C,IAAI,CAAC,OAAO,GAAA,OAAA,MAAA,CAAA,OAAA,MAAA,CAAA,CAAA,GAAQ,IAAI,CAAC,OAAO,GAAK,OAAO,CAAE,CAAA;IAChD,CAAC;IAED,OAAO,CAAC,MAAc,EAAE,QAAkB,EAAA;;QACxC,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,EAAE,CAAC;YAC9B,QAAQ,CAAC,CAAA,KAAA,IAAI,CAAC,YAAY,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAE,QAAQ,CAAC,CAAA;QACvC,CAAC;QAED,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;YAAE,MAAM;YAAE,QAAQ;QAAA,CAAE,CAAC,CAAA;QACxC,OAAO,IAAI,CAAA;IACb,CAAC;IAED,YAAY,GAAA;QACV,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YACtB,OAAM;QACR,CAAC;QACD,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAA;QACzC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;QAEtD,MAAM,QAAQ,GAAG,CAAC,OAAY,EAAE,EAAE;YAChC,IAAI,CAAC,eAAe,EAAE,CAAA;YACtB,IAAI,CAAC,cAAc,EAAE,CAAA;YACrB,IAAI,CAAC,YAAY,GAAG,OAAO,CAAA;YAC3B,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,CAAA;QAC7B,CAAC,CAAA;QAED,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAA,CAAE,EAAE,QAAQ,CAAC,CAAA;QAE7C,IAAI,CAAC,YAAY,GAAQ,UAAU,CAAC,GAAG,EAAE;YACvC,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,CAAA,CAAE,CAAC,CAAA;QAC7B,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC,CAAA;IAClB,CAAC;IAED,OAAO,CAAC,MAAc,EAAE,QAAa,EAAA;QACnC,IAAI,IAAI,CAAC,QAAQ,EACf,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,EAAE;YAAE,MAAM;YAAE,QAAQ;QAAA,CAAE,CAAC,CAAA;IAC9D,CAAC;IAED,OAAO,GAAA;QACL,IAAI,CAAC,eAAe,EAAE,CAAA;QACtB,IAAI,CAAC,cAAc,EAAE,CAAA;IACvB,CAAC;IAEO,eAAe,GAAA;QACrB,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;YACnB,OAAM;QACR,CAAC;QAED,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAA,CAAE,CAAC,CAAA;IACtC,CAAC;IAEO,cAAc,GAAA;QACpB,YAAY,CAAC,IAAI,CAAC,YAAY,CAAC,CAAA;QAC/B,IAAI,CAAC,YAAY,GAAG,SAAS,CAAA;IAC/B,CAAC;IAEO,aAAa,CAAC,EACpB,MAAM,EACN,QAAQ,EAIT,EAAA;QACC,IAAI,CAAC,QAAQ,CACV,MAAM,CAAC,CAAC,CAAC,EAAE,CAAG,CAAD,AAAE,CAAC,MAAM,KAAK,MAAM,CAAC,CAClC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAG,CAAD,AAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAA;IACzC,CAAC;IAEO,YAAY,CAAC,MAAc,EAAA;QACjC,OAAO,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,KAAK,MAAM,CAAA;IACjE,CAAC;CACF"}}, {"offset": {"line": 2013, "column": 0}, "map": {"version": 3, "file": "RealtimePresence.js", "sources": ["../../src/RealtimePresence.ts"], "sourceRoot": "", "names": [], "mappings": "AAAA;;;EAGE;;;;AA+BF,IAAY,+BAIX;AAJD,CAAA,SAAY,+BAA+B;IACzC,+BAAA,CAAA,OAAA,GAAA,MAAa,CAAA;IACb,+BAAA,CAAA,OAAA,GAAA,MAAa,CAAA;IACb,+BAAA,CAAA,QAAA,GAAA,OAAe,CAAA;AACjB,CAAC,EAJW,+BAA+B,IAAA,CAA/B,+BAA+B,GAAA,CAAA,CAAA,GAI1C;AAwBa,MAAO,gBAAgB;IAcnC;;;;;;OAMG,CACH,YAAmB,OAAwB,EAAE,IAAmB,CAAA;QAA7C,IAAA,CAAA,OAAO,GAAP,OAAO,CAAiB;QApB3C,IAAA,CAAA,KAAK,GAA0B,CAAA,CAAE,CAAA;QACjC,IAAA,CAAA,YAAY,GAAsB,EAAE,CAAA;QACpC,IAAA,CAAA,OAAO,GAAkB,IAAI,CAAA;QAC7B,IAAA,CAAA,MAAM,GAIF;YACF,MAAM,EAAE,GAAG,EAAE,AAAE,CAAC;YAChB,OAAO,EAAE,GAAG,EAAE,AAAE,CAAC;YACjB,MAAM,EAAE,GAAG,EAAI,AAAF,CAAG;SACjB,CAAA;QAUC,MAAM,MAAM,GAAG,CAAA,IAAI,KAAA,QAAJ,IAAI,KAAA,KAAA,IAAA,KAAA,IAAJ,IAAI,CAAE,MAAM,KAAI;YAC7B,KAAK,EAAE,gBAAgB;YACvB,IAAI,EAAE,eAAe;SACtB,CAAA;QAED,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,KAAK,EAAE,CAAA,CAAE,EAAE,CAAC,QAA0B,EAAE,EAAE;YAChE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC,MAAM,CAAA;YAE/C,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAA;YAEtC,IAAI,CAAC,KAAK,GAAG,gBAAgB,CAAC,SAAS,CACrC,IAAI,CAAC,KAAK,EACV,QAAQ,EACR,MAAM,EACN,OAAO,CACR,CAAA;YAED,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,EAAE;gBACjC,IAAI,CAAC,KAAK,GAAG,gBAAgB,CAAC,QAAQ,CACpC,IAAI,CAAC,KAAK,EACV,IAAI,EACJ,MAAM,EACN,OAAO,CACR,CAAA;YACH,CAAC,CAAC,CAAA;YAEF,IAAI,CAAC,YAAY,GAAG,EAAE,CAAA;YAEtB,MAAM,EAAE,CAAA;QACV,CAAC,CAAC,CAAA;QAEF,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,EAAE,CAAA,CAAE,EAAE,CAAC,IAAqB,EAAE,EAAE;YAC1D,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC,MAAM,CAAA;YAE/C,IAAI,IAAI,CAAC,kBAAkB,EAAE,EAAE,CAAC;gBAC9B,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;YAC9B,CAAC,MAAM,CAAC;gBACN,IAAI,CAAC,KAAK,GAAG,gBAAgB,CAAC,QAAQ,CACpC,IAAI,CAAC,KAAK,EACV,IAAI,EACJ,MAAM,EACN,OAAO,CACR,CAAA;gBAED,MAAM,EAAE,CAAA;YACV,CAAC;QACH,CAAC,CAAC,CAAA;QAEF,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,gBAAgB,EAAE,YAAY,EAAE,EAAE;YAClD,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,UAAU,EAAE;gBAChC,KAAK,EAAE,MAAM;gBACb,GAAG;gBACH,gBAAgB;gBAChB,YAAY;aACb,CAAC,CAAA;QACJ,CAAC,CAAC,CAAA;QAEF,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,gBAAgB,EAAE,aAAa,EAAE,EAAE;YACpD,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,UAAU,EAAE;gBAChC,KAAK,EAAE,OAAO;gBACd,GAAG;gBACH,gBAAgB;gBAChB,aAAa;aACd,CAAC,CAAA;QACJ,CAAC,CAAC,CAAA;QAEF,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE;YACf,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,UAAU,EAAE;gBAAE,KAAK,EAAE,MAAM;YAAA,CAAE,CAAC,CAAA;QACtD,CAAC,CAAC,CAAA;IACJ,CAAC;IAED;;;;;;;;;OASG,CACK,MAAM,CAAC,SAAS,CACtB,YAAmC,EACnC,QAAkD,EAClD,MAA8B,EAC9B,OAAgC,EAAA;QAEhC,MAAM,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,CAAA;QAC1C,MAAM,gBAAgB,GAAG,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAA;QACtD,MAAM,KAAK,GAA0B,CAAA,CAAE,CAAA;QACvC,MAAM,MAAM,GAA0B,CAAA,CAAE,CAAA;QAExC,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC,GAAW,EAAE,SAAqB,EAAE,EAAE;YACrD,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,EAAE,CAAC;gBAC3B,MAAM,CAAC,GAAG,CAAC,GAAG,SAAS,CAAA;YACzB,CAAC;QACH,CAAC,CAAC,CAAA;QAEF,IAAI,CAAC,GAAG,CAAC,gBAAgB,EAAE,CAAC,GAAG,EAAE,YAAwB,EAAE,EAAE;YAC3D,MAAM,gBAAgB,GAAe,KAAK,CAAC,GAAG,CAAC,CAAA;YAE/C,IAAI,gBAAgB,EAAE,CAAC;gBACrB,MAAM,eAAe,GAAG,YAAY,CAAC,GAAG,CACtC,CAAC,CAAW,EAAE,CAAG,CAAC,AAAF,CAAG,YAAY,CAChC,CAAA;gBACD,MAAM,eAAe,GAAG,gBAAgB,CAAC,GAAG,CAC1C,CAAC,CAAW,EAAE,CAAG,CAAD,AAAE,CAAC,YAAY,CAChC,CAAA;gBACD,MAAM,eAAe,GAAe,YAAY,CAAC,MAAM,CACrD,CAAC,CAAW,EAAE,CAAG,CAAD,cAAgB,CAAC,OAAO,CAAC,CAAC,CAAC,YAAY,CAAC,GAAG,CAAC,CAC7D,CAAA;gBACD,MAAM,aAAa,GAAe,gBAAgB,CAAC,MAAM,CACvD,CAAC,CAAW,EAAE,CAAG,CAAD,cAAgB,CAAC,OAAO,CAAC,CAAC,CAAC,YAAY,CAAC,GAAG,CAAC,CAC7D,CAAA;gBAED,IAAI,eAAe,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oBAC/B,KAAK,CAAC,GAAG,CAAC,GAAG,eAAe,CAAA;gBAC9B,CAAC;gBAED,IAAI,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oBAC7B,MAAM,CAAC,GAAG,CAAC,GAAG,aAAa,CAAA;gBAC7B,CAAC;YACH,CAAC,MAAM,CAAC;gBACN,KAAK,CAAC,GAAG,CAAC,GAAG,YAAY,CAAA;YAC3B,CAAC;QACH,CAAC,CAAC,CAAA;QAEF,OAAO,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE;YAAE,KAAK;YAAE,MAAM;QAAA,CAAE,EAAE,MAAM,EAAE,OAAO,CAAC,CAAA;IACjE,CAAC;IAED;;;;;;;;;OASG,CACK,MAAM,CAAC,QAAQ,CACrB,KAA4B,EAC5B,IAAoC,EACpC,MAA8B,EAC9B,OAAgC,EAAA;QAEhC,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG;YACxB,KAAK,EAAE,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC;YACtC,MAAM,EAAE,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC;SACzC,CAAA;QAED,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,MAAM,GAAG,GAAG,EAAE,AAAE,CAAC,CAAA;QACnB,CAAC;QAED,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,OAAO,GAAG,GAAG,EAAE,AAAE,CAAC,CAAA;QACpB,CAAC;QAED,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC,GAAG,EAAE,YAAwB,EAAE,EAAE;;YAChD,MAAM,gBAAgB,GAAe,CAAA,KAAA,KAAK,CAAC,GAAG,CAAC,MAAA,QAAA,OAAA,KAAA,IAAA,KAAI,EAAE,CAAA;YACrD,KAAK,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,CAAA;YAEzC,IAAI,gBAAgB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAChC,MAAM,kBAAkB,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CACvC,CAAC,CAAW,EAAE,CAAG,CAAD,AAAE,CAAC,YAAY,CAChC,CAAA;gBACD,MAAM,YAAY,GAAe,gBAAgB,CAAC,MAAM,CACtD,CAAC,CAAW,EAAE,CAAG,CAAD,iBAAmB,CAAC,OAAO,CAAC,CAAC,CAAC,YAAY,CAAC,GAAG,CAAC,CAChE,CAAA;gBAED,KAAK,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,GAAG,YAAY,CAAC,CAAA;YACrC,CAAC;YAED,MAAM,CAAC,GAAG,EAAE,gBAAgB,EAAE,YAAY,CAAC,CAAA;QAC7C,CAAC,CAAC,CAAA;QAEF,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,aAAyB,EAAE,EAAE;YAClD,IAAI,gBAAgB,GAAe,KAAK,CAAC,GAAG,CAAC,CAAA;YAE7C,IAAI,CAAC,gBAAgB,EAAE,OAAM;YAE7B,MAAM,oBAAoB,GAAG,aAAa,CAAC,GAAG,CAC5C,CAAC,CAAW,EAAE,CAAG,CAAD,AAAE,CAAC,YAAY,CAChC,CAAA;YACD,gBAAgB,GAAG,gBAAgB,CAAC,MAAM,CACxC,CAAC,CAAW,EAAE,CAAG,CAAD,mBAAqB,CAAC,OAAO,CAAC,CAAC,CAAC,YAAY,CAAC,GAAG,CAAC,CAClE,CAAA;YAED,KAAK,CAAC,GAAG,CAAC,GAAG,gBAAgB,CAAA;YAE7B,OAAO,CAAC,GAAG,EAAE,gBAAgB,EAAE,aAAa,CAAC,CAAA;YAE7C,IAAI,gBAAgB,CAAC,MAAM,KAAK,CAAC,EAAE,OAAO,KAAK,CAAC,GAAG,CAAC,CAAA;QACtD,CAAC,CAAC,CAAA;QAEF,OAAO,KAAK,CAAA;IACd,CAAC;IAED,cAAA,EAAgB,CACR,MAAM,CAAC,GAAG,CAChB,GAA0B,EAC1B,IAAwB,EAAA;QAExB,OAAO,MAAM,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,CAAG,CAAD,GAAK,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAA;IAC1E,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;OAsBG,CACK,MAAM,CAAC,cAAc,CAC3B,KAA+C,EAAA;QAE/C,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAA;QAE7B,OAAO,MAAM,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,CAAC,QAAQ,EAAE,GAAG,EAAE,EAAE;YAChE,MAAM,SAAS,GAAG,KAAK,CAAC,GAAG,CAAC,CAAA;YAE5B,IAAI,OAAO,IAAI,SAAS,EAAE,CAAC;gBACzB,QAAQ,CAAC,GAAG,CAAC,GAAG,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE,EAAE;oBAC/C,QAAQ,CAAC,cAAc,CAAC,GAAG,QAAQ,CAAC,SAAS,CAAC,CAAA;oBAE9C,OAAO,QAAQ,CAAC,SAAS,CAAC,CAAA;oBAC1B,OAAO,QAAQ,CAAC,cAAc,CAAC,CAAA;oBAE/B,OAAO,QAAQ,CAAA;gBACjB,CAAC,CAAe,CAAA;YAClB,CAAC,MAAM,CAAC;gBACN,QAAQ,CAAC,GAAG,CAAC,GAAG,SAAS,CAAA;YAC3B,CAAC;YAED,OAAO,QAAQ,CAAA;QACjB,CAAC,EAAE,CAAA,CAA2B,CAAC,CAAA;IACjC,CAAC;IAED,cAAA,EAAgB,CACR,MAAM,CAAC,SAAS,CAAC,GAA2B,EAAA;QAClD,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,CAAA;IACxC,CAAC;IAED,cAAA,EAAgB,CACR,MAAM,CAAC,QAAgC,EAAA;QAC7C,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,QAAQ,CAAA;IAC/B,CAAC;IAED,cAAA,EAAgB,CACR,OAAO,CAAC,QAAiC,EAAA;QAC/C,IAAI,CAAC,MAAM,CAAC,OAAO,GAAG,QAAQ,CAAA;IAChC,CAAC;IAED,cAAA,EAAgB,CACR,MAAM,CAAC,QAAoB,EAAA;QACjC,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,QAAQ,CAAA;IAC/B,CAAC;IAED,cAAA,EAAgB,CACR,kBAAkB,GAAA;QACxB,OAAO,CAAC,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,KAAK,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAA;IAClE,CAAC;CACF"}}, {"offset": {"line": 2235, "column": 0}, "map": {"version": 3, "file": "RealtimeChannel.js", "sources": ["../../src/RealtimeChannel.ts"], "sourceRoot": "", "names": [], "mappings": ";;;;;;;AAAA,OAAO,EAAE,cAAc,EAAE,cAAc,EAAE,MAAM,iBAAiB,CAAA;AAChE,OAAO,IAAI,MAAM,YAAY,CAAA;AAE7B,OAAO,KAAK,MAAM,aAAa,CAAA;AAC/B,OAAO,gBAEN,MAAM,oBAAoB,CAAA;AAM3B,OAAO,KAAK,YAAY,MAAM,oBAAoB,CAAA;;;;;;;AA6ElD,IAAY,sCAKX;AALD,CAAA,SAAY,sCAAsC;IAChD,sCAAA,CAAA,MAAA,GAAA,GAAS,CAAA;IACT,sCAAA,CAAA,SAAA,GAAA,QAAiB,CAAA;IACjB,sCAAA,CAAA,SAAA,GAAA,QAAiB,CAAA;IACjB,sCAAA,CAAA,SAAA,GAAA,QAAiB,CAAA;AACnB,CAAC,EALW,sCAAsC,IAAA,CAAtC,sCAAsC,GAAA,CAAA,CAAA,GAKjD;AAED,IAAY,qBAKX;AALD,CAAA,SAAY,qBAAqB;IAC/B,qBAAA,CAAA,YAAA,GAAA,WAAuB,CAAA;IACvB,qBAAA,CAAA,WAAA,GAAA,UAAqB,CAAA;IACrB,qBAAA,CAAA,mBAAA,GAAA,kBAAqC,CAAA;IACrC,qBAAA,CAAA,SAAA,GAAA,QAAiB,CAAA;AACnB,CAAC,EALW,qBAAqB,IAAA,CAArB,qBAAqB,GAAA,CAAA,CAAA,GAKhC;AAED,IAAY,yBAKX;AALD,CAAA,SAAY,yBAAyB;IACnC,yBAAA,CAAA,aAAA,GAAA,YAAyB,CAAA;IACzB,yBAAA,CAAA,YAAA,GAAA,WAAuB,CAAA;IACvB,yBAAA,CAAA,SAAA,GAAA,QAAiB,CAAA;IACjB,yBAAA,CAAA,gBAAA,GAAA,eAA+B,CAAA;AACjC,CAAC,EALW,yBAAyB,IAAA,CAAzB,yBAAyB,GAAA,CAAA,CAAA,GAKpC;AAEM,MAAM,uBAAuB,8LAAG,iBAAc,CAAA;AAgBvC,MAAO,eAAe;IAoBlC,YACE,kCAAA,EAAoC,CAC7B,KAAa,EACb,SAAiC;QAAE,MAAM,EAAE,CAAA,CAAE;IAAA,CAAE,EAC/C,MAAsB,CAAA;QAFtB,IAAA,CAAA,KAAK,GAAL,KAAK,CAAQ;QACb,IAAA,CAAA,MAAM,GAAN,MAAM,CAAyC;QAC/C,IAAA,CAAA,MAAM,GAAN,MAAM,CAAgB;QAvB/B,IAAA,CAAA,QAAQ,GAOJ,CAAA,CAAE,CAAA;QAEN,IAAA,CAAA,KAAK,8LAAG,iBAAc,CAAC,MAAM,CAAA;QAC7B,IAAA,CAAA,UAAU,GAAG,KAAK,CAAA;QAGlB,IAAA,CAAA,UAAU,GAAW,EAAE,CAAA;QAYrB,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC,OAAO,CAAC,aAAa,EAAE,EAAE,CAAC,CAAA;QAChD,IAAI,CAAC,MAAM,CAAC,MAAM,GAAA,OAAA,MAAA,CACb;YACD,SAAS,EAAE;gBAAE,GAAG,EAAE,KAAK;gBAAE,IAAI,EAAE,KAAK;YAAA,CAAE;YACtC,QAAQ,EAAE;gBAAE,GAAG,EAAE,EAAE;YAAA,CAAE;YACrB,OAAO,EAAE,KAAK;SACf,EACE,MAAM,CAAC,MAAM,CACjB,CAAA;QACD,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,CAAA;QAClC,IAAI,CAAC,QAAQ,GAAG,0LAAI,UAAI,CACtB,IAAI,6LACJ,iBAAc,CAAC,IAAI,EACnB,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,OAAO,CACb,CAAA;QACD,IAAI,CAAC,WAAW,GAAG,2LAAI,UAAK,CAC1B,GAAG,CAAG,CAAD,GAAK,CAAC,qBAAqB,EAAE,EAClC,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAC7B,CAAA;QACD,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,EAAE,GAAG,EAAE;YAC/B,IAAI,CAAC,KAAK,8LAAG,iBAAc,CAAC,MAAM,CAAA;YAClC,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,CAAA;YACxB,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,SAAe,EAAE,CAAG,CAAD,QAAU,CAAC,IAAI,EAAE,CAAC,CAAA;YAC9D,IAAI,CAAC,UAAU,GAAG,EAAE,CAAA;QACtB,CAAC,CAAC,CAAA;QACF,IAAI,CAAC,QAAQ,CAAC,GAAG,EAAE;YACjB,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,CAAA;YACxB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,SAAS,EAAE,CAAA,MAAA,EAAS,IAAI,CAAC,KAAK,CAAA,CAAA,EAAI,IAAI,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAA;YACpE,IAAI,CAAC,KAAK,8LAAG,iBAAc,CAAC,MAAM,CAAA;YAClC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,CAAA;QAC3B,CAAC,CAAC,CAAA;QACF,IAAI,CAAC,QAAQ,CAAC,CAAC,MAAc,EAAE,EAAE;YAC/B,IAAI,IAAI,CAAC,UAAU,EAAE,IAAI,IAAI,CAAC,SAAS,EAAE,EAAE,CAAC;gBAC1C,OAAM;YACR,CAAC;YACD,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,SAAS,EAAE,CAAA,MAAA,EAAS,IAAI,CAAC,KAAK,EAAE,EAAE,MAAM,CAAC,CAAA;YACzD,IAAI,CAAC,KAAK,8LAAG,iBAAc,CAAC,OAAO,CAAA;YACnC,IAAI,CAAC,WAAW,CAAC,eAAe,EAAE,CAAA;QACpC,CAAC,CAAC,CAAA;QACF,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,SAAS,EAAE,GAAG,EAAE;YACpC,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,EAAE,CAAC;gBACvB,OAAM;YACR,CAAC;YACD,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,SAAS,EAAE,CAAA,QAAA,EAAW,IAAI,CAAC,KAAK,EAAE,EAAE,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAA;YAC1E,IAAI,CAAC,KAAK,8LAAG,iBAAc,CAAC,OAAO,CAAA;YACnC,IAAI,CAAC,WAAW,CAAC,eAAe,EAAE,CAAA;QACpC,CAAC,CAAC,CAAA;QACF,IAAI,CAAC,GAAG,4LAAC,iBAAc,CAAC,KAAK,EAAE,CAAA,CAAE,EAAE,CAAC,OAAY,EAAE,GAAW,EAAE,EAAE;YAC/D,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,EAAE,OAAO,CAAC,CAAA;QACnD,CAAC,CAAC,CAAA;QAEF,IAAI,CAAC,QAAQ,GAAG,+LAAI,UAAgB,CAAC,IAAI,CAAC,CAAA;QAE1C,IAAI,CAAC,oBAAoB,qMACvB,kBAAA,AAAe,EAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,GAAG,gBAAgB,CAAA;QAC1D,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,OAAO,IAAI,KAAK,CAAA;IACpD,CAAC;IAED,oDAAA,EAAsD,CACtD,SAAS,CACP,QAAmE,EACnE,OAAO,GAAG,IAAI,CAAC,OAAO,EAAA;;QAEtB,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,EAAE,CAAC;YAC/B,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAA;QACvB,CAAC;QACD,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;YACpB,MAAM,CAAA,oGAAA,CAAsG,CAAA;QAC9G,CAAC,MAAM,CAAC;YACN,MAAM,EACJ,MAAM,EAAE,EAAE,SAAS,EAAE,QAAQ,EAAE,OAAO,EAAE,SAAS,EAAE,EACpD,GAAG,IAAI,CAAC,MAAM,CAAA;YAEf,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAQ,EAAE,CACvB,CADyB,OACjB,KAAA,QAAR,QAAQ,KAAA,KAAA,IAAA,KAAA,IAAR,QAAQ,CAAG,yBAAyB,CAAC,aAAa,EAAE,CAAC,CAAC,CACvD,CAAA;YACD,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAG,CAAD,OAAS,KAAA,QAAR,QAAQ,KAAA,KAAA,IAAA,KAAA,IAAR,QAAQ,CAAG,yBAAyB,CAAC,MAAM,CAAC,CAAC,CAAA;YAEjE,MAAM,kBAAkB,GAA8B,CAAA,CAAE,CAAA;YACxD,MAAM,MAAM,GAAG;gBACb,SAAS;gBACT,QAAQ;gBACR,gBAAgB,EACd,CAAA,KAAA,CAAA,KAAA,IAAI,CAAC,QAAQ,CAAC,gBAAgB,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAE,GAAG,CAAC,CAAC,CAAC,EAAE,CAAG,CAAD,AAAE,CAAC,MAAM,CAAC,MAAA,QAAA,OAAA,KAAA,IAAA,KAAI,EAAE;gBAC5D,OAAO,EAAE,SAAS;aACnB,CAAA;YAED,IAAI,IAAI,CAAC,MAAM,CAAC,gBAAgB,EAAE,CAAC;gBACjC,kBAAkB,CAAC,YAAY,GAAG,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAA;YAChE,CAAC;YAED,IAAI,CAAC,iBAAiB,CAAA,OAAA,MAAA,CAAM;gBAAE,MAAM;YAAA,CAAE,EAAK,kBAAkB,EAAG,CAAA;YAEhE,IAAI,CAAC,UAAU,GAAG,IAAI,CAAA;YACtB,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAA;YAErB,IAAI,CAAC,QAAQ,CACV,OAAO,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE,gBAAgB,EAA0B,EAAE,EAAE;;gBACpE,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAA;gBACrB,IAAI,gBAAgB,KAAK,SAAS,EAAE,CAAC;oBACnC,QAAQ,KAAA,QAAR,QAAQ,KAAA,KAAA,IAAA,KAAA,IAAR,QAAQ,CAAG,yBAAyB,CAAC,UAAU,CAAC,CAAA;oBAChD,OAAM;gBACR,CAAC,MAAM,CAAC;oBACN,MAAM,sBAAsB,GAAG,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAAA;oBAC7D,MAAM,WAAW,GAAG,CAAA,KAAA,sBAAsB,KAAA,QAAtB,sBAAsB,KAAA,KAAA,IAAA,KAAA,IAAtB,sBAAsB,CAAE,MAAM,MAAA,QAAA,OAAA,KAAA,IAAA,KAAI,CAAC,CAAA;oBACvD,MAAM,mBAAmB,GAAG,EAAE,CAAA;oBAE9B,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,EAAE,CAAC,EAAE,CAAE,CAAC;wBACrC,MAAM,qBAAqB,GAAG,sBAAsB,CAAC,CAAC,CAAC,CAAA;wBACvD,MAAM,EACJ,MAAM,EAAE,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,EACzC,GAAG,qBAAqB,CAAA;wBACzB,MAAM,oBAAoB,GACxB,gBAAgB,IAAI,gBAAgB,CAAC,CAAC,CAAC,CAAA;wBAEzC,IACE,oBAAoB,IACpB,oBAAoB,CAAC,KAAK,KAAK,KAAK,IACpC,oBAAoB,CAAC,MAAM,KAAK,MAAM,IACtC,oBAAoB,CAAC,KAAK,KAAK,KAAK,IACpC,oBAAoB,CAAC,MAAM,KAAK,MAAM,EACtC,CAAC;4BACD,mBAAmB,CAAC,IAAI,CAAA,OAAA,MAAA,CAAA,OAAA,MAAA,CAAA,CAAA,GACnB,qBAAqB,GAAA;gCACxB,EAAE,EAAE,oBAAoB,CAAC,EAAE;4BAAA,GAC3B,CAAA;wBACJ,CAAC,MAAM,CAAC;4BACN,IAAI,CAAC,WAAW,EAAE,CAAA;4BAClB,IAAI,CAAC,KAAK,8LAAG,iBAAc,CAAC,OAAO,CAAA;4BAEnC,QAAQ,KAAA,QAAR,QAAQ,KAAA,KAAA,IAAA,KAAA,IAAR,QAAQ,CACN,yBAAyB,CAAC,aAAa,EACvC,IAAI,KAAK,CACP,kEAAkE,CACnE,CACF,CAAA;4BACD,OAAM;wBACR,CAAC;oBACH,CAAC;oBAED,IAAI,CAAC,QAAQ,CAAC,gBAAgB,GAAG,mBAAmB,CAAA;oBAEpD,QAAQ,IAAI,QAAQ,CAAC,yBAAyB,CAAC,UAAU,CAAC,CAAA;oBAC1D,OAAM;gBACR,CAAC;YACH,CAAC,CAAC,CACD,OAAO,CAAC,OAAO,EAAE,CAAC,KAA6B,EAAE,EAAE;gBAClD,IAAI,CAAC,KAAK,8LAAG,iBAAc,CAAC,OAAO,CAAA;gBACnC,QAAQ,KAAA,QAAR,QAAQ,KAAA,KAAA,IAAA,KAAA,IAAR,QAAQ,CACN,yBAAyB,CAAC,aAAa,EACvC,IAAI,KAAK,CACP,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,OAAO,CAAC,CAC3D,CACF,CAAA;gBACD,OAAM;YACR,CAAC,CAAC,CACD,OAAO,CAAC,SAAS,EAAE,GAAG,EAAE;gBACvB,QAAQ,KAAA,QAAR,QAAQ,KAAA,KAAA,IAAA,KAAA,IAAR,QAAQ,CAAG,yBAAyB,CAAC,SAAS,CAAC,CAAA;gBAC/C,OAAM;YACR,CAAC,CAAC,CAAA;QACN,CAAC;QACD,OAAO,IAAI,CAAA;IACb,CAAC;IAED,aAAa,GAAA;QAGX,OAAO,IAAI,CAAC,QAAQ,CAAC,KAAiC,CAAA;IACxD,CAAC;IAED,KAAK,CAAC,KAAK,CACT,OAA+B,EAC/B,OAA+B,CAAA,CAAE,EAAA;QAEjC,OAAO,MAAM,IAAI,CAAC,IAAI,CACpB;YACE,IAAI,EAAE,UAAU;YAChB,KAAK,EAAE,OAAO;YACd,OAAO;SACR,EACD,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAC7B,CAAA;IACH,CAAC;IAED,KAAK,CAAC,OAAO,CACX,OAA+B,CAAA,CAAE,EAAA;QAEjC,OAAO,MAAM,IAAI,CAAC,IAAI,CACpB;YACE,IAAI,EAAE,UAAU;YAChB,KAAK,EAAE,SAAS;SACjB,EACD,IAAI,CACL,CAAA;IACH,CAAC;IAqED,EAAE,CACA,IAAgC,EAChC,MAAgD,EAChD,QAAgC,EAAA;QAEhC,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,MAAM,EAAE,QAAQ,CAAC,CAAA;IACzC,CAAC;IACD;;;;;;;;OAQG,CACH,KAAK,CAAC,IAAI,CACR,IAKC,EACD,OAA+B,CAAA,CAAE,EAAA;;QAEjC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,IAAI,CAAC,IAAI,KAAK,WAAW,EAAE,CAAC;YAClD,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,gBAAgB,EAAE,GAAG,IAAI,CAAA;YACjD,MAAM,aAAa,GAAG,IAAI,CAAC,MAAM,CAAC,gBAAgB,GAC9C,CAAA,OAAA,EAAU,IAAI,CAAC,MAAM,CAAC,gBAAgB,EAAE,GACxC,EAAE,CAAA;YACN,MAAM,OAAO,GAAG;gBACd,MAAM,EAAE,MAAM;gBACd,OAAO,EAAE;oBACP,aAAa,EAAE,aAAa;oBAC5B,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE;oBACpD,cAAc,EAAE,kBAAkB;iBACnC;gBACD,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC;oBACnB,QAAQ,EAAE;wBACR;4BACE,KAAK,EAAE,IAAI,CAAC,QAAQ;4BACpB,KAAK;4BACL,OAAO,EAAE,gBAAgB;4BACzB,OAAO,EAAE,IAAI,CAAC,OAAO;yBACtB;qBACF;iBACF,CAAC;aACH,CAAA;YAED,IAAI,CAAC;gBACH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAC3C,IAAI,CAAC,oBAAoB,EACzB,OAAO,EACP,CAAA,KAAA,IAAI,CAAC,OAAO,MAAA,QAAA,OAAA,KAAA,IAAA,KAAI,IAAI,CAAC,OAAO,CAC7B,CAAA;gBAED,MAAM,CAAA,CAAA,KAAA,QAAQ,CAAC,IAAI,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAE,MAAM,EAAE,CAAA,CAAA;gBAC7B,OAAO,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,CAAA;YACrC,CAAC,CAAC,OAAO,KAAU,EAAE,CAAC;gBACpB,IAAI,KAAK,CAAC,IAAI,KAAK,YAAY,EAAE,CAAC;oBAChC,OAAO,WAAW,CAAA;gBACpB,CAAC,MAAM,CAAC;oBACN,OAAO,OAAO,CAAA;gBAChB,CAAC;YACH,CAAC;QACH,CAAC,MAAM,CAAC;YACN,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;;gBAC7B,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,CAAA;gBAEtE,IAAI,IAAI,CAAC,IAAI,KAAK,WAAW,IAAI,CAAC,CAAA,CAAA,KAAA,CAAA,KAAA,CAAA,KAAA,IAAI,CAAC,MAAM,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAE,MAAM,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAE,SAAS,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAE,GAAG,CAAA,EAAE,CAAC;oBACtE,OAAO,CAAC,IAAI,CAAC,CAAA;gBACf,CAAC;gBAED,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,GAAG,CAAG,CAAD,MAAQ,CAAC,IAAI,CAAC,CAAC,CAAA;gBACvC,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,GAAG,CAAG,CAAD,MAAQ,CAAC,OAAO,CAAC,CAAC,CAAA;gBAC7C,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,GAAG,CAAG,CAAD,MAAQ,CAAC,WAAW,CAAC,CAAC,CAAA;YACrD,CAAC,CAAC,CAAA;QACJ,CAAC;IACH,CAAC;IAED,iBAAiB,CAAC,OAA+B,EAAA;QAC/C,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,OAAO,CAAC,CAAA;IACtC,CAAC;IAED;;;;;;;;OAQG,CACH,WAAW,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,EAAA;QAChC,IAAI,CAAC,KAAK,8LAAG,iBAAc,CAAC,OAAO,CAAA;QACnC,MAAM,OAAO,GAAG,GAAG,EAAE;YACnB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,SAAS,EAAE,CAAA,MAAA,EAAS,IAAI,CAAC,KAAK,EAAE,CAAC,CAAA;YACjD,IAAI,CAAC,QAAQ,4LAAC,iBAAc,CAAC,KAAK,EAAE,OAAO,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAA;QAC/D,CAAC,CAAA;QAED,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAA;QAEvB,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;YAC7B,MAAM,SAAS,GAAG,0LAAI,UAAI,CAAC,IAAI,6LAAE,iBAAc,CAAC,KAAK,EAAE,CAAA,CAAE,EAAE,OAAO,CAAC,CAAA;YACnE,SAAS,CACN,OAAO,CAAC,IAAI,EAAE,GAAG,EAAE;gBAClB,OAAO,EAAE,CAAA;gBACT,OAAO,CAAC,IAAI,CAAC,CAAA;YACf,CAAC,CAAC,CACD,OAAO,CAAC,SAAS,EAAE,GAAG,EAAE;gBACvB,OAAO,EAAE,CAAA;gBACT,OAAO,CAAC,WAAW,CAAC,CAAA;YACtB,CAAC,CAAC,CACD,OAAO,CAAC,OAAO,EAAE,GAAG,EAAE;gBACrB,OAAO,CAAC,OAAO,CAAC,CAAA;YAClB,CAAC,CAAC,CAAA;YAEJ,SAAS,CAAC,IAAI,EAAE,CAAA;YAChB,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE,CAAC;gBACrB,SAAS,CAAC,OAAO,CAAC,IAAI,EAAE,CAAA,CAAE,CAAC,CAAA;YAC7B,CAAC;QACH,CAAC,CAAC,CAAA;IACJ,CAAC;IACD;;;;OAIG,CACH,QAAQ,GAAA;QACN,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,IAAU,EAAE,CAAG,CAAD,GAAK,CAAC,OAAO,EAAE,CAAC,CAAA;QACvD,IAAI,CAAC,WAAW,IAAI,YAAY,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAA;QACxD,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAA;IACzB,CAAC;IAED,cAAA,EAAgB,CAEhB,KAAK,CAAC,iBAAiB,CACrB,GAAW,EACX,OAA+B,EAC/B,OAAe,EAAA;QAEf,MAAM,UAAU,GAAG,IAAI,eAAe,EAAE,CAAA;QACxC,MAAM,EAAE,GAAG,UAAU,CAAC,GAAG,CAAG,CAAD,SAAW,CAAC,KAAK,EAAE,EAAE,OAAO,CAAC,CAAA;QAExD,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,EAAA,OAAA,MAAA,CAAA,OAAA,MAAA,CAAA,CAAA,GACvC,OAAO,GAAA;YACV,MAAM,EAAE,UAAU,CAAC,MAAM;QAAA,GACzB,CAAA;QAEF,YAAY,CAAC,EAAE,CAAC,CAAA;QAEhB,OAAO,QAAQ,CAAA;IACjB,CAAC;IAED,cAAA,EAAgB,CAChB,KAAK,CACH,KAAa,EACb,OAA+B,EAC/B,OAAO,GAAG,IAAI,CAAC,OAAO,EAAA;QAEtB,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;YACrB,MAAM,CAAA,eAAA,EAAkB,KAAK,CAAA,MAAA,EAAS,IAAI,CAAC,KAAK,CAAA,+DAAA,CAAiE,CAAA;QACnH,CAAC;QACD,IAAI,SAAS,GAAG,0LAAI,UAAI,CAAC,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,OAAO,CAAC,CAAA;QACvD,IAAI,IAAI,CAAC,QAAQ,EAAE,EAAE,CAAC;YACpB,SAAS,CAAC,IAAI,EAAE,CAAA;QAClB,CAAC,MAAM,CAAC;YACN,SAAS,CAAC,YAAY,EAAE,CAAA;YACxB,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,CAAA;QACjC,CAAC;QAED,OAAO,SAAS,CAAA;IAClB,CAAC;IAED;;;;;;;OAOG,CACH,UAAU,CAAC,MAAc,EAAE,OAAY,EAAE,IAAa,EAAA;QACpD,OAAO,OAAO,CAAA;IAChB,CAAC;IAED,cAAA,EAAgB,CAChB,SAAS,CAAC,KAAa,EAAA;QACrB,OAAO,IAAI,CAAC,KAAK,KAAK,KAAK,CAAA;IAC7B,CAAC;IAED,cAAA,EAAgB,CAChB,QAAQ,GAAA;QACN,OAAO,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAA;IAC1B,CAAC;IAED,cAAA,EAAgB,CAChB,QAAQ,CAAC,IAAY,EAAE,OAAa,EAAE,GAAY,EAAA;;QAChD,MAAM,SAAS,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAA;QAC1C,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,6LAAG,kBAAc,CAAA;QACpD,MAAM,MAAM,GAAa;YAAC,KAAK;YAAE,KAAK;YAAE,KAAK;YAAE,IAAI;SAAC,CAAA;QACpD,IAAI,GAAG,IAAI,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,GAAG,KAAK,IAAI,CAAC,QAAQ,EAAE,EAAE,CAAC;YACrE,OAAM;QACR,CAAC;QACD,IAAI,cAAc,GAAG,IAAI,CAAC,UAAU,CAAC,SAAS,EAAE,OAAO,EAAE,GAAG,CAAC,CAAA;QAC7D,IAAI,OAAO,IAAI,CAAC,cAAc,EAAE,CAAC;YAC/B,MAAM,6EAA6E,CAAA;QACrF,CAAC;QAED,IAAI;YAAC,QAAQ;YAAE,QAAQ;YAAE,QAAQ;SAAC,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC;YACvD,CAAA,KAAA,IAAI,CAAC,QAAQ,CAAC,gBAAgB,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAC1B,MAAM,CAAC,CAAC,IAAI,EAAE,EAAE;;gBAChB,OACE,AADK,CACL,CAAA,KAAA,IAAI,CAAC,MAAM,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAE,KAAK,MAAK,GAAG,IAC1B,CAAA,CAAA,KAAA,CAAA,KAAA,IAAI,CAAC,MAAM,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAE,KAAK,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAE,iBAAiB,EAAE,MAAK,SAAS,CACtD,CAAA;YACH,CAAC,EACA,GAAG,CAAC,CAAC,IAAI,EAAE,CAAG,CAAD,GAAK,CAAC,QAAQ,CAAC,cAAc,EAAE,GAAG,CAAC,CAAC,CAAA;QACtD,CAAC,MAAM,CAAC;YACN,CAAA,KAAA,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GACpB,MAAM,CAAC,CAAC,IAAI,EAAE,EAAE;;gBAChB,IACE;oBAAC,WAAW;oBAAE,UAAU;oBAAE,kBAAkB;iBAAC,CAAC,QAAQ,CAAC,SAAS,CAAC,EACjE,CAAC;oBACD,IAAI,IAAI,IAAI,IAAI,EAAE,CAAC;wBACjB,MAAM,MAAM,GAAG,IAAI,CAAC,EAAE,CAAA;wBACtB,MAAM,SAAS,GAAG,CAAA,KAAA,IAAI,CAAC,MAAM,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAE,KAAK,CAAA;wBACpC,OAAO,AACL,MAAM,KACN,CAAA,KAAA,OAAO,CAAC,GAAG,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAE,QAAQ,CAAC,MAAM,CAAC,CAAA,IAC7B,CAAC,SAAS,KAAK,GAAG,IAChB,CAAA,SAAS,KAAA,QAAT,SAAS,KAAA,KAAA,IAAA,KAAA,IAAT,SAAS,CAAE,iBAAiB,EAAE,OAC5B,CAAA,KAAA,OAAO,CAAC,IAAI,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAE,IAAI,CAAC,iBAAiB,EAAE,CAAA,CAAC,CAC5C,CAAA;oBACH,CAAC,MAAM,CAAC;wBACN,MAAM,SAAS,GAAG,CAAA,KAAA,CAAA,KAAA,IAAI,KAAA,QAAJ,IAAI,KAAA,KAAA,IAAA,KAAA,IAAJ,IAAI,CAAE,MAAM,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAE,KAAK,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAE,iBAAiB,EAAE,CAAA;wBAC1D,OAAO,AACL,SAAS,KAAK,GAAG,IACjB,SAAS,KAAA,CAAK,CAAA,KAAA,OAAO,KAAA,QAAP,OAAO,KAAA,KAAA,IAAA,KAAA,IAAP,OAAO,CAAE,KAAK,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAE,iBAAiB,EAAE,CAAA,CAClD,CAAA;oBACH,CAAC;gBACH,CAAC,MAAM,CAAC;oBACN,OAAO,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,KAAK,SAAS,CAAA;gBACpD,CAAC;YACH,CAAC,EACA,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE;gBACZ,IAAI,OAAO,cAAc,KAAK,QAAQ,IAAI,KAAK,IAAI,cAAc,EAAE,CAAC;oBAClE,MAAM,eAAe,GAAG,cAAc,CAAC,IAAI,CAAA;oBAC3C,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,gBAAgB,EAAE,IAAI,EAAE,MAAM,EAAE,GACrD,eAAe,CAAA;oBACjB,MAAM,eAAe,GAAG;wBACtB,MAAM,EAAE,MAAM;wBACd,KAAK,EAAE,KAAK;wBACZ,gBAAgB,EAAE,gBAAgB;wBAClC,SAAS,EAAE,IAAI;wBACf,GAAG,EAAE,CAAA,CAAE;wBACP,GAAG,EAAE,CAAA,CAAE;wBACP,MAAM,EAAE,MAAM;qBACf,CAAA;oBACD,cAAc,GAAA,OAAA,MAAA,CAAA,OAAA,MAAA,CAAA,CAAA,GACT,eAAe,GACf,IAAI,CAAC,kBAAkB,CAAC,eAAe,CAAC,CAC5C,CAAA;gBACH,CAAC;gBACD,IAAI,CAAC,QAAQ,CAAC,cAAc,EAAE,GAAG,CAAC,CAAA;YACpC,CAAC,CAAC,CAAA;QACN,CAAC;IACH,CAAC;IAED,cAAA,EAAgB,CAChB,SAAS,GAAA;QACP,OAAO,IAAI,CAAC,KAAK,gMAAK,iBAAc,CAAC,MAAM,CAAA;IAC7C,CAAC;IAED,cAAA,EAAgB,CAChB,SAAS,GAAA;QACP,OAAO,IAAI,CAAC,KAAK,KAAK,4MAAc,CAAC,MAAM,CAAA;IAC7C,CAAC;IAED,cAAA,EAAgB,CAChB,UAAU,GAAA;QACR,OAAO,IAAI,CAAC,KAAK,gMAAK,iBAAc,CAAC,OAAO,CAAA;IAC9C,CAAC;IAED,cAAA,EAAgB,CAChB,UAAU,GAAA;QACR,OAAO,IAAI,CAAC,KAAK,gMAAK,iBAAc,CAAC,OAAO,CAAA;IAC9C,CAAC;IAED,cAAA,EAAgB,CAChB,eAAe,CAAC,GAAW,EAAA;QACzB,OAAO,CAAA,WAAA,EAAc,GAAG,EAAE,CAAA;IAC5B,CAAC;IAED,cAAA,EAAgB,CAChB,GAAG,CAAC,IAAY,EAAE,MAA8B,EAAE,QAAkB,EAAA;QAClE,MAAM,SAAS,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAA;QAE1C,MAAM,OAAO,GAAG;YACd,IAAI,EAAE,SAAS;YACf,MAAM,EAAE,MAAM;YACd,QAAQ,EAAE,QAAQ;SACnB,CAAA;QAED,IAAI,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC;YAC7B,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;QACxC,CAAC,MAAM,CAAC;YACN,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,GAAG;gBAAC,OAAO;aAAC,CAAA;QACtC,CAAC;QAED,OAAO,IAAI,CAAA;IACb,CAAC;IAED,cAAA,EAAgB,CAChB,IAAI,CAAC,IAAY,EAAE,MAA8B,EAAA;QAC/C,MAAM,SAAS,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAA;QAE1C,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,EAAE;;YAClE,OAAO,CAAC,CACN,CAAA,CAAA,KAAA,IAAI,CAAC,IAAI,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAE,iBAAiB,EAAE,MAAK,SAAS,IAC5C,eAAe,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,EAAE,MAAM,CAAC,CAC7C,CAAA;QACH,CAAC,CAAC,CAAA;QACF,OAAO,IAAI,CAAA;IACb,CAAC;IAED,cAAA,EAAgB,CACR,MAAM,CAAC,OAAO,CACpB,IAA+B,EAC/B,IAA+B,EAAA;QAE/B,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,MAAM,KAAK,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,CAAC;YAC1D,OAAO,KAAK,CAAA;QACd,CAAC;QAED,IAAK,MAAM,CAAC,IAAI,IAAI,CAAE,CAAC;YACrB,IAAI,IAAI,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC;gBACxB,OAAO,KAAK,CAAA;YACd,CAAC;QACH,CAAC;QAED,OAAO,IAAI,CAAA;IACb,CAAC;IAED,cAAA,EAAgB,CACR,qBAAqB,GAAA;QAC3B,IAAI,CAAC,WAAW,CAAC,eAAe,EAAE,CAAA;QAClC,IAAI,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,EAAE,CAAC;YAC9B,IAAI,CAAC,OAAO,EAAE,CAAA;QAChB,CAAC;IACH,CAAC;IAED;;;;OAIG,CACK,QAAQ,CAAC,QAAkB,EAAA;QACjC,IAAI,CAAC,GAAG,4LAAC,iBAAc,CAAC,KAAK,EAAE,CAAA,CAAE,EAAE,QAAQ,CAAC,CAAA;IAC9C,CAAC;IAED;;;;OAIG,CACK,QAAQ,CAAC,QAAkB,EAAA;QACjC,IAAI,CAAC,GAAG,CAAC,4MAAc,CAAC,KAAK,EAAE,CAAA,CAAE,EAAE,CAAC,MAAc,EAAE,CAAG,CAAD,OAAS,CAAC,MAAM,CAAC,CAAC,CAAA;IAC1E,CAAC;IAED;;;;OAIG,CACK,QAAQ,GAAA;QACd,OAAO,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,IAAI,IAAI,CAAC,SAAS,EAAE,CAAA;IACtD,CAAC;IAED,cAAA,EAAgB,CACR,OAAO,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,EAAA;QACpC,IAAI,IAAI,CAAC,UAAU,EAAE,EAAE,CAAC;YACtB,OAAM;QACR,CAAC;QACD,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;QACvC,IAAI,CAAC,KAAK,8LAAG,iBAAc,CAAC,OAAO,CAAA;QACnC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,CAAA;IAC/B,CAAC;IAED,cAAA,EAAgB,CACR,kBAAkB,CAAC,OAAY,EAAA;QACrC,MAAM,OAAO,GAAG;YACd,GAAG,EAAE,CAAA,CAAE;YACP,GAAG,EAAE,CAAA,CAAE;SACR,CAAA;QAED,IAAI,OAAO,CAAC,IAAI,KAAK,QAAQ,IAAI,OAAO,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;YAC3D,OAAO,CAAC,GAAG,IAAG,YAAY,CAAC,wMAAiB,AAAjB,EACzB,OAAO,CAAC,OAAO,EACf,OAAO,CAAC,MAAM,CACf,CAAA;QACH,CAAC;QAED,IAAI,OAAO,CAAC,IAAI,KAAK,QAAQ,IAAI,OAAO,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;YAC3D,OAAO,CAAC,GAAG,qMAAG,YAAY,CAAC,OAAA,AAAiB,EAC1C,OAAO,CAAC,OAAO,EACf,OAAO,CAAC,UAAU,CACnB,CAAA;QACH,CAAC;QAED,OAAO,OAAO,CAAA;IAChB,CAAC;CACF"}}, {"offset": {"line": 2726, "column": 0}, "map": {"version": 3, "file": "RealtimeClient.js", "sources": ["../../src/RealtimeClient.ts"], "sourceRoot": "", "names": [], "mappings": ";;;AAAA,OAAO,SAAS,MAAM,aAAa,CAAA;AAEnC,OAAO,EACL,cAAc,EACd,gBAAgB,EAChB,eAAe,EACf,eAAe,EACf,aAAa,EACb,UAAU,EACV,GAAG,EACH,eAAe,GAChB,MAAM,iBAAiB,CAAA;AAExB,OAAO,UAAU,MAAM,kBAAkB,CAAA;AACzC,OAAO,KAAK,MAAM,aAAa,CAAA;AAE/B,OAAO,EAAE,eAAe,EAAE,MAAM,oBAAoB,CAAA;AACpD,OAAO,eAAe,MAAM,mBAAmB,CAAA;;;;;;;AA6B/C,MAAM,IAAI,GAAG,GAAG,EAAE,AAAE,CAAC,CAAA;AAqCrB,MAAM,aAAa,GAAG,CAAA;;;;;MAKhB,CAAA;AAEQ,MAAO,cAAc;IAyCjC;;;;;;;;;;;;;;;;;OAiBG,CACH,YAAY,QAAgB,EAAE,OAA+B,CAAA;;QA1D7D,IAAA,CAAA,gBAAgB,GAAkB,IAAI,CAAA;QACtC,IAAA,CAAA,MAAM,GAAkB,IAAI,CAAA;QAC5B,IAAA,CAAA,QAAQ,GAAsB,IAAI,KAAK,EAAE,CAAA;QACzC,IAAA,CAAA,QAAQ,GAAW,EAAE,CAAA;QACrB,IAAA,CAAA,YAAY,GAAW,EAAE,CAAA;QACzB,IAAA,CAAA,OAAO,8LAA+B,kBAAe,CAAA;QACrD,IAAA,CAAA,MAAM,GAA+B,CAAA,CAAE,CAAA;QACvC,IAAA,CAAA,OAAO,8LAAW,kBAAe,CAAA;QAEjC,IAAA,CAAA,mBAAmB,GAAW,KAAK,CAAA;QACnC,IAAA,CAAA,cAAc,GAA+C,SAAS,CAAA;QACtE,IAAA,CAAA,mBAAmB,GAAkB,IAAI,CAAA;QACzC,IAAA,CAAA,iBAAiB,GAAsC,IAAI,CAAA;QAC3D,IAAA,CAAA,GAAG,GAAW,CAAC,CAAA;QAEf,IAAA,CAAA,MAAM,GAAa,IAAI,CAAA;QAKvB,IAAA,CAAA,IAAI,GAAyB,IAAI,CAAA;QACjC,IAAA,CAAA,UAAU,GAAe,EAAE,CAAA;QAC3B,IAAA,CAAA,UAAU,GAAe,IAAI,sMAAU,EAAE,CAAA;QACzC,IAAA,CAAA,oBAAoB,GAKhB;YACF,IAAI,EAAE,EAAE;YACR,KAAK,EAAE,EAAE;YACT,KAAK,EAAE,EAAE;YACT,OAAO,EAAE,EAAE;SACZ,CAAA;QAED,IAAA,CAAA,WAAW,GAA0C,IAAI,CAAA;QAoUzD;;;;WAIG,CACH,IAAA,CAAA,aAAa,GAAG,CAAC,WAAmB,EAAS,EAAE;YAC7C,IAAI,MAAa,CAAA;YACjB,IAAI,WAAW,EAAE,CAAC;gBAChB,MAAM,GAAG,WAAW,CAAA;YACtB,CAAC,MAAM,IAAI,OAAO,KAAK,KAAK,WAAW,EAAE,CAAC;gBACxC,MAAM,GAAG,CAAC,GAAG,IAAI,EAAE,CACjB,CADmB,KACb,CAAC,sBAA6B,CAAC,+GAAC,IAAI,CAAC,CAAC,EAAE,OAAO,EAAE,MAAK,EAAE,EAAE,CAC9D,CADgE,IAC3D,CAAC,IAAG,IAAI,CAAC,CACf,CAAA;YACL,CAAC,MAAM,CAAC;gBACN,MAAM,GAAG,KAAK,CAAA;YAChB,CAAC;YACD,OAAO,CAAC,GAAG,IAAI,EAAE,CAAG,CAAD,KAAO,CAAC,GAAG,IAAI,CAAC,CAAA;QACrC,CAAC,CAAA;QA9TC,IAAI,CAAC,QAAQ,GAAG,GAAG,QAAQ,CAAA,CAAA,6LAAI,aAAU,CAAC,SAAS,EAAE,CAAA;QACrD,IAAI,CAAC,YAAY,qMAAG,kBAAA,AAAe,EAAC,QAAQ,CAAC,CAAA;QAC7C,IAAI,OAAO,KAAA,QAAP,OAAO,KAAA,KAAA,IAAA,KAAA,IAAP,OAAO,CAAE,SAAS,EAAE,CAAC;YACvB,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC,SAAS,CAAA;QACpC,CAAC,MAAM,CAAC;YACN,IAAI,CAAC,SAAS,GAAG,IAAI,CAAA;QACvB,CAAC;QACD,IAAI,OAAO,KAAA,QAAP,OAAO,KAAA,KAAA,IAAA,KAAA,IAAP,OAAO,CAAE,MAAM,EAAE,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,CAAA;QACjD,IAAI,OAAO,KAAA,QAAP,OAAO,KAAA,KAAA,IAAA,KAAA,IAAP,OAAO,CAAE,OAAO,EAAE,IAAI,CAAC,OAAO,GAAA,OAAA,MAAA,CAAA,OAAA,MAAA,CAAA,CAAA,GAAQ,IAAI,CAAC,OAAO,GAAK,OAAO,CAAC,OAAO,CAAE,CAAA;QAC5E,IAAI,OAAO,KAAA,QAAP,OAAO,KAAA,KAAA,IAAA,KAAA,IAAP,OAAO,CAAE,OAAO,EAAE,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO,CAAA;QACpD,IAAI,OAAO,KAAA,QAAP,OAAO,KAAA,KAAA,IAAA,KAAA,IAAP,OAAO,CAAE,MAAM,EAAE,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,CAAA;QACjD,IAAI,CAAA,OAAO,KAAA,QAAP,OAAO,KAAA,KAAA,IAAA,KAAA,IAAP,OAAO,CAAE,QAAQ,KAAA,CAAI,OAAO,KAAA,QAAP,OAAO,KAAA,KAAA,IAAA,KAAA,IAAP,OAAO,CAAE,SAAS,CAAA,EAAE,CAAC;YAC5C,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ,IAAI,OAAO,CAAC,SAAS,CAAA;YACrD,IAAI,CAAC,MAAM,GAAA,OAAA,MAAA,CAAA,OAAA,MAAA,CAAA,CAAA,GAAQ,IAAI,CAAC,MAAM,GAAA;gBAAE,SAAS,EAAE,IAAI,CAAC,QAAkB;YAAA,EAAE,CAAA;QACtE,CAAC;QAED,IAAI,OAAO,KAAA,QAAP,OAAO,KAAA,KAAA,IAAA,KAAA,IAAP,OAAO,CAAE,mBAAmB,EAC9B,IAAI,CAAC,mBAAmB,GAAG,OAAO,CAAC,mBAAmB,CAAA;QAExD,MAAM,gBAAgB,GAAG,CAAA,KAAA,OAAO,KAAA,QAAP,OAAO,KAAA,KAAA,IAAA,KAAA,IAAP,OAAO,CAAE,MAAM,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAE,MAAM,CAAA;QAChD,IAAI,gBAAgB,EAAE,CAAC;YACrB,IAAI,CAAC,gBAAgB,GAAG,gBAAgB,CAAA;YACxC,IAAI,CAAC,MAAM,GAAG,gBAAgB,CAAA;QAChC,CAAC;QAED,IAAI,CAAC,gBAAgB,GAAG,CAAA,OAAO,KAAA,QAAP,OAAO,KAAA,KAAA,IAAA,KAAA,IAAP,OAAO,CAAE,gBAAgB,IAC7C,OAAO,CAAC,gBAAgB,GACxB,CAAC,KAAa,EAAE,EAAE;YAChB,OAAO;gBAAC,IAAI;gBAAE,IAAI;gBAAE,IAAI;gBAAE,KAAK;aAAC,CAAC,KAAK,GAAG,CAAC,CAAC,IAAI,KAAK,CAAA;QACtD,CAAC,CAAA;QACL,IAAI,CAAC,MAAM,GAAG,CAAA,OAAO,KAAA,QAAP,OAAO,KAAA,KAAA,IAAA,KAAA,IAAP,OAAO,CAAE,MAAM,IACzB,OAAO,CAAC,MAAM,GACd,CAAC,OAAa,EAAE,QAAkB,EAAE,EAAE;YACpC,OAAO,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAA;QAC1C,CAAC,CAAA;QACL,IAAI,CAAC,MAAM,GAAG,CAAA,OAAO,KAAA,QAAP,OAAO,KAAA,KAAA,IAAA,KAAA,IAAP,OAAO,CAAE,MAAM,IACzB,OAAO,CAAC,MAAM,GACd,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAA;QAChD,IAAI,CAAC,cAAc,GAAG,2LAAI,UAAK,CAAC,KAAK,IAAI,EAAE;YACzC,IAAI,CAAC,UAAU,EAAE,CAAA;YACjB,IAAI,CAAC,OAAO,EAAE,CAAA;QAChB,CAAC,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAA;QAEzB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,OAAO,KAAA,QAAP,OAAO,KAAA,KAAA,IAAA,KAAA,IAAP,OAAO,CAAE,KAAK,CAAC,CAAA;QAC/C,IAAI,OAAO,KAAA,QAAP,OAAO,KAAA,KAAA,IAAA,KAAA,IAAP,OAAO,CAAE,MAAM,EAAE,CAAC;YACpB,IAAI,OAAO,MAAM,KAAK,WAAW,IAAI,CAAC,CAAe,CAAC,IAAV,CAAC,MAAM;;YAEnD,CAAC;YACD,IAAI,CAAC,MAAM,GAAG,CAAA,OAAO,KAAA,QAAP,OAAO,KAAA,KAAA,IAAA,KAAA,IAAP,OAAO,CAAE,MAAM,KAAI,KAAK,CAAA;YACtC,IAAI,CAAC,SAAS,GAAG,OAAO,KAAA,QAAP,OAAO,KAAA,KAAA,IAAA,KAAA,IAAP,OAAO,CAAE,SAAS,CAAA;QACrC,CAAC;QACD,IAAI,CAAC,WAAW,GAAG,CAAA,OAAO,KAAA,QAAP,OAAO,KAAA,KAAA,IAAA,KAAA,IAAP,OAAO,CAAE,WAAW,KAAI,IAAI,CAAA;IACjD,CAAC;IAED;;OAEG,CACH,OAAO,GAAA;QACL,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC;YACd,OAAM;QACR,CAAC;QACD,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;YACpB,IAAI,CAAC,SAAS,uLAAG,UAAS,CAAA;QAC5B,CAAC;QACD,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACnB,+CAA+C;YAC/C,MAAM,SAAS,GACb,OAAO,MAAM,GAAK,WAAW,IAAI,IAAI,CAAC,SAAS,KAAK,MAAM,CAAC,SAAS,CAAA;YACtE,IAAI,SAAS,0BAAE,CAAC;;YAEhB,CAAC,MAAM,CAAC;gBACN,IAAI,CAAC,IAAI,GAAG,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,WAAW,EAAE,EAAE,SAAS,EAAE;oBAC5D,OAAO,EAAE,IAAI,CAAC,OAAO;iBACtB,CAAC,CAAA;YACJ,CAAC;YACD,IAAI,CAAC,eAAe,EAAE,CAAA;YACtB,OAAM;QACR,CAAC;QACD,IAAI,CAAC,IAAI,GAAG,IAAI,gBAAgB,CAAC,IAAI,CAAC,WAAW,EAAE,EAAE,SAAS,EAAE;YAC9D,KAAK,EAAE,GAAG,EAAE;gBACV,IAAI,CAAC,IAAI,GAAG,IAAI,CAAA;YAClB,CAAC;SACF,CAAC,CAAA;IACJ,CAAC;IAED;;;OAGG,CACH,WAAW,GAAA;QACT,OAAO,IAAI,CAAC,aAAa,CACvB,IAAI,CAAC,QAAQ,EACb,MAAM,CAAC,MAAM,CAAC,CAAA,CAAE,EAAE,IAAI,CAAC,MAAM,EAAE;YAAE,GAAG,6LAAE,MAAG;QAAA,CAAE,CAAC,CAC7C,CAAA;IACH,CAAC;IAED;;;;;OAKG,CACH,UAAU,CAAC,IAAa,EAAE,MAAe,EAAA;QACvC,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC;YACd,IAAI,CAAC,IAAI,CAAC,OAAO,GAAG,YAAa,CAAC,CAAA,CAAC,OAAO;YAC1C,IAAI,IAAI,EAAE,CAAC;gBACT,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,MAAM,KAAA,QAAN,MAAM,KAAA,KAAA,IAAN,MAAM,GAAI,EAAE,CAAC,CAAA;YACrC,CAAC,MAAM,CAAC;gBACN,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,CAAA;YACnB,CAAC;YACD,IAAI,CAAC,IAAI,GAAG,IAAI,CAAA;YAEhB,sBAAsB;YACtB,IAAI,CAAC,cAAc,IAAI,aAAa,CAAC,IAAI,CAAC,cAAc,CAAC,CAAA;YACzD,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,CAAA;YAC3B,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,CAAG,CAAD,MAAQ,CAAC,QAAQ,EAAE,CAAC,CAAA;QACxD,CAAC;IACH,CAAC;IAED;;OAEG,CACH,WAAW,GAAA;QACT,OAAO,IAAI,CAAC,QAAQ,CAAA;IACtB,CAAC;IAED;;;OAGG,CACH,KAAK,CAAC,aAAa,CACjB,OAAwB,EAAA;QAExB,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,WAAW,EAAE,CAAA;QAC1C,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAG,CAAD,AAAE,CAAC,QAAQ,KAAK,OAAO,CAAC,QAAQ,CAAC,CAAA;QAE5E,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC/B,IAAI,CAAC,UAAU,EAAE,CAAA;QACnB,CAAC;QAED,OAAO,MAAM,CAAA;IACf,CAAC;IAED;;OAEG,CACH,KAAK,CAAC,iBAAiB,GAAA;QACrB,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,GAAG,CAChC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,CAAG,CAAD,MAAQ,CAAC,WAAW,EAAE,CAAC,CACtD,CAAA;QACD,IAAI,CAAC,QAAQ,GAAG,EAAE,CAAA;QAClB,IAAI,CAAC,UAAU,EAAE,CAAA;QACjB,OAAO,QAAQ,CAAA;IACjB,CAAC;IAED;;;;OAIG,CACH,GAAG,CAAC,IAAY,EAAE,GAAW,EAAE,IAAU,EAAA;QACvC,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,GAAG,EAAE,IAAI,CAAC,CAAA;IAC9B,CAAC;IAED;;OAEG,CACH,eAAe,GAAA;QACb,OAAQ,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;YAC1C,gMAAK,gBAAa,CAAC,UAAU;gBAC3B,kMAAO,mBAAgB,CAAC,UAAU,CAAA;YACpC,gMAAK,gBAAa,CAAC,IAAI;gBACrB,OAAO,8MAAgB,CAAC,IAAI,CAAA;YAC9B,gMAAK,gBAAa,CAAC,OAAO;gBACxB,kMAAO,mBAAgB,CAAC,OAAO,CAAA;YACjC;gBACE,OAAO,8MAAgB,CAAC,MAAM,CAAA;QAClC,CAAC;IACH,CAAC;IAED;;OAEG,CACH,WAAW,GAAA;QACT,OAAO,IAAI,CAAC,eAAe,EAAE,+LAAK,oBAAgB,CAAC,IAAI,CAAA;IACzD,CAAC;IAED,OAAO,CACL,KAAa,EACb,SAAiC;QAAE,MAAM,EAAE,CAAA,CAAE;IAAA,CAAE,EAAA;QAE/C,MAAM,aAAa,GAAG,CAAA,SAAA,EAAY,KAAK,EAAE,CAAA;QACzC,MAAM,MAAM,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC,IAAI,CACpC,CAAC,CAAkB,EAAE,CAAG,CAAD,AAAE,CAAC,KAAK,KAAK,aAAa,CAClD,CAAA;QAED,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,MAAM,IAAI,GAAG,8LAAI,UAAe,CAAC,CAAA,SAAA,EAAY,KAAK,EAAE,EAAE,MAAM,EAAE,IAAI,CAAC,CAAA;YACnE,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;YAExB,OAAO,IAAI,CAAA;QACb,CAAC,MAAM,CAAC;YACN,OAAO,MAAM,CAAA;QACf,CAAC;IACH,CAAC;IAED;;;;OAIG,CACH,IAAI,CAAC,IAAqB,EAAA;QACxB,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,OAAO,EAAE,GAAG,EAAE,GAAG,IAAI,CAAA;QAC3C,MAAM,QAAQ,GAAG,GAAG,EAAE;YACpB,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC,MAAW,EAAE,EAAE;;gBAChC,CAAA,KAAA,IAAI,CAAC,IAAI,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAE,IAAI,CAAC,MAAM,CAAC,CAAA;YACzB,CAAC,CAAC,CAAA;QACJ,CAAC,CAAA;QACD,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,GAAG,KAAK,CAAA,CAAA,EAAI,KAAK,CAAA,EAAA,EAAK,GAAG,CAAA,CAAA,CAAG,EAAE,OAAO,CAAC,CAAA;QACvD,IAAI,IAAI,CAAC,WAAW,EAAE,EAAE,CAAC;YACvB,QAAQ,EAAE,CAAA;QACZ,CAAC,MAAM,CAAC;YACN,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;QAChC,CAAC;IACH,CAAC;IAED;;;;;;;;OAQG,CACH,KAAK,CAAC,OAAO,CAAC,QAAuB,IAAI,EAAA;QACvC,IAAI,WAAW,GACb,KAAK,IACJ,IAAI,CAAC,WAAW,IAAI,AAAC,MAAM,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC,EAChD,IAAI,CAAC,gBAAgB,CAAA;QAEvB,IAAI,IAAI,CAAC,gBAAgB,IAAI,WAAW,EAAE,CAAC;YACzC,IAAI,CAAC,gBAAgB,GAAG,WAAW,CAAA;YACnC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;gBAChC,WAAW,IACT,OAAO,CAAC,iBAAiB,CAAC;oBACxB,YAAY,EAAE,WAAW;oBACzB,OAAO,EAAE,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC;iBACvD,CAAC,CAAA;gBAEJ,IAAI,OAAO,CAAC,UAAU,IAAI,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC;oBAC9C,OAAO,CAAC,KAAK,4LAAC,iBAAc,CAAC,YAAY,EAAE;wBACzC,YAAY,EAAE,WAAW;qBAC1B,CAAC,CAAA;gBACJ,CAAC;YACH,CAAC,CAAC,CAAA;QACJ,CAAC;IACH,CAAC;IACD;;OAEG,CACH,KAAK,CAAC,aAAa,GAAA;;QACjB,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,EAAE,CAAC;YACxB,IAAI,CAAC,iBAAiB,CAAC,cAAc,CAAC,CAAA;YACtC,OAAM;QACR,CAAC;QACD,IAAI,IAAI,CAAC,mBAAmB,EAAE,CAAC;YAC7B,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAA;YAC/B,IAAI,CAAC,GAAG,CACN,WAAW,EACX,0DAA0D,CAC3D,CAAA;YACD,IAAI,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAA;YACjC,CAAA,KAAA,IAAI,CAAC,IAAI,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAE,KAAK,CAAC,6MAAe,EAAE,kBAAkB,CAAC,CAAA;YACrD,OAAM;QACR,CAAC;QACD,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAA;QAC1C,IAAI,CAAC,IAAI,CAAC;YACR,KAAK,EAAE,SAAS;YAChB,KAAK,EAAE,WAAW;YAClB,OAAO,EAAE,CAAA,CAAE;YACX,GAAG,EAAE,IAAI,CAAC,mBAAmB;SAC9B,CAAC,CAAA;QACF,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAA;QAC9B,MAAM,IAAI,CAAC,OAAO,EAAE,CAAA;IACtB,CAAC;IAED,WAAW,CAAC,QAA2C,EAAA;QACrD,IAAI,CAAC,iBAAiB,GAAG,QAAQ,CAAA;IACnC,CAAC;IACD;;OAEG,CACH,eAAe,GAAA;QACb,IAAI,IAAI,CAAC,WAAW,EAAE,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACrD,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,CAAG,CAAD,OAAS,EAAE,CAAC,CAAA;YACjD,IAAI,CAAC,UAAU,GAAG,EAAE,CAAA;QACtB,CAAC;IACH,CAAC;IAsBD;;;;OAIG,CACH,QAAQ,GAAA;QACN,IAAI,MAAM,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC,CAAA;QACzB,IAAI,MAAM,KAAK,IAAI,CAAC,GAAG,EAAE,CAAC;YACxB,IAAI,CAAC,GAAG,GAAG,CAAC,CAAA;QACd,CAAC,MAAM,CAAC;YACN,IAAI,CAAC,GAAG,GAAG,MAAM,CAAA;QACnB,CAAC;QAED,OAAO,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAA;IAC5B,CAAC;IAED;;;;OAIG,CACH,eAAe,CAAC,KAAa,EAAA;QAC3B,IAAI,UAAU,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CACjC,CAAC,CAAC,EAAE,CAAG,CAAD,AAAE,CAAC,KAAK,KAAK,KAAK,IAAI,CAAC,CAAC,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC,UAAU,EAAE,CAAC,CAC9D,CAAA;QACD,IAAI,UAAU,EAAE,CAAC;YACf,IAAI,CAAC,GAAG,CAAC,WAAW,EAAE,CAAA,yBAAA,EAA4B,KAAK,CAAA,CAAA,CAAG,CAAC,CAAA;YAC3D,UAAU,CAAC,WAAW,EAAE,CAAA;QAC1B,CAAC;IACH,CAAC;IAED;;;;;;OAMG,CACH,OAAO,CAAC,OAAwB,EAAA;QAC9B,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAG,CAAC,AAAF,CAAG,KAAK,KAAK,OAAO,CAAC,KAAK,CAAC,CAAA;IACxE,CAAC;IAED;;;;OAIG,CACK,eAAe,GAAA;QACrB,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC;YACd,IAAI,CAAC,IAAI,CAAC,UAAU,GAAG,aAAa,CAAA;YACpC,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,GAAG,CAAG,CAAD,GAAK,CAAC,WAAW,EAAE,CAAA;YAC3C,IAAI,CAAC,IAAI,CAAC,OAAO,GAAG,CAAC,KAAyB,EAAE,CAC9C,CADgD,GAC5C,CAAC,YAAY,CAAC,KAA2B,CAAC,CAAA;YAChD,IAAI,CAAC,IAAI,CAAC,SAAS,GAAG,CAAC,KAAU,EAAE,CAAG,CAAD,GAAK,CAAC,cAAc,CAAC,KAAK,CAAC,CAAA;YAChE,IAAI,CAAC,IAAI,CAAC,OAAO,GAAG,CAAC,KAAU,EAAE,CAAG,CAAD,GAAK,CAAC,YAAY,CAAC,KAAK,CAAC,CAAA;QAC9D,CAAC;IACH,CAAC;IAED,cAAA,EAAgB,CACR,cAAc,CAAC,UAAyB,EAAA;QAC9C,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,IAAI,EAAE,CAAC,GAAoB,EAAE,EAAE;YACpD,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,OAAO,EAAE,GAAG,EAAE,GAAG,GAAG,CAAA;YAExC,IAAI,KAAK,KAAK,SAAS,IAAI,KAAK,KAAK,WAAW,EAAE,CAAC;gBACjD,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,OAAO,CAAC,MAAM,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,CAAA;YACrE,CAAC;YAED,IAAI,GAAG,IAAI,GAAG,KAAK,IAAI,CAAC,mBAAmB,EAAE,CAAC;gBAC5C,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAA;YACjC,CAAC;YAED,IAAI,CAAC,GAAG,CACN,SAAS,EACT,GAAG,OAAO,CAAC,MAAM,IAAI,EAAE,CAAA,CAAA,EAAI,KAAK,CAAA,CAAA,EAAI,KAAK,CAAA,CAAA,EACtC,AAAD,GAAI,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,GAAI,EAC9B,EAAE,EACF,OAAO,CACR,CAAA;YAED,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CACtB,MAAM,CAAC,CAAC,OAAwB,EAAE,CAAG,CAAD,MAAQ,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAC9D,OAAO,CAAC,CAAC,OAAwB,EAAE,CAClC,CADoC,MAC7B,CAAC,QAAQ,CAAC,KAAK,EAAE,OAAO,EAAE,GAAG,CAAC,CACtC,CAAA;YAEH,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,CAAG,CAAD,OAAS,CAAC,GAAG,CAAC,CAAC,CAAA;QACxE,CAAC,CAAC,CAAA;IACJ,CAAC;IAED,cAAA,EAAgB,CACR,WAAW,GAAA;QACjB,IAAI,CAAC,GAAG,CAAC,WAAW,EAAE,CAAA,aAAA,EAAgB,IAAI,CAAC,WAAW,EAAE,EAAE,CAAC,CAAA;QAC3D,IAAI,CAAC,eAAe,EAAE,CAAA;QACtB,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,CAAA;QAC3B,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;YACjB,IAAI,CAAC,cAAc,IAAI,aAAa,CAAC,IAAI,CAAC,cAAc,CAAC,CAAA;YACzD,IAAI,CAAC,cAAc,GAAG,WAAW,CAC/B,GAAG,CAAG,CAAD,GAAK,CAAC,aAAa,EAAE,EAC1B,IAAI,CAAC,mBAAmB,CACzB,CAAA;QACH,CAAC,MAAM,CAAC;YACN,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;gBACnB,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAA,yBAAA,EAA4B,IAAI,CAAC,SAAS,EAAE,CAAC,CAAA;YAClE,CAAC,MAAM,CAAC;gBACN,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAA,uBAAA,CAAyB,CAAC,CAAA;YAC/C,CAAC;YACD,MAAM,SAAS,GAAG,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,SAAU,CAAC,CAAA;YACxD,IAAI,CAAC,SAAS,GAAG,IAAI,MAAM,CAAC,SAAS,CAAC,CAAA;YACtC,IAAI,CAAC,SAAS,CAAC,OAAO,GAAG,CAAC,KAAK,EAAE,EAAE;gBACjC,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE,cAAc,EAAG,KAAoB,CAAC,OAAO,CAAC,CAAA;gBACjE,IAAI,CAAC,SAAU,CAAC,SAAS,EAAE,CAAA;YAC7B,CAAC,CAAA;YACD,IAAI,CAAC,SAAS,CAAC,SAAS,GAAG,CAAC,KAAK,EAAE,EAAE;gBACnC,IAAI,KAAK,CAAC,IAAI,CAAC,KAAK,KAAK,WAAW,EAAE,CAAC;oBACrC,IAAI,CAAC,aAAa,EAAE,CAAA;gBACtB,CAAC;YACH,CAAC,CAAA;YACD,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC;gBACzB,KAAK,EAAE,OAAO;gBACd,QAAQ,EAAE,IAAI,CAAC,mBAAmB;aACnC,CAAC,CAAA;QACJ,CAAC;QACD,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,CAAG,CAAD,OAAS,EAAE,CAAC,CAAA;IAClE,CAAC;IAED,cAAA,EAAgB,CACR,YAAY,CAAC,KAAU,EAAA;QAC7B,IAAI,CAAC,GAAG,CAAC,WAAW,EAAE,OAAO,EAAE,KAAK,CAAC,CAAA;QACrC,IAAI,CAAC,iBAAiB,EAAE,CAAA;QACxB,IAAI,CAAC,cAAc,IAAI,aAAa,CAAC,IAAI,CAAC,cAAc,CAAC,CAAA;QACzD,IAAI,CAAC,cAAc,CAAC,eAAe,EAAE,CAAA;QACrC,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,CAAG,CAAD,OAAS,CAAC,KAAK,CAAC,CAAC,CAAA;IACxE,CAAC;IAED,cAAA,EAAgB,CACR,YAAY,CAAC,KAAyB,EAAA;QAC5C,IAAI,CAAC,GAAG,CAAC,WAAW,EAAE,KAAK,CAAC,OAAO,CAAC,CAAA;QACpC,IAAI,CAAC,iBAAiB,EAAE,CAAA;QACxB,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,CAAG,CAAD,OAAS,CAAC,KAAK,CAAC,CAAC,CAAA;IACxE,CAAC;IAED,cAAA,EAAgB,CACR,iBAAiB,GAAA;QACvB,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,OAAwB,EAAE,CAC/C,CADiD,MAC1C,CAAC,QAAQ,4LAAC,iBAAc,CAAC,KAAK,CAAC,CACvC,CAAA;IACH,CAAC;IAED,cAAA,EAAgB,CACR,aAAa,CACnB,GAAW,EACX,MAAiC,EAAA;QAEjC,IAAI,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACrC,OAAO,GAAG,CAAA;QACZ,CAAC;QACD,MAAM,MAAM,GAAG,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAA;QAC1C,MAAM,KAAK,GAAG,IAAI,eAAe,CAAC,MAAM,CAAC,CAAA;QACzC,OAAO,GAAG,GAAG,GAAG,MAAM,GAAG,KAAK,EAAE,CAAA;IAClC,CAAC;IAEO,gBAAgB,CAAC,GAAuB,EAAA;QAC9C,IAAI,UAAkB,CAAA;QACtB,IAAI,GAAG,EAAE,CAAC;YACR,UAAU,GAAG,GAAG,CAAA;QAClB,CAAC,MAAM,CAAC;YACN,MAAM,IAAI,GAAG,IAAI,IAAI,CAAC;gBAAC,aAAa;aAAC,EAAE;gBAAE,IAAI,EAAE,wBAAwB;YAAA,CAAE,CAAC,CAAA;YAC1E,UAAU,GAAG,GAAG,CAAC,eAAe,CAAC,IAAI,CAAC,CAAA;QACxC,CAAC;QACD,OAAO,UAAU,CAAA;IACnB,CAAC;CACF;AAED,MAAM,gBAAgB;IAWpB,YACE,OAAe,EACf,UAAqB,EACrB,OAA4B,CAAA;QAb9B,IAAA,CAAA,UAAU,GAAW,aAAa,CAAA;QAElC,IAAA,CAAA,OAAO,GAAa,GAAG,EAAE,AAAE,CAAC,CAAA;QAC5B,IAAA,CAAA,OAAO,GAAa,GAAG,EAAE,AAAE,CAAC,CAAA;QAC5B,IAAA,CAAA,SAAS,GAAa,GAAG,EAAE,AAAE,CAAC,CAAA;QAC9B,IAAA,CAAA,MAAM,GAAa,GAAG,EAAE,AAAE,CAAC,CAAA;QAC3B,IAAA,CAAA,UAAU,8LAAW,gBAAa,CAAC,UAAU,CAAA;QAC7C,IAAA,CAAA,IAAI,GAAa,GAAG,EAAE,AAAE,CAAC,CAAA;QACzB,IAAA,CAAA,GAAG,GAAwB,IAAI,CAAA;QAO7B,IAAI,CAAC,GAAG,GAAG,OAAO,CAAA;QAClB,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,CAAA;IAC5B,CAAC;CACF"}}, {"offset": {"line": 3204, "column": 0}, "map": {"version": 3, "file": "index.js", "sources": ["../../src/index.ts"], "sourceRoot": "", "names": [], "mappings": ";AAAA,OAAO,cAIN,MAAM,kBAAkB,CAAA;AACzB,OAAO,eAAe,EAAE,EAQtB,qBAAqB,EACrB,sCAAsC,EACtC,yBAAyB,EACzB,uBAAuB,GACxB,MAAM,mBAAmB,CAAA;AAC1B,OAAO,gBAAgB,EAAE,EAIvB,+BAA+B,GAChC,MAAM,oBAAoB,CAAA"}}, {"offset": {"line": 3240, "column": 0}, "map": {"version": 3, "file": "errors.js", "sources": ["../../../src/lib/errors.ts"], "sourceRoot": "", "names": [], "mappings": ";;;;;;AAAM,MAAO,YAAa,SAAQ,KAAK;IAGrC,YAAY,OAAe,CAAA;QACzB,KAAK,CAAC,OAAO,CAAC,CAAA;QAHN,IAAA,CAAA,gBAAgB,GAAG,IAAI,CAAA;QAI/B,IAAI,CAAC,IAAI,GAAG,cAAc,CAAA;IAC5B,CAAC;CACF;AAEK,SAAU,cAAc,CAAC,KAAc;IAC3C,OAAO,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,KAAK,IAAI,IAAI,kBAAkB,IAAI,KAAK,CAAA;AACnF,CAAC;AAEK,MAAO,eAAgB,SAAQ,YAAY;IAG/C,YAAY,OAAe,EAAE,MAAc,CAAA;QACzC,KAAK,CAAC,OAAO,CAAC,CAAA;QACd,IAAI,CAAC,IAAI,GAAG,iBAAiB,CAAA;QAC7B,IAAI,CAAC,MAAM,GAAG,MAAM,CAAA;IACtB,CAAC;IAED,MAAM,GAAA;QACJ,OAAO;YACL,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,MAAM,EAAE,IAAI,CAAC,MAAM;SACpB,CAAA;IACH,CAAC;CACF;AAEK,MAAO,mBAAoB,SAAQ,YAAY;IAGnD,YAAY,OAAe,EAAE,aAAsB,CAAA;QACjD,KAAK,CAAC,OAAO,CAAC,CAAA;QACd,IAAI,CAAC,IAAI,GAAG,qBAAqB,CAAA;QACjC,IAAI,CAAC,aAAa,GAAG,aAAa,CAAA;IACpC,CAAC;CACF"}}, {"offset": {"line": 3283, "column": 0}, "map": {"version": 3, "file": "helpers.js", "sources": ["../../../src/lib/helpers.ts"], "sourceRoot": "", "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEO,MAAM,YAAY,GAAG,CAAC,WAAmB,EAAS,EAAE;IACzD,IAAI,MAAa,CAAA;IACjB,IAAI,WAAW,EAAE;QACf,MAAM,GAAG,WAAW,CAAA;KACrB,MAAM,IAAI,OAAO,KAAK,KAAK,WAAW,EAAE;QACvC,MAAM,GAAG,CAAC,GAAG,IAAI,EAAE,CACjB,CADmB,KACb,CAAC,sBAA6B,CAAC,+GAAC,IAAI,CAAC,CAAC,EAAE,OAAO,EAAE,MAAK,EAAE,EAAE,CAAG,CAAD,IAAM,CAAC,IAAG,IAAI,CAAC,CAAC,CAAA;KACrF,MAAM;QACL,MAAM,GAAG,KAAK,CAAA;KACf;IACD,OAAO,CAAC,GAAG,IAAI,EAAE,CAAG,CAAD,KAAO,CAAC,GAAG,IAAI,CAAC,CAAA;AACrC,CAAC,CAAA;AAEM,MAAM,eAAe,GAAG,GAAmC,CAAE,CAAA,SAAA,KAAA,GAAA,KAAA,GAAA,KAAA,GAAA;QAClE,IAAI,OAAO,QAAQ,KAAK,WAAW,EAAE;YACnC,aAAa;YACb,OAAO,CAAC,MAAM,MAAM,CAAC,sBAA6B,+GAAC,CAAC,CAAC,QAAQ,CAAA;SAC9D;QAED,OAAO,QAAQ,CAAA;IACjB,CAAC,CAAA,CAAA;AAEM,MAAM,gBAAgB,GAAG,CAAC,IAAyB,EAAW,EAAE;IACrE,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;QACvB,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAG,CAAD,eAAiB,CAAC,EAAE,CAAC,CAAC,CAAA;KAC9C,MAAM,IAAI,OAAO,IAAI,KAAK,UAAU,IAAI,IAAI,KAAK,MAAM,CAAC,IAAI,CAAC,EAAE;QAC9D,OAAO,IAAI,CAAA;KACZ;IAED,MAAM,MAAM,GAAwB,CAAA,CAAE,CAAA;IACtC,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,EAAE,EAAE;QAC5C,MAAM,MAAM,GAAG,GAAG,CAAC,OAAO,CAAC,eAAe,EAAE,CAAC,CAAC,EAAE,CAAG,CAAD,AAAE,CAAC,WAAW,EAAE,CAAC,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,CAAA;QACxF,MAAM,CAAC,MAAM,CAAC,GAAG,gBAAgB,CAAC,KAAK,CAAC,CAAA;IAC1C,CAAC,CAAC,CAAA;IAEF,OAAO,MAAM,CAAA;AACf,CAAC,CAAA"}}, {"offset": {"line": 3352, "column": 0}, "map": {"version": 3, "file": "fetch.js", "sources": ["../../../src/lib/fetch.ts"], "sourceRoot": "", "names": [], "mappings": ";;;;;;;AAAA,OAAO,EAAE,eAAe,EAAE,mBAAmB,EAAE,MAAM,UAAU,CAAA;AAC/D,OAAO,EAAE,eAAe,EAAE,MAAM,WAAW,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAc3C,MAAM,gBAAgB,GAAG,CAAC,GAAQ,EAAU,CAC1C,CAD4C,EACzC,CAAC,GAAG,IAAI,GAAG,CAAC,OAAO,IAAI,GAAG,CAAC,iBAAiB,IAAI,GAAG,CAAC,KAAK,IAAI,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAA;AAErF,MAAM,WAAW,GAAG,CAClB,KAAc,EACd,MAA8B,EAC9B,OAAsB,EACtB,CAAE,CAAA,SAAA,KAAA,GAAA,KAAA,GAAA,KAAA,GAAA;QACF,MAAM,GAAG,GAAG,kMAAM,kBAAA,AAAe,EAAE,CAAA;QAEnC,IAAI,KAAK,YAAY,GAAG,IAAI,CAAC,CAAA,OAAO,KAAA,QAAP,OAAO,KAAA,KAAA,IAAA,KAAA,IAAP,OAAO,CAAE,aAAa,CAAA,EAAE;YACnD,KAAK,CACF,IAAI,EAAE,CACN,IAAI,CAAC,CAAC,GAAG,EAAE,EAAE;gBACZ,MAAM,CAAC,2LAAI,kBAAe,CAAC,gBAAgB,CAAC,GAAG,CAAC,EAAE,KAAK,CAAC,MAAM,IAAI,GAAG,CAAC,CAAC,CAAA;YACzE,CAAC,CAAC,CACD,KAAK,CAAC,CAAC,GAAG,EAAE,EAAE;gBACb,MAAM,CAAC,2LAAI,sBAAmB,CAAC,gBAAgB,CAAC,GAAG,CAAC,EAAE,GAAG,CAAC,CAAC,CAAA;YAC7D,CAAC,CAAC,CAAA;SACL,MAAM;YACL,MAAM,CAAC,2LAAI,sBAAmB,CAAC,gBAAgB,CAAC,KAAK,CAAC,EAAE,KAAK,CAAC,CAAC,CAAA;SAChE;IACH,CAAC,CAAA,CAAA;AAED,MAAM,iBAAiB,GAAG,CACxB,MAAyB,EACzB,OAAsB,EACtB,UAA4B,EAC5B,IAAa,EACb,EAAE;IACF,MAAM,MAAM,GAAyB;QAAE,MAAM;QAAE,OAAO,EAAE,CAAA,OAAO,KAAA,QAAP,OAAO,KAAA,KAAA,IAAA,KAAA,IAAP,OAAO,CAAE,OAAO,KAAI,CAAA,CAAE;IAAA,CAAE,CAAA;IAEhF,IAAI,MAAM,KAAK,KAAK,EAAE;QACpB,OAAO,MAAM,CAAA;KACd;IAED,MAAM,CAAC,OAAO,GAAA,OAAA,MAAA,CAAA;QAAK,cAAc,EAAE,kBAAkB;IAAA,GAAK,OAAO,KAAA,QAAP,OAAO,KAAA,KAAA,IAAA,KAAA,IAAP,OAAO,CAAE,OAAO,CAAE,CAAA;IAE5E,IAAI,IAAI,EAAE;QACR,MAAM,CAAC,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA;KACnC;IACD,OAAA,OAAA,MAAA,CAAA,OAAA,MAAA,CAAA,CAAA,GAAY,MAAM,GAAK,UAAU,EAAE;AACrC,CAAC,CAAA;AAED,SAAe,cAAc,CAC3B,OAAc,EACd,MAAyB,EACzB,GAAW,EACX,OAAsB,EACtB,UAA4B,EAC5B,IAAa;;QAEb,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACrC,OAAO,CAAC,GAAG,EAAE,iBAAiB,CAAC,MAAM,EAAE,OAAO,EAAE,UAAU,EAAE,IAAI,CAAC,CAAC,CAC/D,IAAI,CAAC,CAAC,MAAM,EAAE,EAAE;gBACf,IAAI,CAAC,MAAM,CAAC,EAAE,EAAE,MAAM,MAAM,CAAA;gBAC5B,IAAI,OAAO,KAAA,QAAP,OAAO,KAAA,KAAA,IAAA,KAAA,IAAP,OAAO,CAAE,aAAa,EAAE,OAAO,MAAM,CAAA;gBACzC,OAAO,MAAM,CAAC,IAAI,EAAE,CAAA;YACtB,CAAC,CAAC,CACD,IAAI,CAAC,CAAC,IAAI,EAAE,CAAG,CAAD,MAAQ,CAAC,IAAI,CAAC,CAAC,CAC7B,KAAK,CAAC,CAAC,KAAK,EAAE,CAAG,CAAD,UAAY,CAAC,KAAK,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC,CAAA;QAC1D,CAAC,CAAC,CAAA;IACJ,CAAC;CAAA;AAEK,SAAgB,GAAG,CACvB,OAAc,EACd,GAAW,EACX,OAAsB,EACtB,UAA4B;;QAE5B,OAAO,cAAc,CAAC,OAAO,EAAE,KAAK,EAAE,GAAG,EAAE,OAAO,EAAE,UAAU,CAAC,CAAA;IACjE,CAAC;CAAA;AAEK,SAAgB,IAAI,CACxB,OAAc,EACd,GAAW,EACX,IAAY,EACZ,OAAsB,EACtB,UAA4B;;QAE5B,OAAO,cAAc,CAAC,OAAO,EAAE,MAAM,EAAE,GAAG,EAAE,OAAO,EAAE,UAAU,EAAE,IAAI,CAAC,CAAA;IACxE,CAAC;CAAA;AAEK,SAAgB,GAAG,CACvB,OAAc,EACd,GAAW,EACX,IAAY,EACZ,OAAsB,EACtB,UAA4B;;QAE5B,OAAO,cAAc,CAAC,OAAO,EAAE,KAAK,EAAE,GAAG,EAAE,OAAO,EAAE,UAAU,EAAE,IAAI,CAAC,CAAA;IACvE,CAAC;CAAA;AAEK,SAAgB,IAAI,CACxB,OAAc,EACd,GAAW,EACX,OAAsB,EACtB,UAA4B;;QAE5B,OAAO,cAAc,CACnB,OAAO,EACP,MAAM,EACN,GAAG,EAAA,OAAA,MAAA,CAAA,OAAA,MAAA,CAAA,CAAA,GAEE,OAAO,GAAA;YACV,aAAa,EAAE,IAAI;QAAA,IAErB,UAAU,CACX,CAAA;IACH,CAAC;CAAA;AAEK,SAAgB,MAAM,CAC1B,OAAc,EACd,GAAW,EACX,IAAY,EACZ,OAAsB,EACtB,UAA4B;;QAE5B,OAAO,cAAc,CAAC,OAAO,EAAE,QAAQ,EAAE,GAAG,EAAE,OAAO,EAAE,UAAU,EAAE,IAAI,CAAC,CAAA;IAC1E,CAAC;CAAA"}}, {"offset": {"line": 3463, "column": 0}, "map": {"version": 3, "file": "StorageFileApi.js", "sources": ["../../../src/packages/StorageFileApi.ts"], "sourceRoot": "", "names": [], "mappings": ";;;AA2xBe;AA3xBf,OAAO,EAAE,cAAc,EAAE,YAAY,EAAE,mBAAmB,EAAE,MAAM,eAAe,CAAA;AACjF,OAAO,EAAS,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,cAAc,CAAA;AAC7D,OAAO,EAAE,gBAAgB,EAAE,YAAY,EAAE,MAAM,gBAAgB,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAY/D,MAAM,sBAAsB,GAAG;IAC7B,KAAK,EAAE,GAAG;IACV,MAAM,EAAE,CAAC;IACT,MAAM,EAAE;QACN,MAAM,EAAE,MAAM;QACd,KAAK,EAAE,KAAK;KACb;CACF,CAAA;AAED,MAAM,oBAAoB,GAAgB;IACxC,YAAY,EAAE,MAAM;IACpB,WAAW,EAAE,0BAA0B;IACvC,MAAM,EAAE,KAAK;CACd,CAAA;AAca,MAAO,cAAc;IAMjC,YACE,GAAW,EACX,UAAqC,CAAA,CAAE,EACvC,QAAiB,EACjB,KAAa,CAAA;QAEb,IAAI,CAAC,GAAG,GAAG,GAAG,CAAA;QACd,IAAI,CAAC,OAAO,GAAG,OAAO,CAAA;QACtB,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAA;QACxB,IAAI,CAAC,KAAK,+LAAG,eAAA,AAAY,EAAC,KAAK,CAAC,CAAA;IAClC,CAAC;IAED;;;;;;OAMG,CACW,cAAc,CAC1B,MAAsB,EACtB,IAAY,EACZ,QAAkB,EAClB,WAAyB,EAAA;;YAWzB,IAAI;gBACF,IAAI,IAAI,CAAA;gBACR,MAAM,OAAO,GAAA,OAAA,MAAA,CAAA,OAAA,MAAA,CAAA,CAAA,GAAQ,oBAAoB,GAAK,WAAW,CAAE,CAAA;gBAC3D,IAAI,OAAO,GAAA,OAAA,MAAA,CAAA,OAAA,MAAA,CAAA,CAAA,GACN,IAAI,CAAC,OAAO,GACZ,AAAC,MAAM,KAAK,MAAM,IAAI;oBAAE,UAAU,EAAE,MAAM,CAAC,OAAO,CAAC,MAAiB,CAAC;gBAAA,CAAE,CAAC,CAC5E,CAAA;gBAED,MAAM,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAA;gBAEjC,IAAI,OAAO,IAAI,KAAK,WAAW,IAAI,QAAQ,YAAY,IAAI,EAAE;oBAC3D,IAAI,GAAG,IAAI,QAAQ,EAAE,CAAA;oBACrB,IAAI,CAAC,MAAM,CAAC,cAAc,EAAE,OAAO,CAAC,YAAsB,CAAC,CAAA;oBAC3D,IAAI,QAAQ,EAAE;wBACZ,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC,CAAA;qBACvD;oBACD,IAAI,CAAC,MAAM,CAAC,EAAE,EAAE,QAAQ,CAAC,CAAA;iBAC1B,MAAM,IAAI,OAAO,QAAQ,KAAK,WAAW,IAAI,QAAQ,YAAY,QAAQ,EAAE;oBAC1E,IAAI,GAAG,QAAQ,CAAA;oBACf,IAAI,CAAC,MAAM,CAAC,cAAc,EAAE,OAAO,CAAC,YAAsB,CAAC,CAAA;oBAC3D,IAAI,QAAQ,EAAE;wBACZ,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC,CAAA;qBACvD;iBACF,MAAM;oBACL,IAAI,GAAG,QAAQ,CAAA;oBACf,OAAO,CAAC,eAAe,CAAC,GAAG,CAAA,QAAA,EAAW,OAAO,CAAC,YAAY,EAAE,CAAA;oBAC5D,OAAO,CAAC,cAAc,CAAC,GAAG,OAAO,CAAC,WAAqB,CAAA;oBAEvD,IAAI,QAAQ,EAAE;wBACZ,OAAO,CAAC,YAAY,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC,CAAA;qBACrE;iBACF;gBAED,IAAI,WAAW,KAAA,QAAX,WAAW,KAAA,KAAA,IAAA,KAAA,IAAX,WAAW,CAAE,OAAO,EAAE;oBACxB,OAAO,GAAA,OAAA,MAAA,CAAA,OAAA,MAAA,CAAA,CAAA,GAAQ,OAAO,GAAK,WAAW,CAAC,OAAO,CAAE,CAAA;iBACjD;gBAED,MAAM,SAAS,GAAG,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAA;gBAChD,MAAM,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,CAAA;gBAC3C,MAAM,GAAG,GAAG,MAAM,IAAI,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,GAAG,CAAA,QAAA,EAAW,KAAK,EAAE,EAAA,OAAA,MAAA,CAAA;oBACxD,MAAM;oBACN,IAAI,EAAE,IAAgB;oBACtB,OAAO;gBAAA,GACJ,AAAC,CAAA,OAAO,KAAA,QAAP,OAAO,KAAA,KAAA,IAAA,KAAA,IAAP,OAAO,CAAE,MAAM,EAAC,CAAC,CAAC;oBAAE,MAAM,EAAE,OAAO,CAAC,MAAM;gBAAA,CAAE,CAAC,CAAC,CAAC,CAAA,CAAE,CAAC,EACtD,CAAA;gBAEF,MAAM,IAAI,GAAG,MAAM,GAAG,CAAC,IAAI,EAAE,CAAA;gBAE7B,IAAI,GAAG,CAAC,EAAE,EAAE;oBACV,OAAO;wBACL,IAAI,EAAE;4BAAE,IAAI,EAAE,SAAS;4BAAE,EAAE,EAAE,IAAI,CAAC,EAAE;4BAAE,QAAQ,EAAE,IAAI,CAAC,GAAG;wBAAA,CAAE;wBAC1D,KAAK,EAAE,IAAI;qBACZ,CAAA;iBACF,MAAM;oBACL,MAAM,KAAK,GAAG,IAAI,CAAA;oBAClB,OAAO;wBAAE,IAAI,EAAE,IAAI;wBAAE,KAAK;oBAAA,CAAE,CAAA;iBAC7B;aACF,CAAC,OAAO,KAAK,EAAE;gBACd,+LAAI,iBAAA,AAAc,EAAC,KAAK,CAAC,EAAE;oBACzB,OAAO;wBAAE,IAAI,EAAE,IAAI;wBAAE,KAAK;oBAAA,CAAE,CAAA;iBAC7B;gBAED,MAAM,KAAK,CAAA;aACZ;QACH,CAAC;KAAA;IAED;;;;;OAKG,CACG,MAAM,CACV,IAAY,EACZ,QAAkB,EAClB,WAAyB,EAAA;;YAWzB,OAAO,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,CAAC,CAAA;QACjE,CAAC;KAAA;IAED;;;;;OAKG,CACG,iBAAiB,CACrB,IAAY,EACZ,KAAa,EACb,QAAkB,EAClB,WAAyB,EAAA;;YAEzB,MAAM,SAAS,GAAG,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAA;YAChD,MAAM,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,CAAA;YAE3C,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,GAAG,GAAG,CAAA,oBAAA,EAAuB,KAAK,EAAE,CAAC,CAAA;YAC9D,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,OAAO,EAAE,KAAK,CAAC,CAAA;YAEpC,IAAI;gBACF,IAAI,IAAI,CAAA;gBACR,MAAM,OAAO,GAAA,OAAA,MAAA,CAAA;oBAAK,MAAM,EAAE,oBAAoB,CAAC,MAAM;gBAAA,GAAK,WAAW,CAAE,CAAA;gBACvE,MAAM,OAAO,GAAA,OAAA,MAAA,CAAA,OAAA,MAAA,CAAA,CAAA,GACR,IAAI,CAAC,OAAO,GACZ;oBAAE,UAAU,EAAE,MAAM,CAAC,OAAO,CAAC,MAAiB,CAAC;gBAAA,CAAE,CACrD,CAAA;gBAED,IAAI,OAAO,IAAI,KAAK,WAAW,IAAI,QAAQ,YAAY,IAAI,EAAE;oBAC3D,IAAI,GAAG,IAAI,QAAQ,EAAE,CAAA;oBACrB,IAAI,CAAC,MAAM,CAAC,cAAc,EAAE,OAAO,CAAC,YAAsB,CAAC,CAAA;oBAC3D,IAAI,CAAC,MAAM,CAAC,EAAE,EAAE,QAAQ,CAAC,CAAA;iBAC1B,MAAM,IAAI,OAAO,QAAQ,KAAK,WAAW,IAAI,QAAQ,YAAY,QAAQ,EAAE;oBAC1E,IAAI,GAAG,QAAQ,CAAA;oBACf,IAAI,CAAC,MAAM,CAAC,cAAc,EAAE,OAAO,CAAC,YAAsB,CAAC,CAAA;iBAC5D,MAAM;oBACL,IAAI,GAAG,QAAQ,CAAA;oBACf,OAAO,CAAC,eAAe,CAAC,GAAG,CAAA,QAAA,EAAW,OAAO,CAAC,YAAY,EAAE,CAAA;oBAC5D,OAAO,CAAC,cAAc,CAAC,GAAG,OAAO,CAAC,WAAqB,CAAA;iBACxD;gBAED,MAAM,GAAG,GAAG,MAAM,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,EAAE,EAAE;oBAC3C,MAAM,EAAE,KAAK;oBACb,IAAI,EAAE,IAAgB;oBACtB,OAAO;iBACR,CAAC,CAAA;gBAEF,MAAM,IAAI,GAAG,MAAM,GAAG,CAAC,IAAI,EAAE,CAAA;gBAE7B,IAAI,GAAG,CAAC,EAAE,EAAE;oBACV,OAAO;wBACL,IAAI,EAAE;4BAAE,IAAI,EAAE,SAAS;4BAAE,QAAQ,EAAE,IAAI,CAAC,GAAG;wBAAA,CAAE;wBAC7C,KAAK,EAAE,IAAI;qBACZ,CAAA;iBACF,MAAM;oBACL,MAAM,KAAK,GAAG,IAAI,CAAA;oBAClB,OAAO;wBAAE,IAAI,EAAE,IAAI;wBAAE,KAAK;oBAAA,CAAE,CAAA;iBAC7B;aACF,CAAC,OAAO,KAAK,EAAE;gBACd,+LAAI,iBAAA,AAAc,EAAC,KAAK,CAAC,EAAE;oBACzB,OAAO;wBAAE,IAAI,EAAE,IAAI;wBAAE,KAAK;oBAAA,CAAE,CAAA;iBAC7B;gBAED,MAAM,KAAK,CAAA;aACZ;QACH,CAAC;KAAA;IAED;;;;;;OAMG,CACG,qBAAqB,CACzB,IAAY,EACZ,OAA6B,EAAA;;YAW7B,IAAI;gBACF,IAAI,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAA;gBAEpC,MAAM,OAAO,GAAA,OAAA,MAAA,CAAA,CAAA,GAAQ,IAAI,CAAC,OAAO,CAAE,CAAA;gBAEnC,IAAI,OAAO,KAAA,QAAP,OAAO,KAAA,KAAA,IAAA,KAAA,IAAP,OAAO,CAAE,MAAM,EAAE;oBACnB,OAAO,CAAC,UAAU,CAAC,GAAG,MAAM,CAAA;iBAC7B;gBAED,MAAM,IAAI,GAAG,+LAAM,QAAA,AAAI,EACrB,IAAI,CAAC,KAAK,EACV,GAAG,IAAI,CAAC,GAAG,CAAA,oBAAA,EAAuB,KAAK,EAAE,EACzC,CAAA,CAAE,EACF;oBAAE,OAAO;gBAAA,CAAE,CACZ,CAAA;gBAED,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,CAAA;gBAExC,MAAM,KAAK,GAAG,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,OAAO,CAAC,CAAA;gBAE3C,IAAI,CAAC,KAAK,EAAE;oBACV,MAAM,2LAAI,eAAY,CAAC,0BAA0B,CAAC,CAAA;iBACnD;gBAED,OAAO;oBAAE,IAAI,EAAE;wBAAE,SAAS,EAAE,GAAG,CAAC,QAAQ,EAAE;wBAAE,IAAI;wBAAE,KAAK;oBAAA,CAAE;oBAAE,KAAK,EAAE,IAAI;gBAAA,CAAE,CAAA;aACzE,CAAC,OAAO,KAAK,EAAE;gBACd,QAAI,wMAAA,AAAc,EAAC,KAAK,CAAC,EAAE;oBACzB,OAAO;wBAAE,IAAI,EAAE,IAAI;wBAAE,KAAK;oBAAA,CAAE,CAAA;iBAC7B;gBAED,MAAM,KAAK,CAAA;aACZ;QACH,CAAC;KAAA;IAED;;;;;OAKG,CACG,MAAM,CACV,IAAY,EACZ,QAUU,EACV,WAAyB,EAAA;;YAWzB,OAAO,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,CAAC,CAAA;QAChE,CAAC;KAAA;IAED;;;;;;OAMG,CACG,IAAI,CACR,QAAgB,EAChB,MAAc,EACd,OAA4B,EAAA;;YAW5B,IAAI;gBACF,MAAM,IAAI,GAAG,gMAAM,OAAA,AAAI,EACrB,IAAI,CAAC,KAAK,EACV,GAAG,IAAI,CAAC,GAAG,CAAA,YAAA,CAAc,EACzB;oBACE,QAAQ,EAAE,IAAI,CAAC,QAAQ;oBACvB,SAAS,EAAE,QAAQ;oBACnB,cAAc,EAAE,MAAM;oBACtB,iBAAiB,EAAE,OAAO,KAAA,QAAP,OAAO,KAAA,KAAA,IAAA,KAAA,IAAP,OAAO,CAAE,iBAAiB;iBAC9C,EACD;oBAAE,OAAO,EAAE,IAAI,CAAC,OAAO;gBAAA,CAAE,CAC1B,CAAA;gBACD,OAAO;oBAAE,IAAI;oBAAE,KAAK,EAAE,IAAI;gBAAA,CAAE,CAAA;aAC7B,CAAC,OAAO,KAAK,EAAE;gBACd,+LAAI,iBAAA,AAAc,EAAC,KAAK,CAAC,EAAE;oBACzB,OAAO;wBAAE,IAAI,EAAE,IAAI;wBAAE,KAAK;oBAAA,CAAE,CAAA;iBAC7B;gBAED,MAAM,KAAK,CAAA;aACZ;QACH,CAAC;KAAA;IAED;;;;;;OAMG,CACG,IAAI,CACR,QAAgB,EAChB,MAAc,EACd,OAA4B,EAAA;;YAW5B,IAAI;gBACF,MAAM,IAAI,GAAG,OAAM,gMAAA,AAAI,EACrB,IAAI,CAAC,KAAK,EACV,GAAG,IAAI,CAAC,GAAG,CAAA,YAAA,CAAc,EACzB;oBACE,QAAQ,EAAE,IAAI,CAAC,QAAQ;oBACvB,SAAS,EAAE,QAAQ;oBACnB,cAAc,EAAE,MAAM;oBACtB,iBAAiB,EAAE,OAAO,KAAA,QAAP,OAAO,KAAA,KAAA,IAAA,KAAA,IAAP,OAAO,CAAE,iBAAiB;iBAC9C,EACD;oBAAE,OAAO,EAAE,IAAI,CAAC,OAAO;gBAAA,CAAE,CAC1B,CAAA;gBACD,OAAO;oBAAE,IAAI,EAAE;wBAAE,IAAI,EAAE,IAAI,CAAC,GAAG;oBAAA,CAAE;oBAAE,KAAK,EAAE,IAAI;gBAAA,CAAE,CAAA;aACjD,CAAC,OAAO,KAAK,EAAE;gBACd,+LAAI,iBAAA,AAAc,EAAC,KAAK,CAAC,EAAE;oBACzB,OAAO;wBAAE,IAAI,EAAE,IAAI;wBAAE,KAAK;oBAAA,CAAE,CAAA;iBAC7B;gBAED,MAAM,KAAK,CAAA;aACZ;QACH,CAAC;KAAA;IAED;;;;;;;OAOG,CACG,eAAe,CACnB,IAAY,EACZ,SAAiB,EACjB,OAAuE,EAAA;;YAWvE,IAAI;gBACF,IAAI,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAA;gBAEpC,IAAI,IAAI,GAAG,OAAM,gMAAA,AAAI,EACnB,IAAI,CAAC,KAAK,EACV,GAAG,IAAI,CAAC,GAAG,CAAA,aAAA,EAAgB,KAAK,EAAE,EAAA,OAAA,MAAA,CAAA;oBAChC,SAAS;gBAAA,GAAK,AAAC,CAAA,OAAO,KAAA,QAAP,OAAO,KAAA,KAAA,IAAA,KAAA,IAAP,OAAO,CAAE,SAAS,EAAC,CAAC,CAAC;oBAAE,SAAS,EAAE,OAAO,CAAC,SAAS;gBAAA,CAAE,CAAC,CAAC,CAAC,CAAA,CAAE,CAAC,EAC5E;oBAAE,OAAO,EAAE,IAAI,CAAC,OAAO;gBAAA,CAAE,CAC1B,CAAA;gBACD,MAAM,kBAAkB,GAAG,CAAA,OAAO,KAAA,QAAP,OAAO,KAAA,KAAA,IAAA,KAAA,IAAP,OAAO,CAAE,QAAQ,IACxC,CAAA,UAAA,EAAa,OAAO,CAAC,QAAQ,KAAK,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,QAAQ,EAAE,GAChE,EAAE,CAAA;gBACN,MAAM,SAAS,GAAG,SAAS,CAAC,GAAG,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,SAAS,GAAG,kBAAkB,EAAE,CAAC,CAAA;gBAChF,IAAI,GAAG;oBAAE,SAAS;gBAAA,CAAE,CAAA;gBACpB,OAAO;oBAAE,IAAI;oBAAE,KAAK,EAAE,IAAI;gBAAA,CAAE,CAAA;aAC7B,CAAC,OAAO,KAAK,EAAE;gBACd,+LAAI,iBAAA,AAAc,EAAC,KAAK,CAAC,EAAE;oBACzB,OAAO;wBAAE,IAAI,EAAE,IAAI;wBAAE,KAAK;oBAAA,CAAE,CAAA;iBAC7B;gBAED,MAAM,KAAK,CAAA;aACZ;QACH,CAAC;KAAA;IAED;;;;;;OAMG,CACG,gBAAgB,CACpB,KAAe,EACf,SAAiB,EACjB,OAAwC,EAAA;;YAWxC,IAAI;gBACF,MAAM,IAAI,GAAG,gMAAM,OAAA,AAAI,EACrB,IAAI,CAAC,KAAK,EACV,GAAG,IAAI,CAAC,GAAG,CAAA,aAAA,EAAgB,IAAI,CAAC,QAAQ,EAAE,EAC1C;oBAAE,SAAS;oBAAE,KAAK;gBAAA,CAAE,EACpB;oBAAE,OAAO,EAAE,IAAI,CAAC,OAAO;gBAAA,CAAE,CAC1B,CAAA;gBAED,MAAM,kBAAkB,GAAG,CAAA,OAAO,KAAA,QAAP,OAAO,KAAA,KAAA,IAAA,KAAA,IAAP,OAAO,CAAE,QAAQ,IACxC,CAAA,UAAA,EAAa,OAAO,CAAC,QAAQ,KAAK,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,QAAQ,EAAE,GAChE,EAAE,CAAA;gBACN,OAAO;oBACL,IAAI,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,KAA4B,EAAE,CAAG,CAAD,CAAC,KAAA,MAAA,CAAA,OAAA,MAAA,CAAA,CAAA,GAC5C,KAAK,GAAA;4BACR,SAAS,EAAE,KAAK,CAAC,SAAS,GACtB,SAAS,CAAC,GAAG,IAAI,CAAC,GAAG,GAAG,KAAK,CAAC,SAAS,GAAG,kBAAkB,EAAE,CAAC,GAC/D,IAAI;wBAAA,GACR,CAAC;oBACH,KAAK,EAAE,IAAI;iBACZ,CAAA;aACF,CAAC,OAAO,KAAK,EAAE;gBACd,+LAAI,iBAAc,AAAd,EAAe,KAAK,CAAC,EAAE;oBACzB,OAAO;wBAAE,IAAI,EAAE,IAAI;wBAAE,KAAK;oBAAA,CAAE,CAAA;iBAC7B;gBAED,MAAM,KAAK,CAAA;aACZ;QACH,CAAC;KAAA;IAED;;;;;OAKG,CACG,QAAQ,CACZ,IAAY,EACZ,OAA0C,EAAA;;YAW1C,MAAM,mBAAmB,GAAG,OAAO,CAAA,OAAO,KAAA,QAAP,OAAO,KAAA,KAAA,IAAA,KAAA,IAAP,OAAO,CAAE,SAAS,CAAA,KAAK,WAAW,CAAA;YACrE,MAAM,UAAU,GAAG,mBAAmB,CAAC,CAAC,CAAC,4BAA4B,CAAC,CAAC,CAAC,QAAQ,CAAA;YAChF,MAAM,mBAAmB,GAAG,IAAI,CAAC,0BAA0B,CAAC,CAAA,OAAO,KAAA,QAAP,OAAO,KAAA,KAAA,IAAA,KAAA,IAAP,OAAO,CAAE,SAAS,KAAI,CAAA,CAAE,CAAC,CAAA;YACrF,MAAM,WAAW,GAAG,mBAAmB,CAAC,CAAC,CAAC,CAAA,CAAA,EAAI,mBAAmB,EAAE,CAAC,CAAC,CAAC,EAAE,CAAA;YAExE,IAAI;gBACF,MAAM,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAA;gBACtC,MAAM,GAAG,GAAG,gMAAM,MAAA,AAAG,EAAC,IAAI,CAAC,KAAK,EAAE,GAAG,IAAI,CAAC,GAAG,CAAA,CAAA,EAAI,UAAU,CAAA,CAAA,EAAI,KAAK,GAAG,WAAW,EAAE,EAAE;oBACpF,OAAO,EAAE,IAAI,CAAC,OAAO;oBACrB,aAAa,EAAE,IAAI;iBACpB,CAAC,CAAA;gBACF,MAAM,IAAI,GAAG,MAAM,GAAG,CAAC,IAAI,EAAE,CAAA;gBAC7B,OAAO;oBAAE,IAAI;oBAAE,KAAK,EAAE,IAAI;gBAAA,CAAE,CAAA;aAC7B,CAAC,OAAO,KAAK,EAAE;gBACd,+LAAI,iBAAA,AAAc,EAAC,KAAK,CAAC,EAAE;oBACzB,OAAO;wBAAE,IAAI,EAAE,IAAI;wBAAE,KAAK;oBAAA,CAAE,CAAA;iBAC7B;gBAED,MAAM,KAAK,CAAA;aACZ;QACH,CAAC;KAAA;IAED;;;OAGG,CACG,IAAI,CACR,IAAY,EAAA;;YAWZ,MAAM,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAA;YAEtC,IAAI;gBACF,MAAM,IAAI,GAAG,OAAM,+LAAA,AAAG,EAAC,IAAI,CAAC,KAAK,EAAE,GAAG,IAAI,CAAC,GAAG,CAAA,aAAA,EAAgB,KAAK,EAAE,EAAE;oBACrE,OAAO,EAAE,IAAI,CAAC,OAAO;iBACtB,CAAC,CAAA;gBAEF,OAAO;oBAAE,IAAI,8LAAE,mBAAA,AAAgB,EAAC,IAAI,CAA2B;oBAAE,KAAK,EAAE,IAAI;gBAAA,CAAE,CAAA;aAC/E,CAAC,OAAO,KAAK,EAAE;gBACd,+LAAI,iBAAA,AAAc,EAAC,KAAK,CAAC,EAAE;oBACzB,OAAO;wBAAE,IAAI,EAAE,IAAI;wBAAE,KAAK;oBAAA,CAAE,CAAA;iBAC7B;gBAED,MAAM,KAAK,CAAA;aACZ;QACH,CAAC;KAAA;IAED;;;OAGG,CACG,MAAM,CACV,IAAY,EAAA;;YAWZ,MAAM,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAA;YAEtC,IAAI;gBACF,UAAM,6LAAA,AAAI,EAAC,IAAI,CAAC,KAAK,EAAE,GAAG,IAAI,CAAC,GAAG,CAAA,QAAA,EAAW,KAAK,EAAE,EAAE;oBACpD,OAAO,EAAE,IAAI,CAAC,OAAO;iBACtB,CAAC,CAAA;gBAEF,OAAO;oBAAE,IAAI,EAAE,IAAI;oBAAE,KAAK,EAAE,IAAI;gBAAA,CAAE,CAAA;aACnC,CAAC,OAAO,KAAK,EAAE;gBACd,IAAI,4MAAc,AAAd,EAAe,KAAK,CAAC,IAAI,KAAK,mMAAY,sBAAmB,EAAE;oBACjE,MAAM,aAAa,GAAI,KAAK,CAAC,aAA+C,CAAA;oBAE5E,IAAI;wBAAC,GAAG;wBAAE,GAAG;qBAAC,CAAC,QAAQ,CAAC,aAAa,KAAA,QAAb,aAAa,KAAA,KAAA,IAAA,KAAA,IAAb,aAAa,CAAE,MAAM,CAAC,EAAE;wBAC9C,OAAO;4BAAE,IAAI,EAAE,KAAK;4BAAE,KAAK;wBAAA,CAAE,CAAA;qBAC9B;iBACF;gBAED,MAAM,KAAK,CAAA;aACZ;QACH,CAAC;KAAA;IAED;;;;;;;OAOG,CACH,YAAY,CACV,IAAY,EACZ,OAAuE,EAAA;QAEvE,MAAM,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAA;QACtC,MAAM,YAAY,GAAG,EAAE,CAAA;QAEvB,MAAM,kBAAkB,GAAG,CAAA,OAAO,KAAA,QAAP,OAAO,KAAA,KAAA,IAAA,KAAA,IAAP,OAAO,CAAE,QAAQ,IACxC,CAAA,SAAA,EAAY,OAAO,CAAC,QAAQ,KAAK,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,QAAQ,EAAE,GAC/D,EAAE,CAAA;QAEN,IAAI,kBAAkB,KAAK,EAAE,EAAE;YAC7B,YAAY,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAA;SACtC;QAED,MAAM,mBAAmB,GAAG,OAAO,CAAA,OAAO,KAAA,QAAP,OAAO,KAAA,KAAA,IAAA,KAAA,IAAP,OAAO,CAAE,SAAS,CAAA,KAAK,WAAW,CAAA;QACrE,MAAM,UAAU,GAAG,mBAAmB,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,QAAQ,CAAA;QAClE,MAAM,mBAAmB,GAAG,IAAI,CAAC,0BAA0B,CAAC,CAAA,OAAO,KAAA,QAAP,OAAO,KAAA,KAAA,IAAA,KAAA,IAAP,OAAO,CAAE,SAAS,KAAI,CAAA,CAAE,CAAC,CAAA;QAErF,IAAI,mBAAmB,KAAK,EAAE,EAAE;YAC9B,YAAY,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAA;SACvC;QAED,IAAI,WAAW,GAAG,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;QACxC,IAAI,WAAW,KAAK,EAAE,EAAE;YACtB,WAAW,GAAG,CAAA,CAAA,EAAI,WAAW,EAAE,CAAA;SAChC;QAED,OAAO;YACL,IAAI,EAAE;gBAAE,SAAS,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,GAAG,CAAA,CAAA,EAAI,UAAU,CAAA,QAAA,EAAW,KAAK,GAAG,WAAW,EAAE,CAAC;YAAA,CAAE;SAC1F,CAAA;IACH,CAAC;IAED;;;;OAIG,CACG,MAAM,CACV,KAAe,EAAA;;YAWf,IAAI;gBACF,MAAM,IAAI,GAAG,gMAAM,SAAA,AAAM,EACvB,IAAI,CAAC,KAAK,EACV,GAAG,IAAI,CAAC,GAAG,CAAA,QAAA,EAAW,IAAI,CAAC,QAAQ,EAAE,EACrC;oBAAE,QAAQ,EAAE,KAAK;gBAAA,CAAE,EACnB;oBAAE,OAAO,EAAE,IAAI,CAAC,OAAO;gBAAA,CAAE,CAC1B,CAAA;gBACD,OAAO;oBAAE,IAAI;oBAAE,KAAK,EAAE,IAAI;gBAAA,CAAE,CAAA;aAC7B,CAAC,OAAO,KAAK,EAAE;gBACd,QAAI,wMAAA,AAAc,EAAC,KAAK,CAAC,EAAE;oBACzB,OAAO;wBAAE,IAAI,EAAE,IAAI;wBAAE,KAAK;oBAAA,CAAE,CAAA;iBAC7B;gBAED,MAAM,KAAK,CAAA;aACZ;QACH,CAAC;KAAA;IAED;;;OAGG,CACH,qBAAqB;IACrB,eAAe;IACf,cAAc;IACd,QAAQ;IACR,uBAAuB;IACvB,oBAAoB;IACpB,QAAQ;IACR,QAAQ;IACR,mBAAmB;IACnB,4BAA4B;IAC5B,QAAQ;IACR,MAAM;IACN,UAAU;IACV,kGAAkG;IAClG,mCAAmC;IACnC,sBAAsB;IACtB,mCAAmC;IACnC,qCAAqC;IACrC,QAAQ;IAER,kBAAkB;IAClB,MAAM;IACN,IAAI;IAEJ;;;;OAIG,CACH,wBAAwB;IACxB,gBAAgB;IAChB,mBAAmB;IACnB,cAAc;IACd,QAAQ;IACR,uBAAuB;IACvB,oBAAoB;IACpB,QAAQ;IACR,QAAQ;IACR,mBAAmB;IACnB,4BAA4B;IAC5B,QAAQ;IACR,MAAM;IACN,UAAU;IACV,+BAA+B;IAC/B,oBAAoB;IACpB,sCAAsC;IACtC,qBAAqB;IACrB,kCAAkC;IAClC,QAAQ;IACR,mCAAmC;IACnC,sBAAsB;IACtB,mCAAmC;IACnC,qCAAqC;IACrC,QAAQ;IAER,kBAAkB;IAClB,MAAM;IACN,IAAI;IAEJ;;;OAGG,CACG,IAAI,CACR,IAAa,EACb,OAAuB,EACvB,UAA4B,EAAA;;YAW5B,IAAI;gBACF,MAAM,IAAI,GAAA,OAAA,MAAA,CAAA,OAAA,MAAA,CAAA,OAAA,MAAA,CAAA,CAAA,GAAQ,sBAAsB,GAAK,OAAO,GAAA;oBAAE,MAAM,EAAE,IAAI,IAAI,EAAE;gBAAA,EAAE,CAAA;gBAC1E,MAAM,IAAI,GAAG,gMAAM,OAAA,AAAI,EACrB,IAAI,CAAC,KAAK,EACV,GAAG,IAAI,CAAC,GAAG,CAAA,aAAA,EAAgB,IAAI,CAAC,QAAQ,EAAE,EAC1C,IAAI,EACJ;oBAAE,OAAO,EAAE,IAAI,CAAC,OAAO;gBAAA,CAAE,EACzB,UAAU,CACX,CAAA;gBACD,OAAO;oBAAE,IAAI;oBAAE,KAAK,EAAE,IAAI;gBAAA,CAAE,CAAA;aAC7B,CAAC,OAAO,KAAK,EAAE;gBACd,+LAAI,iBAAA,AAAc,EAAC,KAAK,CAAC,EAAE;oBACzB,OAAO;wBAAE,IAAI,EAAE,IAAI;wBAAE,KAAK;oBAAA,CAAE,CAAA;iBAC7B;gBAED,MAAM,KAAK,CAAA;aACZ;QACH,CAAC;KAAA;IAES,cAAc,CAAC,QAA6B,EAAA;QACpD,OAAO,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAA;IACjC,CAAC;IAED,QAAQ,CAAC,IAAY,EAAA;QACnB,IAAI,sIAAa,KAAK,WAAW,EAAE;YACjC,4HAAO,UAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAA;SAC5C;QACD,OAAO,IAAI,CAAC,IAAI,CAAC,CAAA;IACnB,CAAC;IAEO,aAAa,CAAC,IAAY,EAAA;QAChC,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAA,CAAA,EAAI,IAAI,EAAE,CAAA;IACnC,CAAC;IAEO,mBAAmB,CAAC,IAAY,EAAA;QACtC,OAAO,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAAA;IAC1D,CAAC;IAEO,0BAA0B,CAAC,SAA2B,EAAA;QAC5D,MAAM,MAAM,GAAG,EAAE,CAAA;QACjB,IAAI,SAAS,CAAC,KAAK,EAAE;YACnB,MAAM,CAAC,IAAI,CAAC,CAAA,MAAA,EAAS,SAAS,CAAC,KAAK,EAAE,CAAC,CAAA;SACxC;QAED,IAAI,SAAS,CAAC,MAAM,EAAE;YACpB,MAAM,CAAC,IAAI,CAAC,CAAA,OAAA,EAAU,SAAS,CAAC,MAAM,EAAE,CAAC,CAAA;SAC1C;QAED,IAAI,SAAS,CAAC,MAAM,EAAE;YACpB,MAAM,CAAC,IAAI,CAAC,CAAA,OAAA,EAAU,SAAS,CAAC,MAAM,EAAE,CAAC,CAAA;SAC1C;QAED,IAAI,SAAS,CAAC,MAAM,EAAE;YACpB,MAAM,CAAC,IAAI,CAAC,CAAA,OAAA,EAAU,SAAS,CAAC,MAAM,EAAE,CAAC,CAAA;SAC1C;QAED,IAAI,SAAS,CAAC,OAAO,EAAE;YACrB,MAAM,CAAC,IAAI,CAAC,CAAA,QAAA,EAAW,SAAS,CAAC,OAAO,EAAE,CAAC,CAAA;SAC5C;QAED,OAAO,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;IACzB,CAAC;CACF"}}, {"offset": {"line": 4128, "column": 0}, "map": {"version": 3, "file": "version.js", "sources": ["../../../src/lib/version.ts"], "sourceRoot": "", "names": [], "mappings": "AAAA,0BAA0B;;;;AACnB,MAAM,OAAO,GAAG,OAAO,CAAA"}}, {"offset": {"line": 4139, "column": 0}, "map": {"version": 3, "file": "constants.js", "sources": ["../../../src/lib/constants.ts"], "sourceRoot": "", "names": [], "mappings": ";;;AAAA,OAAO,EAAE,OAAO,EAAE,MAAM,WAAW,CAAA;;AAC5B,MAAM,eAAe,GAAG;IAAE,eAAe,EAAE,CAAA,WAAA,0LAAc,UAAO,EAAE;AAAA,CAAE,CAAA"}}, {"offset": {"line": 4153, "column": 0}, "map": {"version": 3, "file": "StorageBucketApi.js", "sources": ["../../../src/packages/StorageBucketApi.ts"], "sourceRoot": "", "names": [], "mappings": ";;;AAAA,OAAO,EAAE,eAAe,EAAE,MAAM,kBAAkB,CAAA;AAClD,OAAO,EAAE,cAAc,EAAgB,MAAM,eAAe,CAAA;AAC5D,OAAO,EAAS,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,MAAM,EAAE,MAAM,cAAc,CAAA;AAC5D,OAAO,EAAE,YAAY,EAAE,MAAM,gBAAgB,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAG/B,MAAO,gBAAgB;IAKnC,YAAY,GAAW,EAAE,UAAqC,CAAA,CAAE,EAAE,KAAa,CAAA;QAC7E,IAAI,CAAC,GAAG,GAAG,GAAG,CAAA;QACd,IAAI,CAAC,OAAO,GAAA,OAAA,MAAA,CAAA,OAAA,MAAA,CAAA,CAAA,6LAAQ,kBAAe,GAAK,OAAO,CAAE,CAAA;QACjD,IAAI,CAAC,KAAK,+LAAG,eAAA,AAAY,EAAC,KAAK,CAAC,CAAA;IAClC,CAAC;IAED;;OAEG,CACG,WAAW,GAAA;;YAUf,IAAI;gBACF,MAAM,IAAI,GAAG,UAAM,4LAAA,AAAG,EAAC,IAAI,CAAC,KAAK,EAAE,GAAG,IAAI,CAAC,GAAG,CAAA,OAAA,CAAS,EAAE;oBAAE,OAAO,EAAE,IAAI,CAAC,OAAO;gBAAA,CAAE,CAAC,CAAA;gBACnF,OAAO;oBAAE,IAAI;oBAAE,KAAK,EAAE,IAAI;gBAAA,CAAE,CAAA;aAC7B,CAAC,OAAO,KAAK,EAAE;gBACd,+LAAI,iBAAA,AAAc,EAAC,KAAK,CAAC,EAAE;oBACzB,OAAO;wBAAE,IAAI,EAAE,IAAI;wBAAE,KAAK;oBAAA,CAAE,CAAA;iBAC7B;gBAED,MAAM,KAAK,CAAA;aACZ;QACH,CAAC;KAAA;IAED;;;;OAIG,CACG,SAAS,CACb,EAAU,EAAA;;YAWV,IAAI;gBACF,MAAM,IAAI,GAAG,MAAM,gMAAA,AAAG,EAAC,IAAI,CAAC,KAAK,EAAE,GAAG,IAAI,CAAC,GAAG,CAAA,QAAA,EAAW,EAAE,EAAE,EAAE;oBAAE,OAAO,EAAE,IAAI,CAAC,OAAO;gBAAA,CAAE,CAAC,CAAA;gBACzF,OAAO;oBAAE,IAAI;oBAAE,KAAK,EAAE,IAAI;gBAAA,CAAE,CAAA;aAC7B,CAAC,OAAO,KAAK,EAAE;gBACd,KAAI,2MAAA,AAAc,EAAC,KAAK,CAAC,EAAE;oBACzB,OAAO;wBAAE,IAAI,EAAE,IAAI;wBAAE,KAAK;oBAAA,CAAE,CAAA;iBAC7B;gBAED,MAAM,KAAK,CAAA;aACZ;QACH,CAAC;KAAA;IAED;;;;;;;;;;;;OAYG,CACG,YAAY,CAChB,EAAU,EACV,UAII;QACF,MAAM,EAAE,KAAK;KACd,EAAA;;YAWD,IAAI;gBACF,MAAM,IAAI,GAAG,gMAAM,OAAA,AAAI,EACrB,IAAI,CAAC,KAAK,EACV,GAAG,IAAI,CAAC,GAAG,CAAA,OAAA,CAAS,EACpB;oBACE,EAAE;oBACF,IAAI,EAAE,EAAE;oBACR,MAAM,EAAE,OAAO,CAAC,MAAM;oBACtB,eAAe,EAAE,OAAO,CAAC,aAAa;oBACtC,kBAAkB,EAAE,OAAO,CAAC,gBAAgB;iBAC7C,EACD;oBAAE,OAAO,EAAE,IAAI,CAAC,OAAO;gBAAA,CAAE,CAC1B,CAAA;gBACD,OAAO;oBAAE,IAAI;oBAAE,KAAK,EAAE,IAAI;gBAAA,CAAE,CAAA;aAC7B,CAAC,OAAO,KAAK,EAAE;gBACd,+LAAI,iBAAA,AAAc,EAAC,KAAK,CAAC,EAAE;oBACzB,OAAO;wBAAE,IAAI,EAAE,IAAI;wBAAE,KAAK;oBAAA,CAAE,CAAA;iBAC7B;gBAED,MAAM,KAAK,CAAA;aACZ;QACH,CAAC;KAAA;IAED;;;;;;;;;;;OAWG,CACG,YAAY,CAChB,EAAU,EACV,OAIC,EAAA;;YAWD,IAAI;gBACF,MAAM,IAAI,GAAG,gMAAM,MAAA,AAAG,EACpB,IAAI,CAAC,KAAK,EACV,GAAG,IAAI,CAAC,GAAG,CAAA,QAAA,EAAW,EAAE,EAAE,EAC1B;oBACE,EAAE;oBACF,IAAI,EAAE,EAAE;oBACR,MAAM,EAAE,OAAO,CAAC,MAAM;oBACtB,eAAe,EAAE,OAAO,CAAC,aAAa;oBACtC,kBAAkB,EAAE,OAAO,CAAC,gBAAgB;iBAC7C,EACD;oBAAE,OAAO,EAAE,IAAI,CAAC,OAAO;gBAAA,CAAE,CAC1B,CAAA;gBACD,OAAO;oBAAE,IAAI;oBAAE,KAAK,EAAE,IAAI;gBAAA,CAAE,CAAA;aAC7B,CAAC,OAAO,KAAK,EAAE;gBACd,IAAI,4MAAA,AAAc,EAAC,KAAK,CAAC,EAAE;oBACzB,OAAO;wBAAE,IAAI,EAAE,IAAI;wBAAE,KAAK;oBAAA,CAAE,CAAA;iBAC7B;gBAED,MAAM,KAAK,CAAA;aACZ;QACH,CAAC;KAAA;IAED;;;;OAIG,CACG,WAAW,CACf,EAAU,EAAA;;YAWV,IAAI;gBACF,MAAM,IAAI,GAAG,gMAAM,OAAA,AAAI,EACrB,IAAI,CAAC,KAAK,EACV,GAAG,IAAI,CAAC,GAAG,CAAA,QAAA,EAAW,EAAE,CAAA,MAAA,CAAQ,EAChC,CAAA,CAAE,EACF;oBAAE,OAAO,EAAE,IAAI,CAAC,OAAO;gBAAA,CAAE,CAC1B,CAAA;gBACD,OAAO;oBAAE,IAAI;oBAAE,KAAK,EAAE,IAAI;gBAAA,CAAE,CAAA;aAC7B,CAAC,OAAO,KAAK,EAAE;gBACd,+LAAI,iBAAA,AAAc,EAAC,KAAK,CAAC,EAAE;oBACzB,OAAO;wBAAE,IAAI,EAAE,IAAI;wBAAE,KAAK;oBAAA,CAAE,CAAA;iBAC7B;gBAED,MAAM,KAAK,CAAA;aACZ;QACH,CAAC;KAAA;IAED;;;;;OAKG,CACG,YAAY,CAChB,EAAU,EAAA;;YAWV,IAAI;gBACF,MAAM,IAAI,GAAG,OAAM,kMAAA,AAAM,EACvB,IAAI,CAAC,KAAK,EACV,GAAG,IAAI,CAAC,GAAG,CAAA,QAAA,EAAW,EAAE,EAAE,EAC1B,CAAA,CAAE,EACF;oBAAE,OAAO,EAAE,IAAI,CAAC,OAAO;gBAAA,CAAE,CAC1B,CAAA;gBACD,OAAO;oBAAE,IAAI;oBAAE,KAAK,EAAE,IAAI;gBAAA,CAAE,CAAA;aAC7B,CAAC,OAAO,KAAK,EAAE;gBACd,+LAAI,iBAAA,AAAc,EAAC,KAAK,CAAC,EAAE;oBACzB,OAAO;wBAAE,IAAI,EAAE,IAAI;wBAAE,KAAK;oBAAA,CAAE,CAAA;iBAC7B;gBAED,MAAM,KAAK,CAAA;aACZ;QACH,CAAC;KAAA;CACF"}}, {"offset": {"line": 4382, "column": 0}, "map": {"version": 3, "file": "StorageClient.js", "sources": ["../../src/StorageClient.ts"], "sourceRoot": "", "names": [], "mappings": ";;;AAAA,OAAO,cAAc,MAAM,2BAA2B,CAAA;AACtD,OAAO,gBAAgB,MAAM,6BAA6B,CAAA;;;AAGpD,MAAO,aAAc,+MAAQ,UAAgB;IACjD,YAAY,GAAW,EAAE,UAAqC,CAAA,CAAE,EAAE,KAAa,CAAA;QAC7E,KAAK,CAAC,GAAG,EAAE,OAAO,EAAE,KAAK,CAAC,CAAA;IAC5B,CAAC;IAED;;;;OAIG,CACH,IAAI,CAAC,EAAU,EAAA;QACb,OAAO,wMAAI,UAAc,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,OAAO,EAAE,EAAE,EAAE,IAAI,CAAC,KAAK,CAAC,CAAA;IACnE,CAAC;CACF"}}, {"offset": {"line": 4407, "column": 0}, "map": {"version": 3, "file": "version.js", "sources": ["../../../src/lib/version.ts"], "sourceRoot": "", "names": [], "mappings": ";;;AAAO,MAAM,OAAO,GAAG,iBAAiB,CAAA"}}, {"offset": {"line": 4417, "column": 0}, "map": {"version": 3, "file": "constants.js", "sources": ["../../../src/lib/constants.ts"], "sourceRoot": "", "names": [], "mappings": ";;;;;;;AAGA,OAAO,EAAE,OAAO,EAAE,MAAM,WAAW,CAAA;;AAEnC,IAAI,MAAM,GAAG,EAAE,CAAA;AACf,aAAa;AACb,IAAI,OAAO,IAAI,KAAK,WAAW,EAAE;IAC/B,MAAM,GAAG,MAAM,CAAA;CAChB,MAAM,IAAI,OAAO,QAAQ,KAAK,WAAW,EAAE;IAC1C,MAAM,GAAG,KAAK,CAAA;CACf,MAAM,IAAI,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,CAAC,OAAO,KAAK,aAAa,EAAE;IAClF,MAAM,GAAG,cAAc,CAAA;CACxB,MAAM;IACL,MAAM,GAAG,MAAM,CAAA;CAChB;AAEM,MAAM,eAAe,GAAG;IAAE,eAAe,EAAE,CAAA,YAAA,EAAe,MAAM,CAAA,CAAA,2LAAI,UAAO,EAAE;AAAA,CAAE,CAAA;AAE/E,MAAM,sBAAsB,GAAG;IACpC,OAAO,EAAE,eAAe;CACzB,CAAA;AAEM,MAAM,kBAAkB,GAAG;IAChC,MAAM,EAAE,QAAQ;CACjB,CAAA;AAEM,MAAM,oBAAoB,GAA8B;IAC7D,gBAAgB,EAAE,IAAI;IACtB,cAAc,EAAE,IAAI;IACpB,kBAAkB,EAAE,IAAI;IACxB,QAAQ,EAAE,UAAU;CACrB,CAAA;AAEM,MAAM,wBAAwB,GAA0B,CAAA,CAAE,CAAA"}}, {"offset": {"line": 4459, "column": 0}, "map": {"version": 3, "file": "fetch.js", "sources": ["../../../src/lib/fetch.ts"], "sourceRoot": "", "names": [], "mappings": ";;;;;AAAA,aAAa;AACb,OAAO,SAAS,EAAE,EAAE,OAAO,IAAI,gBAAgB,EAAE,MAAM,sBAAsB,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAItE,MAAM,YAAY,GAAG,CAAC,WAAmB,EAAS,EAAE;IACzD,IAAI,MAAa,CAAA;IACjB,IAAI,WAAW,EAAE;QACf,MAAM,GAAG,WAAW,CAAA;KACrB,MAAM,IAAI,OAAO,KAAK,KAAK,WAAW,EAAE;QACvC,MAAM,kKAAG,UAA6B,CAAA;KACvC,MAAM;QACL,MAAM,GAAG,KAAK,CAAA;KACf;IACD,OAAO,CAAC,GAAG,IAAuB,EAAE,CAAG,CAAD,KAAO,CAAC,GAAG,IAAI,CAAC,CAAA;AACxD,CAAC,CAAA;AAEM,MAAM,yBAAyB,GAAG,GAAG,EAAE;IAC5C,IAAI,OAAO,OAAO,KAAK,WAAW,EAAE;QAClC,sKAAO,UAAgB,CAAA;KACxB;IAED,OAAO,OAAO,CAAA;AAChB,CAAC,CAAA;AAEM,MAAM,aAAa,GAAG,CAC3B,WAAmB,EACnB,cAA4C,EAC5C,WAAmB,EACZ,EAAE;IACT,MAAM,KAAK,IAAG,YAAY,CAAC,WAAW,CAAC,CAAA;IACvC,MAAM,kBAAkB,GAAG,yBAAyB,EAAE,CAAA;IAEtD,OAAO,CAAO,KAAK,EAAE,IAAI,EAAE,CAAE,CAAA,SAAA,KAAA,GAAA,KAAA,GAAA,KAAA,GAAA;;YAC3B,MAAM,WAAW,GAAG,CAAA,KAAA,AAAC,MAAM,cAAc,EAAE,AAAC,MAAA,QAAA,OAAA,KAAA,IAAA,KAAI,WAAW,CAAA;YAC3D,IAAI,OAAO,GAAG,IAAI,kBAAkB,CAAC,IAAI,KAAA,QAAJ,IAAI,KAAA,KAAA,IAAA,KAAA,IAAJ,IAAI,CAAE,OAAO,CAAC,CAAA;YAEnD,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE;gBAC1B,OAAO,CAAC,GAAG,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAA;aACnC;YAED,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC,EAAE;gBACjC,OAAO,CAAC,GAAG,CAAC,eAAe,EAAE,CAAA,OAAA,EAAU,WAAW,EAAE,CAAC,CAAA;aACtD;YAED,OAAO,KAAK,EAAC,KAAK,EAAA,OAAA,MAAA,CAAA,OAAA,MAAA,CAAA,CAAA,GAAO,IAAI,GAAA;gBAAE,OAAO;YAAA,GAAG,CAAA;QAC3C,CAAC,CAAA,CAAA;AACH,CAAC,CAAA"}}, {"offset": {"line": 4535, "column": 0}, "map": {"version": 3, "file": "helpers.js", "sources": ["../../../src/lib/helpers.ts"], "sourceRoot": "", "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGM,SAAU,IAAI;IAClB,OAAO,sCAAsC,CAAC,OAAO,CAAC,OAAO,EAAE,SAAU,CAAC;QACxE,IAAI,CAAC,GAAG,AAAC,IAAI,CAAC,MAAM,EAAE,GAAG,EAAE,CAAC,EAAG,CAAC,EAC9B,CAAC,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,AAAC,CAAC,GAAG,GAAG,CAAC,EAAG,GAAG,CAAA;QACpC,OAAO,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAA;IACvB,CAAC,CAAC,CAAA;AACJ,CAAC;AAEK,SAAU,mBAAmB,CAAC,GAAW;IAC7C,OAAO,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,CAAA;AAC5C,CAAC;AAEM,MAAM,SAAS,GAAG,GAAG,CAAG,CAAD,MAAQ,MAAM,GAAK,WAAW,CAAA;AAEtD,SAAU,oBAAoB,CAMlC,OAA0C,EAC1C,QAAoC;;IAEpC,MAAM,EACJ,EAAE,EAAE,SAAS,EACb,IAAI,EAAE,WAAW,EACjB,QAAQ,EAAE,eAAe,EACzB,MAAM,EAAE,aAAa,EACtB,GAAG,OAAO,CAAA;IACX,MAAM,EACJ,EAAE,EAAE,kBAAkB,EACtB,IAAI,EAAE,oBAAoB,EAC1B,QAAQ,EAAE,wBAAwB,EAClC,MAAM,EAAE,sBAAsB,EAC/B,GAAG,QAAQ,CAAA;IAEZ,MAAM,MAAM,GAAgD;QAC1D,EAAE,EAAA,OAAA,MAAA,CAAA,OAAA,MAAA,CAAA,CAAA,GACG,kBAAkB,GAClB,SAAS,CACb;QACD,IAAI,EAAA,OAAA,MAAA,CAAA,OAAA,MAAA,CAAA,CAAA,GACC,oBAAoB,GACpB,WAAW,CACf;QACD,QAAQ,EAAA,OAAA,MAAA,CAAA,OAAA,MAAA,CAAA,CAAA,GACH,wBAAwB,GACxB,eAAe,CACnB;QACD,MAAM,EAAA,OAAA,MAAA,CAAA,OAAA,MAAA,CAAA,OAAA,MAAA,CAAA,CAAA,GACD,sBAAsB,GACtB,aAAa,GAAA;YAChB,OAAO,EAAA,OAAA,MAAA,CAAA,OAAA,MAAA,CAAA,CAAA,GACD,AAAD,CAAC,KAAA,sBAAsB,KAAA,QAAtB,sBAAsB,KAAA,KAAA,IAAA,KAAA,IAAtB,sBAAsB,CAAE,OAAO,MAAA,QAAA,OAAA,KAAA,IAAA,KAAI,CAAA,CAAE,CAAC,EACtC,CAAD,AAAC,KAAA,aAAa,KAAA,QAAb,aAAa,KAAA,KAAA,IAAA,KAAA,IAAb,aAAa,CAAE,OAAO,MAAA,QAAA,OAAA,KAAA,IAAA,KAAI,CAAA,CAAE,CAAC;QAAA,EAEpC;QACD,WAAW,EAAE,GAAS,CAAE,CAAA,SAAA,IAAA,EAAA,KAAA,GAAA,KAAA,GAAA;gBAAC,OAAA,EAAE,CAAA;YAAA,EAAA;KAC5B,CAAA;IAED,IAAI,OAAO,CAAC,WAAW,EAAE;QACvB,MAAM,CAAC,WAAW,GAAG,OAAO,CAAC,WAAW,CAAA;KACzC,MAAM;QACL,yBAAyB;QACzB,OAAQ,MAAc,CAAC,WAAW,CAAA;KACnC;IAED,OAAO,MAAM,CAAA;AACf,CAAC"}}, {"offset": {"line": 4607, "column": 0}, "map": {"version": 3, "file": "SupabaseAuthClient.js", "sources": ["../../../src/lib/SupabaseAuthClient.ts"], "sourceRoot": "", "names": [], "mappings": ";;;;AAAA,OAAO,EAAE,UAAU,EAAE,MAAM,mBAAmB,CAAA;;AAGxC,MAAO,kBAAmB,mOAAQ,aAAU;IAChD,YAAY,OAAkC,CAAA;QAC5C,KAAK,CAAC,OAAO,CAAC,CAAA;IAChB,CAAC;CACF"}}, {"offset": {"line": 4624, "column": 0}, "map": {"version": 3, "file": "SupabaseClient.js", "sources": ["../../src/SupabaseClient.ts"], "sourceRoot": "", "names": [], "mappings": ";;;AAAA,OAAO,EAAE,eAAe,EAAE,MAAM,wBAAwB,CAAA;AAExD,OAAO,EACL,eAAe,GAGhB,MAAM,wBAAwB,CAAA;AAC/B,OAAO,EAGL,cAAc,GAEf,MAAM,uBAAuB,CAAA;;AAC9B,OAAO,EAAE,aAAa,IAAI,qBAAqB,EAAE,MAAM,sBAAsB,CAAA;AAC7E,OAAO,EACL,sBAAsB,EACtB,kBAAkB,EAClB,oBAAoB,EACpB,wBAAwB,GACzB,MAAM,iBAAiB,CAAA;AACxB,OAAO,EAAE,aAAa,EAAE,MAAM,aAAa,CAAA;AAC3C,OAAO,EAAE,mBAAmB,EAAE,oBAAoB,EAAE,MAAM,eAAe,CAAA;AACzE,OAAO,EAAE,kBAAkB,EAAE,MAAM,0BAA0B,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ/C,MAAO,cAAc;IA2BjC;;;;;;;;;;;OAWG,CACH,YACY,WAAmB,EACnB,WAAmB,EAC7B,OAA2C,CAAA;;QAFjC,IAAA,CAAA,WAAW,GAAX,WAAW,CAAQ;QACnB,IAAA,CAAA,WAAW,GAAX,WAAW,CAAQ;QAG7B,IAAI,CAAC,WAAW,EAAE,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAA;QAC7D,IAAI,CAAC,WAAW,EAAE,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAA;QAE7D,MAAM,YAAY,OAAG,+MAAA,AAAmB,EAAC,WAAW,CAAC,CAAA;QACrD,MAAM,OAAO,GAAG,IAAI,GAAG,CAAC,YAAY,CAAC,CAAA;QAErC,IAAI,CAAC,WAAW,GAAG,IAAI,GAAG,CAAC,aAAa,EAAE,OAAO,CAAC,CAAA;QAClD,IAAI,CAAC,WAAW,CAAC,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAM,EAAE,IAAI,CAAC,CAAA;QAC3E,IAAI,CAAC,OAAO,GAAG,IAAI,GAAG,CAAC,SAAS,EAAE,OAAO,CAAC,CAAA;QAC1C,IAAI,CAAC,UAAU,GAAG,IAAI,GAAG,CAAC,YAAY,EAAE,OAAO,CAAC,CAAA;QAChD,IAAI,CAAC,YAAY,GAAG,IAAI,GAAG,CAAC,cAAc,EAAE,OAAO,CAAC,CAAA;QAEpD,mEAAmE;QACnE,MAAM,iBAAiB,GAAG,CAAA,GAAA,EAAM,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAA,WAAA,CAAa,CAAA;QAC3E,MAAM,QAAQ,GAAG;YACf,EAAE,6LAAE,qBAAkB;YACtB,QAAQ,6LAAE,2BAAwB;YAClC,IAAI,EAAA,OAAA,MAAA,CAAA,OAAA,MAAA,CAAA,CAAA,8LAAO,uBAAoB,GAAA;gBAAE,UAAU,EAAE,iBAAiB;YAAA,EAAE;YAChE,MAAM,4LAAE,0BAAsB;SAC/B,CAAA;QAED,MAAM,QAAQ,gMAAG,uBAAA,AAAoB,EAAC,OAAO,KAAA,QAAP,OAAO,KAAA,KAAA,IAAP,OAAO,GAAI,CAAA,CAAE,EAAE,QAAQ,CAAC,CAAA;QAE9D,IAAI,CAAC,UAAU,GAAG,CAAA,KAAA,QAAQ,CAAC,IAAI,CAAC,UAAU,MAAA,QAAA,OAAA,KAAA,IAAA,KAAI,EAAE,CAAA;QAChD,IAAI,CAAC,OAAO,GAAG,CAAA,KAAA,QAAQ,CAAC,MAAM,CAAC,OAAO,MAAA,QAAA,OAAA,KAAA,IAAA,KAAI,CAAA,CAAE,CAAA;QAE5C,IAAI,CAAC,QAAQ,CAAC,WAAW,EAAE;YACzB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,uBAAuB,CACtC,CAAA,KAAA,QAAQ,CAAC,IAAI,MAAA,QAAA,OAAA,KAAA,IAAA,KAAI,CAAA,CAAE,EACnB,IAAI,CAAC,OAAO,EACZ,QAAQ,CAAC,MAAM,CAAC,KAAK,CACtB,CAAA;SACF,MAAM;YACL,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,CAAA;YAEvC,IAAI,CAAC,IAAI,GAAG,IAAI,KAAK,CAAqB,CAAA,CAAS,EAAE;gBACnD,GAAG,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,EAAE;oBACf,MAAM,IAAI,KAAK,CACb,CAAA,0GAAA,EAA6G,MAAM,CACjH,IAAI,CACL,CAAA,gBAAA,CAAkB,CACpB,CAAA;gBACH,CAAC;aACF,CAAC,CAAA;SACH;QAED,IAAI,CAAC,KAAK,OAAG,uMAAA,AAAa,EAAC,WAAW,EAAE,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,CAAA;QAC/F,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,mBAAmB,CAAA,OAAA,MAAA,CAAA;YACtC,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,WAAW,EAAE,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC;QAAA,GACzC,QAAQ,CAAC,QAAQ,EACpB,CAAA;QACF,IAAI,CAAC,IAAI,GAAG,qLAAI,kBAAe,CAAC,IAAI,GAAG,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC,IAAI,EAAE;YAChE,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,MAAM,EAAE,QAAQ,CAAC,EAAE,CAAC,MAAM;YAC1B,KAAK,EAAE,IAAI,CAAC,KAAK;SAClB,CAAC,CAAA;QAEF,IAAI,CAAC,QAAQ,CAAC,WAAW,EAAE;YACzB,IAAI,CAAC,oBAAoB,EAAE,CAAA;SAC5B;IACH,CAAC;IAED;;OAEG,CACH,IAAI,SAAS,GAAA;QACX,OAAO,+LAAI,kBAAe,CAAC,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE;YACjD,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,WAAW,EAAE,IAAI,CAAC,KAAK;SACxB,CAAC,CAAA;IACJ,CAAC;IAED;;OAEG,CACH,IAAI,OAAO,GAAA;QACT,OAAO,2LAAI,gBAAqB,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,KAAK,CAAC,CAAA;IAClF,CAAC;IAUD;;;;OAIG,CACH,IAAI,CAAC,QAAgB,EAAA;QACnB,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;IACjC,CAAC;IAED,oEAAoE;IACpE;;;;;;OAMG,CACH,MAAM,CACJ,MAAqB,EAAA;QAMrB,OAAO,IAAI,CAAC,IAAI,CAAC,MAAM,CAAgB,MAAM,CAAC,CAAA;IAChD,CAAC;IAED,iEAAiE;IACjE;;;;;;;;;;;;;;;;;;;;;;OAsBG,CACH,GAAG,CACD,EAAU,EACV,OAAmB,CAAA,CAAE,EACrB,UAII,CAAA,CAAE,EAAA;QAYN,OAAO,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,IAAI,EAAE,OAAO,CAAC,CAAA;IACzC,CAAC;IAED;;;;;;OAMG,CACH,OAAO,CAAC,IAAY,EAAE,OAA+B;QAAE,MAAM,EAAE,CAAA,CAAE;IAAA,CAAE,EAAA;QACjE,OAAO,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,CAAA;IAC1C,CAAC;IAED;;OAEG,CACH,WAAW,GAAA;QACT,OAAO,IAAI,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAA;IACpC,CAAC;IAED;;;;;OAKG,CACH,aAAa,CAAC,OAAwB,EAAA;QACpC,OAAO,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,OAAO,CAAC,CAAA;IAC7C,CAAC;IAED;;OAEG,CACH,iBAAiB,GAAA;QACf,OAAO,IAAI,CAAC,QAAQ,CAAC,iBAAiB,EAAE,CAAA;IAC1C,CAAC;IAEa,eAAe,GAAA;;;YAC3B,IAAI,IAAI,CAAC,WAAW,EAAE;gBACpB,OAAO,MAAM,IAAI,CAAC,WAAW,EAAE,CAAA;aAChC;YAED,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,CAAA;YAE7C,OAAO,CAAA,KAAA,CAAA,KAAA,IAAI,CAAC,OAAO,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAE,YAAY,MAAA,QAAA,OAAA,KAAA,IAAA,KAAI,IAAI,CAAA;;KAC1C;IAEO,uBAAuB,CAC7B,EACE,gBAAgB,EAChB,cAAc,EACd,kBAAkB,EAClB,OAAO,EACP,UAAU,EACV,QAAQ,EACR,IAAI,EACJ,KAAK,EACqB,EAC5B,OAAgC,EAChC,KAAa,EAAA;QAEb,MAAM,WAAW,GAAG;YAClB,aAAa,EAAE,CAAA,OAAA,EAAU,IAAI,CAAC,WAAW,EAAE;YAC3C,MAAM,EAAE,GAAG,IAAI,CAAC,WAAW,EAAE;SAC9B,CAAA;QACD,OAAO,wMAAI,qBAAkB,CAAC;YAC5B,GAAG,EAAE,IAAI,CAAC,OAAO,CAAC,IAAI;YACtB,OAAO,EAAA,OAAA,MAAA,CAAA,OAAA,MAAA,CAAA,CAAA,GAAO,WAAW,GAAK,OAAO,CAAE;YACvC,UAAU,EAAE,UAAU;YACtB,gBAAgB;YAChB,cAAc;YACd,kBAAkB;YAClB,OAAO;YACP,QAAQ;YACR,IAAI;YACJ,KAAK;YACL,KAAK;YACL,wEAAwE;YACxE,gFAAgF;YAChF,4BAA4B,EAAE,eAAe,IAAI,IAAI,CAAC,OAAO;SAC9D,CAAC,CAAA;IACJ,CAAC;IAEO,mBAAmB,CAAC,OAA8B,EAAA;QACxD,OAAO,0OAAI,iBAAc,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,EAAA,OAAA,MAAA,CAAA,OAAA,MAAA,CAAA,CAAA,GAC1C,OAAO,GAAA;YACV,MAAM,EAAA,OAAA,MAAA,CAAO;gBAAE,MAAM,EAAE,IAAI,CAAC,WAAW;YAAA,CAAE,EAAK,OAAO,KAAA,QAAP,OAAO,KAAA,KAAA,IAAA,KAAA,IAAP,OAAO,CAAE,MAAM;QAAA,GAC7D,CAAA;IACJ,CAAC;IAEO,oBAAoB,GAAA;QAC1B,IAAI,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC,KAAK,EAAE,OAAO,EAAE,EAAE;YACxD,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE,QAAQ,EAAE,OAAO,KAAA,QAAP,OAAO,KAAA,KAAA,IAAA,KAAA,IAAP,OAAO,CAAE,YAAY,CAAC,CAAA;QAClE,CAAC,CAAC,CAAA;QACF,OAAO,IAAI,CAAA;IACb,CAAC;IAEO,mBAAmB,CACzB,KAAsB,EACtB,MAA4B,EAC5B,KAAc,EAAA;QAEd,IACE,CAAC,KAAK,KAAK,iBAAiB,IAAI,KAAK,KAAK,WAAW,CAAC,IACtD,IAAI,CAAC,kBAAkB,KAAK,KAAK,EACjC;YACA,IAAI,CAAC,kBAAkB,GAAG,KAAK,CAAA;SAChC,MAAM,IAAI,KAAK,KAAK,YAAY,EAAE;YACjC,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAA;YACvB,IAAI,MAAM,IAAI,SAAS,EAAE,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAA;YAC5C,IAAI,CAAC,kBAAkB,GAAG,SAAS,CAAA;SACpC;IACH,CAAC;CACF"}}, {"offset": {"line": 4879, "column": 0}, "map": {"version": 3, "file": "index.js", "sources": ["../../src/index.ts"], "sourceRoot": "", "names": [], "mappings": ";;;AAAA,OAAO,cAAc,MAAM,kBAAkB,CAAA;AAG7C,cAAc,mBAAmB,CAAA;AAEjC,OAAO,EAIL,cAAc,GACf,MAAM,wBAAwB,CAAA;AAS/B,cAAc,uBAAuB,CAAA;;;;;;;AAO9B,MAAM,YAAY,GAAG,CAS1B,WAAmB,EACnB,WAAmB,EACnB,OAA2C,EACG,EAAE;IAChD,OAAO,6LAAI,UAAc,CAA+B,WAAW,EAAE,WAAW,EAAE,OAAO,CAAC,CAAA;AAC5F,CAAC,CAAA"}}, {"offset": {"line": 4901, "column": 0}, "map": {"version": 3, "file": "version.js", "sources": ["../../src/version.ts"], "sourceRoot": "", "names": [], "mappings": ";;;AAAO,MAAM,OAAO,GAAG,OAAO,CAAC"}}, {"offset": {"line": 4911, "column": 0}, "map": {"version": 3, "file": "helpers.js", "sources": ["../../../src/utils/helpers.ts"], "sourceRoot": "", "names": [], "mappings": ";;;;;;;AACA,OAAO,EAAE,KAAK,IAAI,WAAW,EAAE,SAAS,IAAI,eAAe,EAAE,MAAM,QAAQ,CAAC;;AAMrE,MAAM,KAAK,mJAAG,QAAW,CAAC;AAO1B,MAAM,SAAS,mJAAG,YAAe,CAAC;AAQnC,SAAU,iBAAiB,CAC/B,MAAc;IAEd,MAAM,MAAM,IAAG,2JAAA,AAAW,EAAC,MAAM,CAAC,CAAC;IAEnC,OAAO,MAAM,CAAC,IAAI,CAAC,MAAM,IAAI,CAAA,CAAE,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAG,CAAD,AAAE;YAC9C,IAAI;YACJ,KAAK,EAAE,MAAM,CAAC,IAAI,CAAC;SACpB,CAAC,CAAC,CAAC;AACN,CAAC;AASK,SAAU,qBAAqB,CACnC,IAAY,EACZ,KAAa,EACb,OAAyB;IAEzB,2JAAO,YAAA,AAAe,EAAC,IAAI,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;AAC/C,CAAC;AAEK,SAAU,SAAS;IACvB,OAAO,AACL,OAAO,MAAM,GAAK,WAAW,IAAI,OAAO,MAAM,CAAC,QAAQ,KAAK,WAAW,CACxE,CAAC;AACJ,CAAC"}}, {"offset": {"line": 4941, "column": 0}, "map": {"version": 3, "file": "constants.js", "sources": ["../../../src/utils/constants.ts"], "sourceRoot": "", "names": [], "mappings": ";;;AAEO,MAAM,sBAAsB,GAAkB;IACnD,IAAI,EAAE,GAAG;IACT,QAAQ,EAAE,KAAK;IACf,QAAQ,EAAE,KAAK;IACf,2DAA2D;IAC3D,oGAAoG;IACpG,MAAM,EAAE,GAAG,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;CAC3B,CAAC"}}, {"offset": {"line": 4958, "column": 0}, "map": {"version": 3, "file": "chunker.js", "sources": ["../../../src/utils/chunker.ts"], "sourceRoot": "", "names": [], "mappings": ";;;;;;;AAKO,MAAM,cAAc,GAAG,IAAI,CAAC;AAEnC,MAAM,gBAAgB,GAAG,0BAA0B,CAAC;AAC9C,SAAU,WAAW,CAAC,UAAkB,EAAE,GAAW;IACzD,IAAI,UAAU,KAAK,GAAG,EAAE,CAAC;QACvB,OAAO,IAAI,CAAC;IACd,CAAC;IAED,MAAM,SAAS,GAAG,UAAU,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC;IACrD,IAAI,SAAS,IAAI,SAAS,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE,CAAC;QACtC,OAAO,IAAI,CAAC;IACd,CAAC;IAED,OAAO,KAAK,CAAC;AACf,CAAC;AAKK,SAAU,YAAY,CAC1B,GAAW,EACX,KAAa,EACb,SAAkB;IAElB,MAAM,iBAAiB,GAAG,SAAS,IAAI,cAAc,CAAC;IAEtD,IAAI,YAAY,GAAG,kBAAkB,CAAC,KAAK,CAAC,CAAC;IAE7C,IAAI,YAAY,CAAC,MAAM,IAAI,iBAAiB,EAAE,CAAC;QAC7C,OAAO;YAAC;gBAAE,IAAI,EAAE,GAAG;gBAAE,KAAK;YAAA,CAAE;SAAC,CAAC;IAChC,CAAC;IAED,MAAM,MAAM,GAAa,EAAE,CAAC;IAE5B,MAAO,YAAY,CAAC,MAAM,GAAG,CAAC,CAAE,CAAC;QAC/B,IAAI,gBAAgB,GAAG,YAAY,CAAC,KAAK,CAAC,CAAC,EAAE,iBAAiB,CAAC,CAAC;QAEhE,MAAM,aAAa,GAAG,gBAAgB,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;QAExD,oDAAoD;QACpD,IAAI,aAAa,GAAG,iBAAiB,GAAG,CAAC,EAAE,CAAC;YAC1C,kEAAkE;YAClE,0DAA0D;YAC1D,kCAAkC;YAClC,gBAAgB,GAAG,gBAAgB,CAAC,KAAK,CAAC,CAAC,EAAE,aAAa,CAAC,CAAC;QAC9D,CAAC;QAED,IAAI,SAAS,GAAW,EAAE,CAAC;QAE3B,+DAA+D;QAC/D,MAAO,gBAAgB,CAAC,MAAM,GAAG,CAAC,CAAE,CAAC;YACnC,IAAI,CAAC;gBACH,uDAAuD;gBACvD,gCAAgC;gBAChC,SAAS,GAAG,kBAAkB,CAAC,gBAAgB,CAAC,CAAC;gBACjD,MAAM;YACR,CAAC,CAAC,OAAO,KAAK,EAAE,CAAC;gBACf,IACE,KAAK,YAAY,QAAQ,IACzB,gBAAgB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,IAC/B,gBAAgB,CAAC,MAAM,GAAG,CAAC,EAC3B,CAAC;oBACD,gBAAgB,GAAG,gBAAgB,CAAC,KAAK,CACvC,CAAC,EACD,gBAAgB,CAAC,MAAM,GAAG,CAAC,CAC5B,CAAC;gBACJ,CAAC,MAAM,CAAC;oBACN,MAAM,KAAK,CAAC;gBACd,CAAC;YACH,CAAC;QACH,CAAC;QAED,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QACvB,YAAY,GAAG,YAAY,CAAC,KAAK,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC;IAC7D,CAAC;IAED,OAAO,MAAM,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,CAAC,EAAE,CAAG,CAAD,AAAE;YAAE,IAAI,EAAE,GAAG,GAAG,CAAA,CAAA,EAAI,CAAC,EAAE;YAAE,KAAK;QAAA,CAAE,CAAC,CAAC,CAAC;AACpE,CAAC;AAGM,KAAK,UAAU,aAAa,CACjC,GAAW,EACX,aAEmE;IAEnE,MAAM,KAAK,GAAG,MAAM,aAAa,CAAC,GAAG,CAAC,CAAC;IAEvC,IAAI,KAAK,EAAE,CAAC;QACV,OAAO,KAAK,CAAC;IACf,CAAC;IAED,IAAI,MAAM,GAAa,EAAE,CAAC;IAE1B,IAAK,IAAI,CAAC,GAAG,CAAC,GAAI,CAAC,EAAE,CAAE,CAAC;QACtB,MAAM,SAAS,GAAG,GAAG,GAAG,CAAA,CAAA,EAAI,CAAC,EAAE,CAAC;QAChC,MAAM,KAAK,GAAG,MAAM,aAAa,CAAC,SAAS,CAAC,CAAC;QAE7C,IAAI,CAAC,KAAK,EAAE,CAAC;YACX,MAAM;QACR,CAAC;QAED,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IACrB,CAAC;IAED,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QACtB,OAAO,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IACzB,CAAC;IAED,OAAO,IAAI,CAAC;AACd,CAAC;AAEM,KAAK,UAAU,YAAY,CAChC,GAAW,EACX,aAEmE,EACnE,WAAmD;IAEnD,MAAM,KAAK,GAAG,MAAM,aAAa,CAAC,GAAG,CAAC,CAAC;IAEvC,IAAI,KAAK,EAAE,CAAC;QACV,MAAM,WAAW,CAAC,GAAG,CAAC,CAAC;IACzB,CAAC;IAED,IAAK,IAAI,CAAC,GAAG,CAAC,GAAI,CAAC,EAAE,CAAE,CAAC;QACtB,MAAM,SAAS,GAAG,GAAG,GAAG,CAAA,CAAA,EAAI,CAAC,EAAE,CAAC;QAChC,MAAM,KAAK,GAAG,MAAM,aAAa,CAAC,SAAS,CAAC,CAAC;QAE7C,IAAI,CAAC,KAAK,EAAE,CAAC;YACX,MAAM;QACR,CAAC;QAED,MAAM,WAAW,CAAC,SAAS,CAAC,CAAC;IAC/B,CAAC;AACH,CAAC"}}, {"offset": {"line": 5062, "column": 0}, "map": {"version": 3, "file": "base64url.js", "sources": ["../../../src/utils/base64url.ts"], "sourceRoot": "", "names": [], "mappings": "AAAA;;;;GAIG,CAEH;;;GAGG;;;;;;;AACH,MAAM,YAAY,GAChB,kEAAkE,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;AAE/E;;;GAGG,CACH,MAAM,gBAAgB,GAAG,UAAU,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;AAE9C;;;GAGG,CACH,MAAM,cAAc,GAAG,CAAC,GAAG,EAAE;IAC3B,MAAM,OAAO,GAAa,IAAI,KAAK,CAAC,GAAG,CAAC,CAAC;IAEzC,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,CAAE,CAAC;QAC3C,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;IAClB,CAAC;IAED,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,gBAAgB,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,CAAE,CAAC;QACpD,OAAO,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;IAClD,CAAC;IAED,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,YAAY,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,CAAE,CAAC;QAChD,OAAO,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IAC7C,CAAC;IAED,OAAO,OAAO,CAAC;AACjB,CAAC,CAAC,EAAE,CAAC;AASC,SAAU,iBAAiB,CAAC,GAAW;IAC3C,MAAM,MAAM,GAAa,EAAE,CAAC;IAE5B,IAAI,KAAK,GAAG,CAAC,CAAC;IACd,IAAI,UAAU,GAAG,CAAC,CAAC;IAEnB,MAAM,OAAO,GAAG,CAAC,IAAY,EAAE,EAAE;QAC/B,KAAK,GAAG,AAAC,KAAK,IAAI,CAAC,CAAC,EAAG,IAAI,CAAC;QAC5B,UAAU,IAAI,CAAC,CAAC;QAEhB,MAAO,UAAU,IAAI,CAAC,CAAE,CAAC;YACvB,MAAM,GAAG,GAAG,AAAC,KAAK,IAAI,AAAC,UAAU,GAAG,CAAC,CAAC,CAAC,CAAG,EAAE,CAAC;YAC7C,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC;YAC/B,UAAU,IAAI,CAAC,CAAC;QAClB,CAAC;IACH,CAAC,CAAC;IAEF,YAAY,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;IAE3B,IAAI,UAAU,GAAG,CAAC,EAAE,CAAC;QACnB,KAAK,GAAG,KAAK,IAAI,AAAC,CAAC,GAAG,UAAU,CAAC,CAAC;QAClC,UAAU,GAAG,CAAC,CAAC;QAEf,MAAO,UAAU,IAAI,CAAC,CAAE,CAAC;YACvB,MAAM,GAAG,GAAG,AAAC,KAAK,IAAI,AAAC,UAAU,GAAG,CAAC,CAAC,CAAC,CAAG,EAAE,CAAC;YAC7C,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC;YAC/B,UAAU,IAAI,CAAC,CAAC;QAClB,CAAC;IACH,CAAC;IAED,OAAO,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AACzB,CAAC;AAQK,SAAU,mBAAmB,CAAC,GAAW;IAC7C,MAAM,IAAI,GAAa,EAAE,CAAC;IAE1B,MAAM,IAAI,GAAG,CAAC,SAAiB,EAAE,EAAE;QACjC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC,CAAC;IAC7C,CAAC,CAAC;IAEF,MAAM,KAAK,GAAG;QACZ,OAAO,EAAE,CAAC;QACV,SAAS,EAAE,CAAC;KACb,CAAC;IAEF,IAAI,KAAK,GAAG,CAAC,CAAC;IACd,IAAI,UAAU,GAAG,CAAC,CAAC;IAEnB,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,CAAE,CAAC;QACvC,MAAM,SAAS,GAAG,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;QACpC,MAAM,IAAI,GAAG,cAAc,CAAC,SAAS,CAAC,CAAC;QAEvC,IAAI,IAAI,GAAG,CAAC,CAAC,EAAE,CAAC;YACd,6BAA6B;YAC7B,KAAK,GAAG,AAAC,KAAK,IAAI,CAAC,CAAC,EAAG,IAAI,CAAC;YAC5B,UAAU,IAAI,CAAC,CAAC;YAEhB,MAAO,UAAU,IAAI,CAAC,CAAE,CAAC;gBACvB,cAAc,CAAC,AAAC,KAAK,IAAI,AAAC,UAAU,GAAG,CAAC,CAAC,CAAC,CAAG,IAAI,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;gBAChE,UAAU,IAAI,CAAC,CAAC;YAClB,CAAC;QACH,CAAC,MAAM,IAAI,IAAI,KAAK,CAAC,CAAC,EAAE,CAAC;YAEvB,SAAS;QACX,CAAC,MAAM,CAAC;YACN,MAAM,IAAI,KAAK,CACb,CAAA,8BAAA,EAAiC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,CAAA,cAAA,EAAiB,CAAC,EAAE,CAC/D,CAAC;QACJ,CAAC;IACH,CAAC;IAED,OAAO,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AACvB,CAAC;AAQK,SAAU,eAAe,CAC7B,SAAiB,EACjB,IAA4B;IAE5B,IAAI,SAAS,IAAI,IAAI,EAAE,CAAC;QACtB,IAAI,CAAC,SAAS,CAAC,CAAC;QAChB,OAAO;IACT,CAAC,MAAM,IAAI,SAAS,IAAI,KAAK,EAAE,CAAC;QAC9B,IAAI,CAAC,IAAI,GAAG,AAAC,SAAS,IAAI,CAAC,CAAC,CAAC,CAAC;QAC9B,IAAI,CAAC,IAAI,GAAG,AAAC,SAAS,GAAG,IAAI,CAAC,CAAC,CAAC;QAChC,OAAO;IACT,CAAC,MAAM,IAAI,SAAS,IAAI,MAAM,EAAE,CAAC;QAC/B,IAAI,CAAC,IAAI,GAAG,AAAC,SAAS,IAAI,EAAE,CAAC,CAAC,CAAC;QAC/B,IAAI,CAAC,IAAI,GAAK,AAAD,AAAD,SAAW,IAAI,CAAC,CAAC,EAAG,IAAI,CAAC,CAAC,CAAC;QACvC,IAAI,CAAC,IAAI,GAAG,AAAC,SAAS,GAAG,IAAI,CAAC,CAAC,CAAC;QAChC,OAAO;IACT,CAAC,MAAM,IAAI,SAAS,IAAI,QAAQ,EAAE,CAAC;QACjC,IAAI,CAAC,IAAI,GAAG,AAAC,SAAS,IAAI,EAAE,CAAC,CAAC,CAAC;QAC/B,IAAI,CAAC,IAAI,GAAG,AAAC,AAAC,SAAS,IAAI,EAAE,CAAC,EAAG,IAAI,CAAC,CAAC,CAAC;QACxC,IAAI,CAAC,IAAI,GAAK,AAAF,AAAC,SAAU,IAAI,CAAC,CAAC,EAAG,IAAI,CAAC,CAAC,CAAC;QACvC,IAAI,CAAC,IAAI,GAAI,AAAD,SAAU,GAAG,IAAI,CAAC,CAAC,CAAC;QAChC,OAAO;IACT,CAAC;IAED,MAAM,IAAI,KAAK,CAAC,CAAA,gCAAA,EAAmC,SAAS,CAAC,QAAQ,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;AAC/E,CAAC;AAQK,SAAU,YAAY,CAAC,GAAW,EAAE,IAA4B;IACpE,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,CAAE,CAAC;QACvC,IAAI,SAAS,GAAG,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;QAElC,IAAI,SAAS,GAAG,MAAM,IAAI,SAAS,IAAI,MAAM,EAAE,CAAC;YAC9C,uEAAuE;YACvE,sEAAsE;YACtE,2CAA2C;YAC3C,MAAM,aAAa,GAAG,AAAC,CAAC,SAAS,GAAG,MAAM,CAAC,GAAG,KAAK,CAAC,EAAG,MAAM,CAAC;YAC9D,MAAM,YAAY,GAAG,AAAC,GAAG,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,EAAG,MAAM,CAAC;YAC/D,SAAS,GAAG,CAAC,YAAY,GAAG,aAAa,CAAC,GAAG,OAAO,CAAC;YACrD,CAAC,IAAI,CAAC,CAAC;QACT,CAAC;QAED,eAAe,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;IACnC,CAAC;AACH,CAAC;AAUK,SAAU,cAAc,CAC5B,IAAY,EACZ,KAA6C,EAC7C,IAAiC;IAEjC,IAAI,KAAK,CAAC,OAAO,KAAK,CAAC,EAAE,CAAC;QACxB,IAAI,IAAI,IAAI,IAAI,EAAE,CAAC;YACjB,IAAI,CAAC,IAAI,CAAC,CAAC;YACX,OAAO;QACT,CAAC;QAED,uDAAuD;QACvD,IAAK,IAAI,UAAU,GAAG,CAAC,EAAE,UAAU,GAAG,CAAC,EAAE,UAAU,IAAI,CAAC,CAAE,CAAC;YACzD,IAAI,CAAC,AAAC,IAAI,IAAI,AAAC,CAAC,GAAG,UAAU,CAAC,CAAC,CAAG,CAAC,CAAC,KAAK,CAAC,EAAE,CAAC;gBAC3C,KAAK,CAAC,OAAO,GAAG,UAAU,CAAC;gBAC3B,MAAM;YACR,CAAC;QACH,CAAC;QAED,IAAI,KAAK,CAAC,OAAO,KAAK,CAAC,EAAE,CAAC;YACxB,KAAK,CAAC,SAAS,GAAG,IAAI,GAAG,EAAE,CAAC;QAC9B,CAAC,MAAM,IAAI,KAAK,CAAC,OAAO,KAAK,CAAC,EAAE,CAAC;YAC/B,KAAK,CAAC,SAAS,GAAG,IAAI,GAAG,EAAE,CAAC;QAC9B,CAAC,MAAM,IAAI,KAAK,CAAC,OAAO,KAAK,CAAC,EAAE,CAAC;YAC/B,KAAK,CAAC,SAAS,GAAG,IAAI,GAAG,CAAC,CAAC;QAC7B,CAAC,MAAM,CAAC;YACN,MAAM,IAAI,KAAK,CAAC,wBAAwB,CAAC,CAAC;QAC5C,CAAC;QAED,KAAK,CAAC,OAAO,IAAI,CAAC,CAAC;IACrB,CAAC,MAAM,IAAI,KAAK,CAAC,OAAO,GAAG,CAAC,EAAE,CAAC;QAC7B,IAAI,IAAI,IAAI,IAAI,EAAE,CAAC;YACjB,MAAM,IAAI,KAAK,CAAC,wBAAwB,CAAC,CAAC;QAC5C,CAAC;QAED,KAAK,CAAC,SAAS,GAAG,AAAC,KAAK,CAAC,SAAS,IAAI,CAAC,CAAC,EAAI,CAAD,GAAK,GAAG,EAAE,CAAC,CAAC;QACvD,KAAK,CAAC,OAAO,IAAI,CAAC,CAAC;QAEnB,IAAI,KAAK,CAAC,OAAO,KAAK,CAAC,EAAE,CAAC;YACxB,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;QACxB,CAAC;IACH,CAAC;AACH,CAAC"}}, {"offset": {"line": 5229, "column": 0}, "map": {"version": 3, "file": "index.js", "sources": ["../../../src/utils/index.ts"], "sourceRoot": "", "names": [], "mappings": ";AAAA,cAAc,WAAW,CAAC;AAC1B,cAAc,aAAa,CAAC;AAC5B,cAAc,WAAW,CAAC;AAC1B,cAAc,aAAa,CAAC"}}, {"offset": {"line": 5256, "column": 0}, "map": {"version": 3, "file": "cookies.js", "sources": ["../../src/cookies.ts"], "sourceRoot": "", "names": [], "mappings": ";;;;AAAA,OAAO,EAAE,KAAK,EAAE,SAAS,EAAE,MAAM,QAAQ,CAAC;AAE1C,OAAO,EACL,sBAAsB,EACtB,aAAa,EACb,YAAY,EACZ,SAAS,EACT,WAAW,EACX,mBAAmB,EACnB,iBAAiB,GAClB,MAAM,SAAS,CAAC;;;;;;;AAajB,MAAM,aAAa,GAAG,SAAS,CAAC;AAU1B,SAAU,wBAAwB,CACtC,OAQC,EACD,cAAuB;IAEvB,MAAM,OAAO,GAAG,OAAO,CAAC,OAAO,IAAI,IAAI,CAAC;IACxC,MAAM,cAAc,GAAG,OAAO,CAAC,cAAc,CAAC;IAE9C,MAAM,QAAQ,GAA8B,CAAA,CAAE,CAAC;IAC/C,MAAM,YAAY,GAA+B,CAAA,CAAE,CAAC;IAEpD,IAAI,MAAyD,CAAC;IAC9D,IAAI,MAAqB,CAAC;IAE1B,IAAI,OAAO,EAAE,CAAC;QACZ,IAAI,KAAK,IAAI,OAAO,EAAE,CAAC;YACrB,uEAAuE;YACvE,sEAAsE;YACtE,wEAAwE;YACxE,uEAAuE;YACvE,yEAAyE;YACzE,qEAAqE;YACrE,kCAAkC;YAElC,MAAM,YAAY,GAAG,KAAK,EAAE,QAAkB,EAAE,EAAE;gBAChD,yEAAyE;gBACzE,MAAM,UAAU,GAAG,QAAQ,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,CAAG,CAAD;wBAC9C,OAAO;2BACJ,KAAK,CAAC,IAAI,CAAC;4BAAE,MAAM,EAAE,CAAC;wBAAA,CAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAG,CAAD,EAAI,OAAO,CAAA,CAAA,EAAI,CAAC,EAAE,CAAC;qBAC9D,CAAC,CAAC;gBAEH,MAAM,MAAM,GAA8B,EAAE,CAAC;gBAE7C,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,CAAE,CAAC;oBAC9C,MAAM,KAAK,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;oBAE/C,IAAI,CAAC,KAAK,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;wBACxC,SAAS;oBACX,CAAC;oBAED,MAAM,CAAC,IAAI,CAAC;wBAAE,IAAI,EAAE,UAAU,CAAC,CAAC,CAAC;wBAAE,KAAK;oBAAA,CAAE,CAAC,CAAC;gBAC9C,CAAC;gBAED,0CAA0C;gBAE1C,OAAO,MAAM,CAAC;YAChB,CAAC,CAAC;YAEF,MAAM,GAAG,KAAK,EAAE,QAAkB,EAAE,CAAG,CAAD,KAAO,YAAY,CAAC,QAAQ,CAAC,CAAC;YAEpE,IAAI,KAAK,IAAI,OAAO,IAAI,QAAQ,IAAI,OAAO,EAAE,CAAC;gBAC5C,MAAM,GAAG,KAAK,EAAE,UAAU,EAAE,EAAE;oBAC5B,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,CAAE,CAAC;wBAC9C,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;wBAE/C,IAAI,KAAK,EAAE,CAAC;4BACV,MAAM,OAAO,CAAC,GAAI,CAAC,IAAI,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;wBAC3C,CAAC,MAAM,CAAC;4BACN,MAAM,OAAO,CAAC,MAAO,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;wBACvC,CAAC;oBACH,CAAC;gBACH,CAAC,CAAC;YACJ,CAAC,MAAM,IAAI,cAAc,EAAE,CAAC;gBAC1B,MAAM,GAAG,KAAK,IAAI,EAAE;oBAClB,OAAO,CAAC,IAAI,CACV,meAAme,CACpe,CAAC;gBACJ,CAAC,CAAC;YACJ,CAAC,MAAM,CAAC;gBACN,MAAM,IAAI,KAAK,CACb,4JAA4J,CAC7J,CAAC;YACJ,CAAC;QACH,CAAC,MAAM,IAAI,QAAQ,IAAI,OAAO,EAAE,CAAC;YAC/B,MAAM,GAAG,KAAK,IAAI,CAAG,CAAD,KAAO,OAAO,CAAC,MAAO,EAAE,CAAC;YAE7C,IAAI,QAAQ,IAAI,OAAO,EAAE,CAAC;gBACxB,MAAM,GAAG,OAAO,CAAC,MAAO,CAAC;YAC3B,CAAC,MAAM,IAAI,cAAc,EAAE,CAAC;gBAC1B,MAAM,GAAG,KAAK,IAAI,EAAE;oBAClB,OAAO,CAAC,IAAI,CACV,wUAAwU,CACzU,CAAC;gBACJ,CAAC,CAAC;YACJ,CAAC,MAAM,CAAC;gBACN,MAAM,IAAI,KAAK,CACb,gKAAgK,CACjK,CAAC;YACJ,CAAC;QACH,CAAC,MAAM,CAAC;YACN,qHAAqH;YACrH,MAAM,IAAI,KAAK,CACb,CAAA,eAAA,EAAkB,cAAc,CAAC,CAAC,CAAC,oBAAoB,CAAC,CAAC,CAAC,qBAAqB,CAAA,2GAAA,EAA8G,gMAAA,AAAS,EAAE,CAAC,CAAC,EAAC,oIAAoI,CAAC,CAAC,CAAC,EAAE,EAAE,CACvV,CAAC;QACJ,CAAC;IACH,CAAC,MAAM,IAAI,CAAC,cAAc,wLAAI,YAAA,AAAS,EAAE,GAAE,CAAC;QAC1C,6FAA6F;QAE7F,MAAM,YAAY,GAAG,GAAG,EAAE;YACxB,MAAM,MAAM,uJAAG,QAAA,AAAK,EAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;YAEtC,OAAO,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAG,CAAD,AAAE;oBACxC,IAAI;oBACJ,KAAK,EAAE,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE;iBAC1B,CAAC,CAAC,CAAC;QACN,CAAC,CAAC;QAEF,MAAM,GAAG,GAAG,CAAG,CAAD,WAAa,EAAE,CAAC;QAE9B,MAAM,GAAG,CAAC,UAAU,EAAE,EAAE;YACtB,UAAU,CAAC,OAAO,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,EAAE,EAAE;gBAC9C,QAAQ,CAAC,MAAM,OAAG,4JAAA,AAAS,EAAC,IAAI,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;YACpD,CAAC,CAAC,CAAC;QACL,CAAC,CAAC;IACJ,CAAC,MAAM,IAAI,cAAc,EAAE,CAAC;QAC1B,MAAM,IAAI,KAAK,CACb,yLAAyL,CAC1L,CAAC;IACJ,CAAC,MAAM,CAAC;QACN,yIAAyI;QACzI,MAAM,GAAG,GAAG,EAAE;YACZ,OAAO,EAAE,CAAC;QACZ,CAAC,CAAC;QAEF,uHAAuH;QACvH,MAAM,GAAG,GAAG,EAAE;YACZ,MAAM,IAAI,KAAK,CACb,yPAAyP,CAC1P,CAAC;QACJ,CAAC,CAAC;IACJ,CAAC;IAED,IAAI,CAAC,cAAc,EAAE,CAAC;QACpB,6DAA6D;QAC7D,6DAA6D;QAC7D,6DAA6D;QAC7D,6DAA6D;QAC7D,4CAA4C;QAC5C,OAAO;YACL,MAAM,EAAE,uBAAuB;YAC/B,MAAM,EAAE,uBAAuB;YAC/B,QAAQ,EAAE,uBAAuB;YACjC,YAAY,EAAE,uBAAuB;YACrC,OAAO,EAAE;gBACP,QAAQ,EAAE,KAAK;gBACf,OAAO,EAAE,KAAK,EAAE,GAAW,EAAE,EAAE;oBAC7B,MAAM,UAAU,GAAG,MAAM,MAAM,CAAC;wBAAC,GAAG;qBAAC,CAAC,CAAC;oBACvC,MAAM,aAAa,GAAG,0LAAM,gBAAA,AAAa,EACvC,GAAG,EACH,KAAK,EAAE,SAAiB,EAAE,EAAE;wBAC1B,MAAM,MAAM,GACV,UAAU,EAAE,IAAI,CAAC,CAAC,EAAE,IAAI,EAAE,EAAE,CAAG,CAAD,GAAK,KAAK,SAAS,CAAC,IAAI,IAAI,CAAC;wBAE7D,IAAI,CAAC,MAAM,EAAE,CAAC;4BACZ,OAAO,IAAI,CAAC;wBACd,CAAC;wBAED,OAAO,MAAM,CAAC,KAAK,CAAC;oBACtB,CAAC,CACF,CAAC;oBAEF,IAAI,CAAC,aAAa,EAAE,CAAC;wBACnB,OAAO,IAAI,CAAC;oBACd,CAAC;oBAED,IAAI,OAAO,GAAG,aAAa,CAAC;oBAE5B,IAAI,aAAa,CAAC,UAAU,CAAC,aAAa,CAAC,EAAE,CAAC;wBAC5C,OAAO,OAAG,wMAAA,AAAmB,EAC3B,aAAa,CAAC,SAAS,CAAC,aAAa,CAAC,MAAM,CAAC,CAC9C,CAAC;oBACJ,CAAC;oBAED,OAAO,OAAO,CAAC;gBACjB,CAAC;gBACD,OAAO,EAAE,KAAK,EAAE,GAAW,EAAE,KAAa,EAAE,EAAE;oBAC5C,MAAM,UAAU,GAAG,MAAM,MAAM,CAAC;wBAAC,GAAG;qBAAC,CAAC,CAAC;oBACvC,MAAM,WAAW,GAAG,UAAU,EAAE,GAAG,CAAC,CAAC,EAAE,IAAI,EAAE,EAAE,CAAG,CAAD,GAAK,CAAC,IAAI,EAAE,CAAC;oBAE9D,MAAM,aAAa,GAAG,IAAI,GAAG,CAC3B,WAAW,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,EAAE,mLAAC,cAAA,AAAW,EAAC,IAAI,EAAE,GAAG,CAAC,CAAC,CACrD,CAAC;oBAEF,IAAI,OAAO,GAAG,KAAK,CAAC;oBAEpB,IAAI,cAAc,KAAK,WAAW,EAAE,CAAC;wBACnC,OAAO,GAAG,aAAa,yLAAG,oBAAA,AAAiB,EAAC,KAAK,CAAC,CAAC;oBACrD,CAAC;oBAED,MAAM,UAAU,GAAG,mMAAA,AAAY,EAAC,GAAG,EAAE,OAAO,CAAC,CAAC;oBAE9C,UAAU,CAAC,OAAO,CAAC,CAAC,EAAE,IAAI,EAAE,EAAE,EAAE;wBAC9B,aAAa,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;oBAC7B,CAAC,CAAC,CAAC;oBAEH,MAAM,mBAAmB,GAAG;wBAC1B,oLAAG,0BAAsB;wBACzB,GAAG,OAAO,EAAE,aAAa;wBACzB,MAAM,EAAE,CAAC;qBACV,CAAC;oBACF,MAAM,gBAAgB,GAAG;wBACvB,GAAG,2MAAsB;wBACzB,GAAG,OAAO,EAAE,aAAa;wBACzB,MAAM,oLAAE,yBAAsB,CAAC,MAAM;qBACtC,CAAC;oBAEF,iEAAiE;oBACjE,8BAA8B;oBAC9B,OAAO,mBAAmB,CAAC,IAAI,CAAC;oBAChC,OAAO,gBAAgB,CAAC,IAAI,CAAC;oBAE7B,MAAM,QAAQ,GAAG;2BACZ,CAAC;+BAAG,aAAa;yBAAC,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAG,CAAD,AAAE;gCACnC,IAAI;gCACJ,KAAK,EAAE,EAAE;gCACT,OAAO,EAAE,mBAAmB;6BAC7B,CAAC,CAAC;2BACA,UAAU,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE,CAAG,CAAD,AAAE;gCACtC,IAAI;gCACJ,KAAK;gCACL,OAAO,EAAE,gBAAgB;6BAC1B,CAAC,CAAC;qBACJ,CAAC;oBAEF,IAAI,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;wBACxB,MAAM,MAAM,CAAC,QAAQ,CAAC,CAAC;oBACzB,CAAC;gBACH,CAAC;gBACD,UAAU,EAAE,KAAK,EAAE,GAAW,EAAE,EAAE;oBAChC,MAAM,UAAU,GAAG,MAAM,MAAM,CAAC;wBAAC,GAAG;qBAAC,CAAC,CAAC;oBACvC,MAAM,WAAW,GAAG,UAAU,EAAE,GAAG,CAAC,CAAC,EAAE,IAAI,EAAE,EAAE,CAAG,CAAD,GAAK,CAAC,IAAI,EAAE,CAAC;oBAC9D,MAAM,aAAa,GAAG,WAAW,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,EAAE,mLAChD,cAAA,AAAW,EAAC,IAAI,EAAE,GAAG,CAAC,CACvB,CAAC;oBAEF,MAAM,mBAAmB,GAAG;wBAC1B,qLAAG,yBAAsB;wBACzB,GAAG,OAAO,EAAE,aAAa;wBACzB,MAAM,EAAE,CAAC;qBACV,CAAC;oBAEF,iEAAiE;oBACjE,8BAA8B;oBAC9B,OAAO,mBAAmB,CAAC,IAAI,CAAC;oBAEhC,IAAI,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;wBAC7B,MAAM,MAAM,CACV,aAAa,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAG,CAAD,AAAE;gCAC3B,IAAI;gCACJ,KAAK,EAAE,EAAE;gCACT,OAAO,EAAE,mBAAmB;6BAC7B,CAAC,CAAC,CACJ,CAAC;oBACJ,CAAC;gBACH,CAAC;aACF;SACF,CAAC;IACJ,CAAC;IAED,qEAAqE;IACrE,iEAAiE;IACjE,mEAAmE;IACnE,gEAAgE;IAChE,oEAAoE;IACpE,sEAAsE;IACtE,6BAA6B;IAC7B,OAAO;QACL,MAAM;QACN,MAAM;QACN,QAAQ;QACR,YAAY;QACZ,OAAO,EAAE;YACP,oDAAoD;YACpD,mDAAmD;YACnD,wBAAwB;YACxB,QAAQ,EAAE,IAAI;YACd,OAAO,EAAE,KAAK,EAAE,GAAW,EAAE,EAAE;gBAC7B,IAAI,OAAO,QAAQ,CAAC,GAAG,CAAC,KAAK,QAAQ,EAAE,CAAC;oBACtC,OAAO,QAAQ,CAAC,GAAG,CAAC,CAAC;gBACvB,CAAC;gBAED,IAAI,YAAY,CAAC,GAAG,CAAC,EAAE,CAAC;oBACtB,OAAO,IAAI,CAAC;gBACd,CAAC;gBAED,MAAM,UAAU,GAAG,MAAM,MAAM,CAAC;oBAAC,GAAG;iBAAC,CAAC,CAAC;gBACvC,MAAM,aAAa,GAAG,UAAM,gMAAa,AAAb,EAC1B,GAAG,EACH,KAAK,EAAE,SAAiB,EAAE,EAAE;oBAC1B,MAAM,MAAM,GACV,UAAU,EAAE,IAAI,CAAC,CAAC,EAAE,IAAI,EAAE,EAAE,CAAG,CAAD,GAAK,KAAK,SAAS,CAAC,IAAI,IAAI,CAAC;oBAE7D,IAAI,CAAC,MAAM,EAAE,CAAC;wBACZ,OAAO,IAAI,CAAC;oBACd,CAAC;oBAED,OAAO,MAAM,CAAC,KAAK,CAAC;gBACtB,CAAC,CACF,CAAC;gBAEF,IAAI,CAAC,aAAa,EAAE,CAAC;oBACnB,OAAO,IAAI,CAAC;gBACd,CAAC;gBAED,IAAI,OAAO,GAAG,aAAa,CAAC;gBAE5B,IACE,OAAO,aAAa,KAAK,QAAQ,IACjC,aAAa,CAAC,UAAU,CAAC,aAAa,CAAC,EACvC,CAAC;oBACD,OAAO,wLAAG,uBAAA,AAAmB,EAC3B,aAAa,CAAC,SAAS,CAAC,aAAa,CAAC,MAAM,CAAC,CAC9C,CAAC;gBACJ,CAAC;gBAED,OAAO,OAAO,CAAC;YACjB,CAAC;YACD,OAAO,EAAE,KAAK,EAAE,GAAW,EAAE,KAAa,EAAE,EAAE;gBAC5C,uEAAuE;gBACvE,qEAAqE;gBACrE,oEAAoE;gBACpE,iBAAiB;gBACjB,IAAI,GAAG,CAAC,QAAQ,CAAC,gBAAgB,CAAC,EAAE,CAAC;oBACnC,MAAM,kBAAkB,CACtB;wBACE,MAAM;wBACN,MAAM;wBACN,8CAA8C;wBAC9C,QAAQ,EAAE;4BAAE,CAAC,GAAG,CAAC,EAAE,KAAK;wBAAA,CAAE;wBAC1B,mCAAmC;wBACnC,YAAY,EAAE,CAAA,CAAE;qBACjB,EACD;wBACE,aAAa,EAAE,OAAO,EAAE,aAAa,IAAI,IAAI;wBAC7C,cAAc;qBACf,CACF,CAAC;gBACJ,CAAC;gBAED,QAAQ,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;gBACtB,OAAO,YAAY,CAAC,GAAG,CAAC,CAAC;YAC3B,CAAC;YACD,UAAU,EAAE,KAAK,EAAE,GAAW,EAAE,EAAE;gBAChC,uEAAuE;gBACvE,qEAAqE;gBACrE,uDAAuD;gBACvD,uEAAuE;gBACvE,wCAAwC;gBACxC,OAAO,QAAQ,CAAC,GAAG,CAAC,CAAC;gBACrB,YAAY,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC;YAC3B,CAAC;SACF;KACF,CAAC;AACJ,CAAC;AAOM,KAAK,UAAU,kBAAkB,CACtC,EACE,MAAM,EACN,MAAM,EACN,QAAQ,EACR,YAAY,EAMb,EACD,OAGC;IAED,MAAM,cAAc,GAAG,OAAO,CAAC,cAAc,CAAC;IAC9C,MAAM,aAAa,GAAG,OAAO,CAAC,aAAa,IAAI,IAAI,CAAC;IAEpD,MAAM,UAAU,GAAG,MAAM,MAAM,CAAC;WAC1B,QAAQ,CAAC,CAAC,CAAE,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAc,CAAC,CAAC,CAAC,EAAE,CAAC;WACpD,YAAY,CAAC,CAAC,CAAE,MAAM,CAAC,IAAI,CAAC,YAAY,CAAc,CAAC,CAAC,CAAC,EAAE,CAAC;KACjE,CAAC,CAAC;IACH,MAAM,WAAW,GAAG,UAAU,EAAE,GAAG,CAAC,CAAC,EAAE,IAAI,EAAE,EAAE,CAAG,CAAD,GAAK,CAAC,IAAI,EAAE,CAAC;IAE9D,MAAM,aAAa,GAAa,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,OAAO,CAC/D,CAAC,QAAQ,EAAE,EAAE;QACX,OAAO,WAAW,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,EAAE,mLAAC,cAAA,AAAW,EAAC,IAAI,EAAE,QAAQ,CAAC,CAAC,CAAC;IACnE,CAAC,CACF,CAAC;IAEF,MAAM,UAAU,GAAG,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,EAAE;QAC5D,MAAM,4BAA4B,GAAG,IAAI,GAAG,CAC1C,WAAW,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,EAAE,mLAAC,cAAA,AAAW,EAAC,IAAI,EAAE,QAAQ,CAAC,CAAC,CAC1D,CAAC;QAEF,IAAI,OAAO,GAAG,QAAQ,CAAC,QAAQ,CAAC,CAAC;QAEjC,IAAI,cAAc,KAAK,WAAW,EAAE,CAAC;YACnC,OAAO,GAAG,aAAa,yLAAG,oBAAA,AAAiB,EAAC,OAAO,CAAC,CAAC;QACvD,CAAC;QAED,MAAM,MAAM,uLAAG,eAAA,AAAY,EAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;QAE/C,MAAM,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE;YACvB,4BAA4B,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAClD,CAAC,CAAC,CAAC;QAEH,aAAa,CAAC,IAAI,CAAC,GAAG,4BAA4B,CAAC,CAAC;QAEpD,OAAO,MAAM,CAAC;IAChB,CAAC,CAAC,CAAC;IAEH,MAAM,mBAAmB,GAAG;QAC1B,qLAAG,yBAAsB;QACzB,GAAG,aAAa;QAChB,MAAM,EAAE,CAAC;KACV,CAAC;IACF,MAAM,gBAAgB,GAAG;QACvB,qLAAG,yBAAsB;QACzB,GAAG,aAAa;QAChB,MAAM,oLAAE,yBAAsB,CAAC,MAAM;KACtC,CAAC;IAEF,iEAAiE;IACjE,8BAA8B;IAC9B,OAAQ,mBAA2B,CAAC,IAAI,CAAC;IACzC,OAAQ,gBAAwB,CAAC,IAAI,CAAC;IAEtC,MAAM,MAAM,CAAC;WACR,aAAa,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAG,CAAD,AAAE;gBAC9B,IAAI;gBACJ,KAAK,EAAE,EAAE;gBACT,OAAO,EAAE,mBAAmB;aAC7B,CAAC,CAAC;WACA,UAAU,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE,CAAG,CAAD,AAAE;gBACtC,IAAI;gBACJ,KAAK;gBACL,OAAO,EAAE,gBAAgB;aAC1B,CAAC,CAAC;KACJ,CAAC,CAAC;AACL,CAAC"}}, {"offset": {"line": 5607, "column": 0}, "map": {"version": 3, "file": "createBrowserClient.js", "sources": ["../../src/createBrowserClient.ts"], "sourceRoot": "", "names": [], "mappings": ";;;AAAA,OAAO,EAAE,YAAY,EAAkB,MAAM,uBAAuB,CAAC;AAMrE,OAAO,EAAE,OAAO,EAAE,MAAM,WAAW,CAAC;AACpC,OAAO,EAAE,SAAS,EAAE,MAAM,SAAS,CAAC;;AAQpC,OAAO,EAAE,wBAAwB,EAAE,MAAM,WAAW,CAAC;;;;;AAErD,IAAI,mBAA8D,CAAC;AA8D7D,SAAU,mBAAmB,CASjC,WAAmB,EACnB,WAAmB,EACnB,OAKC;IAED,2HAA2H;IAC3H,MAAM,kBAAkB,GACtB,OAAO,EAAE,WAAW,KAAK,IAAI,IAC5B,CAAC,CAAC,OAAO,IAAI,CAAC,CAAC,aAAa,IAAI,OAAO,CAAC,CAAC,wLAAI,YAAA,AAAS,EAAE,CAAC,CAAC;IAE7D,IAAI,kBAAkB,IAAI,mBAAmB,EAAE,CAAC;QAC9C,OAAO,mBAAmB,CAAC;IAC7B,CAAC;IAED,IAAI,CAAC,WAAW,IAAI,CAAC,WAAW,EAAE,CAAC;QACjC,MAAM,IAAI,KAAK,CACb,CAAA,mNAAA,CAAqN,CACtN,CAAC;IACJ,CAAC;IAED,MAAM,EAAE,OAAO,EAAE,8KAAG,2BAAA,AAAwB,EAC1C;QACE,GAAG,OAAO;QACV,cAAc,EAAE,OAAO,EAAE,cAAc,IAAI,WAAW;KACvD,EACD,KAAK,CACN,CAAC;IAEF,MAAM,MAAM,uMAAG,eAAA,AAAY,EACzB,WAAW,EACX,WAAW,EACX;QACE,GAAG,OAAO;QACV,MAAM,EAAE;YACN,GAAG,OAAO,EAAE,MAAM;YAClB,OAAO,EAAE;gBACP,GAAG,OAAO,EAAE,MAAM,EAAE,OAAO;gBAC3B,eAAe,EAAE,CAAA,aAAA,yKAAgB,UAAO,CAAA,oBAAA,CAAsB;aAC/D;SACF;QACD,IAAI,EAAE;YACJ,GAAG,OAAO,EAAE,IAAI;YAChB,GAAG,AAAC,OAAO,EAAE,aAAa,EAAE,IAAI,GAC5B;gBAAE,UAAU,EAAE,OAAO,CAAC,aAAa,CAAC,IAAI;YAAA,CAAE,GAC1C,IAAI,CAAC;YACT,QAAQ,EAAE,MAAM;YAChB,gBAAgB,sLAAE,YAAA,AAAS,EAAE;YAC7B,kBAAkB,sLAAE,YAAA,AAAS,EAAE;YAC/B,cAAc,EAAE,IAAI;YACpB,OAAO;SACR;KACF,CACF,CAAC;IAEF,IAAI,kBAAkB,EAAE,CAAC;QACvB,mBAAmB,GAAG,MAAM,CAAC;IAC/B,CAAC;IAED,OAAO,MAAM,CAAC;AAChB,CAAC"}}, {"offset": {"line": 5665, "column": 0}, "map": {"version": 3, "file": "createServerClient.js", "sources": ["../../src/createServerClient.ts"], "sourceRoot": "", "names": [], "mappings": ";;;AAAA,OAAO,EAEL,YAAY,GAEb,MAAM,uBAAuB,CAAC;AAM/B,OAAO,EAAE,OAAO,EAAE,MAAM,WAAW,CAAC;AACpC,OAAO,EAAE,wBAAwB,EAAE,kBAAkB,EAAE,MAAM,WAAW,CAAC;;;;AA6GnE,SAAU,kBAAkB,CAShC,WAAmB,EACnB,WAAmB,EACnB,OAIC;IAED,IAAI,CAAC,WAAW,IAAI,CAAC,WAAW,EAAE,CAAC;QACjC,MAAM,IAAI,KAAK,CACb,CAAA,gMAAA,CAAkM,CACnM,CAAC;IACJ,CAAC;IAED,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,YAAY,EAAE,8KACvD,2BAAA,AAAwB,EACtB;QACE,GAAG,OAAO;QACV,cAAc,EAAE,OAAO,EAAE,cAAc,IAAI,WAAW;KACvD,EACD,IAAI,CACL,CAAC;IAEJ,MAAM,MAAM,IAAG,kNAAA,AAAY,EACzB,WAAW,EACX,WAAW,EACX;QACE,GAAG,OAAO;QACV,MAAM,EAAE;YACN,GAAG,OAAO,EAAE,MAAM;YAClB,OAAO,EAAE;gBACP,GAAG,OAAO,EAAE,MAAM,EAAE,OAAO;gBAC3B,eAAe,EAAE,CAAA,aAAA,yKAAgB,UAAO,CAAA,mBAAA,CAAqB;aAC9D;SACF;QACD,IAAI,EAAE;YACJ,GAAG,AAAC,OAAO,EAAE,aAAa,EAAE,IAAI,GAC5B;gBAAE,UAAU,EAAE,OAAO,CAAC,aAAa,CAAC,IAAI;YAAA,CAAE,GAC1C,IAAI,CAAC;YACT,GAAG,OAAO,EAAE,IAAI;YAChB,QAAQ,EAAE,MAAM;YAChB,gBAAgB,EAAE,KAAK;YACvB,kBAAkB,EAAE,KAAK;YACzB,cAAc,EAAE,IAAI;YACpB,OAAO;SACR;KACF,CACF,CAAC;IAEF,MAAM,CAAC,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,KAAsB,EAAE,EAAE;QAC7D,gEAAgE;QAChE,kEAAkE;QAClE,iEAAiE;QACjE,QAAQ;QACR,MAAM,iBAAiB,GACrB,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,MAAM,GAAG,CAAC,IAAI,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC;QAE3E,IACE,iBAAiB,IACjB,CAAC,KAAK,KAAK,WAAW,IACpB,KAAK,KAAK,iBAAiB,IAC3B,KAAK,KAAK,cAAc,IACxB,KAAK,KAAK,mBAAmB,IAC7B,KAAK,KAAK,YAAY,IACtB,KAAK,KAAK,wBAAwB,CAAC,EACrC,CAAC;YACD,iLAAM,qBAAA,AAAkB,EACtB;gBAAE,MAAM;gBAAE,MAAM;gBAAE,QAAQ;gBAAE,YAAY;YAAA,CAAE,EAC1C;gBACE,aAAa,EAAE,OAAO,EAAE,aAAa,IAAI,IAAI;gBAC7C,cAAc,EAAE,OAAO,EAAE,cAAc,IAAI,WAAW;aACvD,CACF,CAAC;QACJ,CAAC;IACH,CAAC,CAAC,CAAC;IAEH,OAAO,MAAM,CAAC;AAChB,CAAC"}}, {"offset": {"line": 5728, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/@supabase/ssr/dist/module/types.js"], "sourcesContent": ["//# sourceMappingURL=types.js.map"], "names": [], "mappings": "AAAA,iCAAiC", "ignoreList": [0]}}, {"offset": {"line": 5735, "column": 0}, "map": {"version": 3, "file": "index.js", "sources": ["../../src/index.ts"], "sourceRoot": "", "names": [], "mappings": ";AAAA,cAAc,uBAAuB,CAAC;AACtC,cAAc,sBAAsB,CAAC;AACrC,cAAc,SAAS,CAAC;AACxB,cAAc,SAAS,CAAC"}}, {"offset": {"line": 5761, "column": 0}, "map": {"version": 3, "file": "index.js", "sources": ["turbopack:///[project]/node_modules/cookie/src/index.ts"], "sourceRoot": "", "sourcesContent": ["/**\n * RegExp to match cookie-name in RFC 6265 sec 4.1.1\n * This refers out to the obsoleted definition of token in RFC 2616 sec 2.2\n * which has been replaced by the token definition in RFC 7230 appendix B.\n *\n * cookie-name       = token\n * token             = 1*tchar\n * tchar             = \"!\" / \"#\" / \"$\" / \"%\" / \"&\" / \"'\" /\n *                     \"*\" / \"+\" / \"-\" / \".\" / \"^\" / \"_\" /\n *                     \"`\" / \"|\" / \"~\" / DIGIT / ALPHA\n *\n * Note: Allowing more characters - https://github.com/jshttp/cookie/issues/191\n * Allow same range as cookie value, except `=`, which delimits end of name.\n */\nconst cookieNameRegExp = /^[\\u0021-\\u003A\\u003C\\u003E-\\u007E]+$/;\n\n/**\n * RegExp to match cookie-value in RFC 6265 sec 4.1.1\n *\n * cookie-value      = *cookie-octet / ( DQUOTE *cookie-octet DQUOTE )\n * cookie-octet      = %x21 / %x23-2B / %x2D-3A / %x3C-5B / %x5D-7E\n *                     ; US-ASCII characters excluding CTLs,\n *                     ; whitespace DQUOTE, comma, semicolon,\n *                     ; and backslash\n *\n * Allowing more characters: https://github.com/jshttp/cookie/issues/191\n * Comma, backslash, and DQUOTE are not part of the parsing algorithm.\n */\nconst cookieValueRegExp = /^[\\u0021-\\u003A\\u003C-\\u007E]*$/;\n\n/**\n * RegExp to match domain-value in RFC 6265 sec 4.1.1\n *\n * domain-value      = <subdomain>\n *                     ; defined in [RFC1034], Section 3.5, as\n *                     ; enhanced by [RFC1123], Section 2.1\n * <subdomain>       = <label> | <subdomain> \".\" <label>\n * <label>           = <let-dig> [ [ <ldh-str> ] <let-dig> ]\n *                     Labels must be 63 characters or less.\n *                     'let-dig' not 'letter' in the first char, per RFC1123\n * <ldh-str>         = <let-dig-hyp> | <let-dig-hyp> <ldh-str>\n * <let-dig-hyp>     = <let-dig> | \"-\"\n * <let-dig>         = <letter> | <digit>\n * <letter>          = any one of the 52 alphabetic characters A through Z in\n *                     upper case and a through z in lower case\n * <digit>           = any one of the ten digits 0 through 9\n *\n * Keep support for leading dot: https://github.com/jshttp/cookie/issues/173\n *\n * > (Note that a leading %x2E (\".\"), if present, is ignored even though that\n * character is not permitted, but a trailing %x2E (\".\"), if present, will\n * cause the user agent to ignore the attribute.)\n */\nconst domainValueRegExp =\n  /^([.]?[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)([.][a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)*$/i;\n\n/**\n * RegExp to match path-value in RFC 6265 sec 4.1.1\n *\n * path-value        = <any CHAR except CTLs or \";\">\n * CHAR              = %x01-7F\n *                     ; defined in RFC 5234 appendix B.1\n */\nconst pathValueRegExp = /^[\\u0020-\\u003A\\u003D-\\u007E]*$/;\n\nconst __toString = Object.prototype.toString;\n\nconst NullObject = /* @__PURE__ */ (() => {\n  const C = function () {};\n  C.prototype = Object.create(null);\n  return C;\n})() as unknown as { new (): any };\n\n/**\n * Parse options.\n */\nexport interface ParseOptions {\n  /**\n   * Specifies a function that will be used to decode a [cookie-value](https://datatracker.ietf.org/doc/html/rfc6265#section-4.1.1).\n   * Since the value of a cookie has a limited character set (and must be a simple string), this function can be used to decode\n   * a previously-encoded cookie value into a JavaScript string.\n   *\n   * The default function is the global `decodeURIComponent`, wrapped in a `try..catch`. If an error\n   * is thrown it will return the cookie's original value. If you provide your own encode/decode\n   * scheme you must ensure errors are appropriately handled.\n   *\n   * @default decode\n   */\n  decode?: (str: string) => string | undefined;\n}\n\n/**\n * Parse a cookie header.\n *\n * Parse the given cookie header string into an object\n * The object has the various cookies as keys(names) => values\n */\nexport function parse(\n  str: string,\n  options?: ParseOptions,\n): Record<string, string | undefined> {\n  const obj: Record<string, string | undefined> = new NullObject();\n  const len = str.length;\n  // RFC 6265 sec 4.1.1, RFC 2616 2.2 defines a cookie name consists of one char minimum, plus '='.\n  if (len < 2) return obj;\n\n  const dec = options?.decode || decode;\n  let index = 0;\n\n  do {\n    const eqIdx = str.indexOf(\"=\", index);\n    if (eqIdx === -1) break; // No more cookie pairs.\n\n    const colonIdx = str.indexOf(\";\", index);\n    const endIdx = colonIdx === -1 ? len : colonIdx;\n\n    if (eqIdx > endIdx) {\n      // backtrack on prior semicolon\n      index = str.lastIndexOf(\";\", eqIdx - 1) + 1;\n      continue;\n    }\n\n    const keyStartIdx = startIndex(str, index, eqIdx);\n    const keyEndIdx = endIndex(str, eqIdx, keyStartIdx);\n    const key = str.slice(keyStartIdx, keyEndIdx);\n\n    // only assign once\n    if (obj[key] === undefined) {\n      let valStartIdx = startIndex(str, eqIdx + 1, endIdx);\n      let valEndIdx = endIndex(str, endIdx, valStartIdx);\n\n      const value = dec(str.slice(valStartIdx, valEndIdx));\n      obj[key] = value;\n    }\n\n    index = endIdx + 1;\n  } while (index < len);\n\n  return obj;\n}\n\nfunction startIndex(str: string, index: number, max: number) {\n  do {\n    const code = str.charCodeAt(index);\n    if (code !== 0x20 /*   */ && code !== 0x09 /* \\t */) return index;\n  } while (++index < max);\n  return max;\n}\n\nfunction endIndex(str: string, index: number, min: number) {\n  while (index > min) {\n    const code = str.charCodeAt(--index);\n    if (code !== 0x20 /*   */ && code !== 0x09 /* \\t */) return index + 1;\n  }\n  return min;\n}\n\n/**\n * Serialize options.\n */\nexport interface SerializeOptions {\n  /**\n   * Specifies a function that will be used to encode a [cookie-value](https://datatracker.ietf.org/doc/html/rfc6265#section-4.1.1).\n   * Since value of a cookie has a limited character set (and must be a simple string), this function can be used to encode\n   * a value into a string suited for a cookie's value, and should mirror `decode` when parsing.\n   *\n   * @default encodeURIComponent\n   */\n  encode?: (str: string) => string;\n  /**\n   * Specifies the `number` (in seconds) to be the value for the [`Max-Age` `Set-Cookie` attribute](https://tools.ietf.org/html/rfc6265#section-5.2.2).\n   *\n   * The [cookie storage model specification](https://tools.ietf.org/html/rfc6265#section-5.3) states that if both `expires` and\n   * `maxAge` are set, then `maxAge` takes precedence, but it is possible not all clients by obey this,\n   * so if both are set, they should point to the same date and time.\n   */\n  maxAge?: number;\n  /**\n   * Specifies the `Date` object to be the value for the [`Expires` `Set-Cookie` attribute](https://tools.ietf.org/html/rfc6265#section-5.2.1).\n   * When no expiration is set clients consider this a \"non-persistent cookie\" and delete it the current session is over.\n   *\n   * The [cookie storage model specification](https://tools.ietf.org/html/rfc6265#section-5.3) states that if both `expires` and\n   * `maxAge` are set, then `maxAge` takes precedence, but it is possible not all clients by obey this,\n   * so if both are set, they should point to the same date and time.\n   */\n  expires?: Date;\n  /**\n   * Specifies the value for the [`Domain` `Set-Cookie` attribute](https://tools.ietf.org/html/rfc6265#section-5.2.3).\n   * When no domain is set clients consider the cookie to apply to the current domain only.\n   */\n  domain?: string;\n  /**\n   * Specifies the value for the [`Path` `Set-Cookie` attribute](https://tools.ietf.org/html/rfc6265#section-5.2.4).\n   * When no path is set, the path is considered the [\"default path\"](https://tools.ietf.org/html/rfc6265#section-5.1.4).\n   */\n  path?: string;\n  /**\n   * Enables the [`HttpOnly` `Set-Cookie` attribute](https://tools.ietf.org/html/rfc6265#section-5.2.6).\n   * When enabled, clients will not allow client-side JavaScript to see the cookie in `document.cookie`.\n   */\n  httpOnly?: boolean;\n  /**\n   * Enables the [`Secure` `Set-Cookie` attribute](https://tools.ietf.org/html/rfc6265#section-5.2.5).\n   * When enabled, clients will only send the cookie back if the browser has a HTTPS connection.\n   */\n  secure?: boolean;\n  /**\n   * Enables the [`Partitioned` `Set-Cookie` attribute](https://tools.ietf.org/html/draft-cutler-httpbis-partitioned-cookies/).\n   * When enabled, clients will only send the cookie back when the current domain _and_ top-level domain matches.\n   *\n   * This is an attribute that has not yet been fully standardized, and may change in the future.\n   * This also means clients may ignore this attribute until they understand it. More information\n   * about can be found in [the proposal](https://github.com/privacycg/CHIPS).\n   */\n  partitioned?: boolean;\n  /**\n   * Specifies the value for the [`Priority` `Set-Cookie` attribute](https://tools.ietf.org/html/draft-west-cookie-priority-00#section-4.1).\n   *\n   * - `'low'` will set the `Priority` attribute to `Low`.\n   * - `'medium'` will set the `Priority` attribute to `Medium`, the default priority when not set.\n   * - `'high'` will set the `Priority` attribute to `High`.\n   *\n   * More information about priority levels can be found in [the specification](https://tools.ietf.org/html/draft-west-cookie-priority-00#section-4.1).\n   */\n  priority?: \"low\" | \"medium\" | \"high\";\n  /**\n   * Specifies the value for the [`SameSite` `Set-Cookie` attribute](https://tools.ietf.org/html/draft-ietf-httpbis-rfc6265bis-09#section-5.4.7).\n   *\n   * - `true` will set the `SameSite` attribute to `Strict` for strict same site enforcement.\n   * - `'lax'` will set the `SameSite` attribute to `Lax` for lax same site enforcement.\n   * - `'none'` will set the `SameSite` attribute to `None` for an explicit cross-site cookie.\n   * - `'strict'` will set the `SameSite` attribute to `Strict` for strict same site enforcement.\n   *\n   * More information about enforcement levels can be found in [the specification](https://tools.ietf.org/html/draft-ietf-httpbis-rfc6265bis-09#section-5.4.7).\n   */\n  sameSite?: boolean | \"lax\" | \"strict\" | \"none\";\n}\n\n/**\n * Serialize data into a cookie header.\n *\n * Serialize a name value pair into a cookie string suitable for\n * http headers. An optional options object specifies cookie parameters.\n *\n * serialize('foo', 'bar', { httpOnly: true })\n *   => \"foo=bar; httpOnly\"\n */\nexport function serialize(\n  name: string,\n  val: string,\n  options?: SerializeOptions,\n): string {\n  const enc = options?.encode || encodeURIComponent;\n\n  if (!cookieNameRegExp.test(name)) {\n    throw new TypeError(`argument name is invalid: ${name}`);\n  }\n\n  const value = enc(val);\n\n  if (!cookieValueRegExp.test(value)) {\n    throw new TypeError(`argument val is invalid: ${val}`);\n  }\n\n  let str = name + \"=\" + value;\n  if (!options) return str;\n\n  if (options.maxAge !== undefined) {\n    if (!Number.isInteger(options.maxAge)) {\n      throw new TypeError(`option maxAge is invalid: ${options.maxAge}`);\n    }\n\n    str += \"; Max-Age=\" + options.maxAge;\n  }\n\n  if (options.domain) {\n    if (!domainValueRegExp.test(options.domain)) {\n      throw new TypeError(`option domain is invalid: ${options.domain}`);\n    }\n\n    str += \"; Domain=\" + options.domain;\n  }\n\n  if (options.path) {\n    if (!pathValueRegExp.test(options.path)) {\n      throw new TypeError(`option path is invalid: ${options.path}`);\n    }\n\n    str += \"; Path=\" + options.path;\n  }\n\n  if (options.expires) {\n    if (\n      !isDate(options.expires) ||\n      !Number.isFinite(options.expires.valueOf())\n    ) {\n      throw new TypeError(`option expires is invalid: ${options.expires}`);\n    }\n\n    str += \"; Expires=\" + options.expires.toUTCString();\n  }\n\n  if (options.httpOnly) {\n    str += \"; HttpOnly\";\n  }\n\n  if (options.secure) {\n    str += \"; Secure\";\n  }\n\n  if (options.partitioned) {\n    str += \"; Partitioned\";\n  }\n\n  if (options.priority) {\n    const priority =\n      typeof options.priority === \"string\"\n        ? options.priority.toLowerCase()\n        : undefined;\n    switch (priority) {\n      case \"low\":\n        str += \"; Priority=Low\";\n        break;\n      case \"medium\":\n        str += \"; Priority=Medium\";\n        break;\n      case \"high\":\n        str += \"; Priority=High\";\n        break;\n      default:\n        throw new TypeError(`option priority is invalid: ${options.priority}`);\n    }\n  }\n\n  if (options.sameSite) {\n    const sameSite =\n      typeof options.sameSite === \"string\"\n        ? options.sameSite.toLowerCase()\n        : options.sameSite;\n    switch (sameSite) {\n      case true:\n      case \"strict\":\n        str += \"; SameSite=Strict\";\n        break;\n      case \"lax\":\n        str += \"; SameSite=Lax\";\n        break;\n      case \"none\":\n        str += \"; SameSite=None\";\n        break;\n      default:\n        throw new TypeError(`option sameSite is invalid: ${options.sameSite}`);\n    }\n  }\n\n  return str;\n}\n\n/**\n * URL-decode string value. Optimized to skip native call when no %.\n */\nfunction decode(str: string): string {\n  if (str.indexOf(\"%\") === -1) return str;\n\n  try {\n    return decodeURIComponent(str);\n  } catch (e) {\n    return str;\n  }\n}\n\n/**\n * Determine if value is a Date.\n */\nfunction isDate(val: any): val is Date {\n  return __toString.call(val) === \"[object Date]\";\n}\n"], "names": [], "mappings": ";;;;AAiGA,QAAA,KAAA,GAAA,MA0CC;AA4GD,QAAA,SAAA,GAAA,UA6GC;AApWD;;;;;;;;;;;;;GAaG,CACH,MAAM,gBAAgB,GAAG,uCAAuC,CAAC;AAEjE;;;;;;;;;;;GAWG,CACH,MAAM,iBAAiB,GAAG,iCAAiC,CAAC;AAE5D;;;;;;;;;;;;;;;;;;;;;;GAsBG,CACH,MAAM,iBAAiB,GACrB,qFAAqF,CAAC;AAExF;;;;;;GAMG,CACH,MAAM,eAAe,GAAG,iCAAiC,CAAC;AAE1D,MAAM,UAAU,GAAG,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC;AAE7C,MAAM,UAAU,GAAG,aAAA,EAAe,CAAC,CAAC,GAAG,EAAE;IACvC,MAAM,CAAC,GAAG,YAAa,CAAC,CAAC;IACzB,CAAC,CAAC,SAAS,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;IAClC,OAAO,CAAC,CAAC;AACX,CAAC,CAAC,EAAgC,CAAC;AAoBnC;;;;;GAKG,CACH,SAAgB,KAAK,CACnB,GAAW,EACX,OAAsB;IAEtB,MAAM,GAAG,GAAuC,IAAI,UAAU,EAAE,CAAC;IACjE,MAAM,GAAG,GAAG,GAAG,CAAC,MAAM,CAAC;IACvB,iGAAiG;IACjG,IAAI,GAAG,GAAG,CAAC,EAAE,OAAO,GAAG,CAAC;IAExB,MAAM,GAAG,GAAG,OAAO,EAAE,MAAM,IAAI,MAAM,CAAC;IACtC,IAAI,KAAK,GAAG,CAAC,CAAC;IAEd,GAAG,CAAC;QACF,MAAM,KAAK,GAAG,GAAG,CAAC,OAAO,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;QACtC,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE,MAAM,CAAC,wBAAwB;QAEjD,MAAM,QAAQ,GAAG,GAAG,CAAC,OAAO,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;QACzC,MAAM,MAAM,GAAG,QAAQ,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,QAAQ,CAAC;QAEhD,IAAI,KAAK,GAAG,MAAM,EAAE,CAAC;YACnB,+BAA+B;YAC/B,KAAK,GAAG,GAAG,CAAC,WAAW,CAAC,GAAG,EAAE,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;YAC5C,SAAS;QACX,CAAC;QAED,MAAM,WAAW,GAAG,UAAU,CAAC,GAAG,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;QAClD,MAAM,SAAS,GAAG,QAAQ,CAAC,GAAG,EAAE,KAAK,EAAE,WAAW,CAAC,CAAC;QACpD,MAAM,GAAG,GAAG,GAAG,CAAC,KAAK,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;QAE9C,mBAAmB;QACnB,IAAI,GAAG,CAAC,GAAG,CAAC,KAAK,SAAS,EAAE,CAAC;YAC3B,IAAI,WAAW,GAAG,UAAU,CAAC,GAAG,EAAE,KAAK,GAAG,CAAC,EAAE,MAAM,CAAC,CAAC;YACrD,IAAI,SAAS,GAAG,QAAQ,CAAC,GAAG,EAAE,MAAM,EAAE,WAAW,CAAC,CAAC;YAEnD,MAAM,KAAK,GAAG,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC,CAAC;YACrD,GAAG,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;QACnB,CAAC;QAED,KAAK,GAAG,MAAM,GAAG,CAAC,CAAC;IACrB,CAAC,OAAQ,KAAK,GAAG,GAAG,CAAE;IAEtB,OAAO,GAAG,CAAC;AACb,CAAC;AAED,SAAS,UAAU,CAAC,GAAW,EAAE,KAAa,EAAE,GAAW;IACzD,GAAG,CAAC;QACF,MAAM,IAAI,GAAG,GAAG,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;QACnC,IAAI,IAAI,KAAK,IAAI,CAAC,KAAA,EAAO,KAAI,IAAI,KAAK,IAAI,CAAC,MAAA,EAAQ,GAAE,OAAO,KAAK,CAAC;IACpE,CAAC,OAAQ,EAAE,KAAK,GAAG,GAAG,CAAE;IACxB,OAAO,GAAG,CAAC;AACb,CAAC;AAED,SAAS,QAAQ,CAAC,GAAW,EAAE,KAAa,EAAE,GAAW;IACvD,MAAO,KAAK,GAAG,GAAG,CAAE,CAAC;QACnB,MAAM,IAAI,GAAG,GAAG,CAAC,UAAU,CAAC,EAAE,KAAK,CAAC,CAAC;QACrC,IAAI,IAAI,KAAK,IAAI,CAAC,KAAA,EAAO,KAAI,IAAI,KAAK,IAAI,CAAC,MAAA,EAAQ,GAAE,OAAO,KAAK,GAAG,CAAC,CAAC;IACxE,CAAC;IACD,OAAO,GAAG,CAAC;AACb,CAAC;AAmFD;;;;;;;;GAQG,CACH,SAAgB,SAAS,CACvB,IAAY,EACZ,GAAW,EACX,OAA0B;IAE1B,MAAM,GAAG,GAAG,OAAO,EAAE,MAAM,IAAI,kBAAkB,CAAC;IAElD,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;QACjC,MAAM,IAAI,SAAS,CAAC,CAAA,0BAAA,EAA6B,IAAI,EAAE,CAAC,CAAC;IAC3D,CAAC;IAED,MAAM,KAAK,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC;IAEvB,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;QACnC,MAAM,IAAI,SAAS,CAAC,CAAA,yBAAA,EAA4B,GAAG,EAAE,CAAC,CAAC;IACzD,CAAC;IAED,IAAI,GAAG,GAAG,IAAI,GAAG,GAAG,GAAG,KAAK,CAAC;IAC7B,IAAI,CAAC,OAAO,EAAE,OAAO,GAAG,CAAC;IAEzB,IAAI,OAAO,CAAC,MAAM,KAAK,SAAS,EAAE,CAAC;QACjC,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC;YACtC,MAAM,IAAI,SAAS,CAAC,CAAA,0BAAA,EAA6B,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC;QACrE,CAAC;QAED,GAAG,IAAI,YAAY,GAAG,OAAO,CAAC,MAAM,CAAC;IACvC,CAAC;IAED,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;QACnB,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC;YAC5C,MAAM,IAAI,SAAS,CAAC,CAAA,0BAAA,EAA6B,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC;QACrE,CAAC;QAED,GAAG,IAAI,WAAW,GAAG,OAAO,CAAC,MAAM,CAAC;IACtC,CAAC;IAED,IAAI,OAAO,CAAC,IAAI,EAAE,CAAC;QACjB,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC;YACxC,MAAM,IAAI,SAAS,CAAC,CAAA,wBAAA,EAA2B,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC;QACjE,CAAC;QAED,GAAG,IAAI,SAAS,GAAG,OAAO,CAAC,IAAI,CAAC;IAClC,CAAC;IAED,IAAI,OAAO,CAAC,OAAO,EAAE,CAAC;QACpB,IACE,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,IACxB,CAAC,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC,EAC3C,CAAC;YACD,MAAM,IAAI,SAAS,CAAC,CAAA,2BAAA,EAA8B,OAAO,CAAC,OAAO,EAAE,CAAC,CAAC;QACvE,CAAC;QAED,GAAG,IAAI,YAAY,GAAG,OAAO,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC;IACtD,CAAC;IAED,IAAI,OAAO,CAAC,QAAQ,EAAE,CAAC;QACrB,GAAG,IAAI,YAAY,CAAC;IACtB,CAAC;IAED,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;QACnB,GAAG,IAAI,UAAU,CAAC;IACpB,CAAC;IAED,IAAI,OAAO,CAAC,WAAW,EAAE,CAAC;QACxB,GAAG,IAAI,eAAe,CAAC;IACzB,CAAC;IAED,IAAI,OAAO,CAAC,QAAQ,EAAE,CAAC;QACrB,MAAM,QAAQ,GACZ,OAAO,OAAO,CAAC,QAAQ,KAAK,QAAQ,GAChC,OAAO,CAAC,QAAQ,CAAC,WAAW,EAAE,GAC9B,SAAS,CAAC;QAChB,OAAQ,QAAQ,EAAE,CAAC;YACjB,KAAK,KAAK;gBACR,GAAG,IAAI,gBAAgB,CAAC;gBACxB,MAAM;YACR,KAAK,QAAQ;gBACX,GAAG,IAAI,mBAAmB,CAAC;gBAC3B,MAAM;YACR,KAAK,MAAM;gBACT,GAAG,IAAI,iBAAiB,CAAC;gBACzB,MAAM;YACR;gBACE,MAAM,IAAI,SAAS,CAAC,CAAA,4BAAA,EAA+B,OAAO,CAAC,QAAQ,EAAE,CAAC,CAAC;QAC3E,CAAC;IACH,CAAC;IAED,IAAI,OAAO,CAAC,QAAQ,EAAE,CAAC;QACrB,MAAM,QAAQ,GACZ,OAAO,OAAO,CAAC,QAAQ,KAAK,QAAQ,GAChC,OAAO,CAAC,QAAQ,CAAC,WAAW,EAAE,GAC9B,OAAO,CAAC,QAAQ,CAAC;QACvB,OAAQ,QAAQ,EAAE,CAAC;YACjB,KAAK,IAAI,CAAC;YACV,KAAK,QAAQ;gBACX,GAAG,IAAI,mBAAmB,CAAC;gBAC3B,MAAM;YACR,KAAK,KAAK;gBACR,GAAG,IAAI,gBAAgB,CAAC;gBACxB,MAAM;YACR,KAAK,MAAM;gBACT,GAAG,IAAI,iBAAiB,CAAC;gBACzB,MAAM;YACR;gBACE,MAAM,IAAI,SAAS,CAAC,CAAA,4BAAA,EAA+B,OAAO,CAAC,QAAQ,EAAE,CAAC,CAAC;QAC3E,CAAC;IACH,CAAC;IAED,OAAO,GAAG,CAAC;AACb,CAAC;AAED;;GAEG,CACH,SAAS,MAAM,CAAC,GAAW;IACzB,IAAI,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE,OAAO,GAAG,CAAC;IAExC,IAAI,CAAC;QACH,OAAO,kBAAkB,CAAC,GAAG,CAAC,CAAC;IACjC,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC;QACX,OAAO,GAAG,CAAC;IACb,CAAC;AACH,CAAC;AAED;;GAEG,CACH,SAAS,MAAM,CAAC,GAAQ;IACtB,OAAO,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,eAAe,CAAC;AAClD,CAAC", "ignoreList": [0]}}, {"offset": {"line": 5986, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/@upstash/core-analytics/dist/index.js"], "sourcesContent": ["\"use strict\";var g=Object.defineProperty;var k=Object.getOwnPropertyDescriptor;var _=Object.getOwnPropertyNames;var y=Object.prototype.hasOwnProperty;var w=(l,e)=>{for(var t in e)g(l,t,{get:e[t],enumerable:!0})},A=(l,e,t,i)=>{if(e&&typeof e==\"object\"||typeof e==\"function\")for(let s of _(e))!y.call(l,s)&&s!==t&&g(l,s,{get:()=>e[s],enumerable:!(i=k(e,s))||i.enumerable});return l};var x=l=>A(g({},\"__esModule\",{value:!0}),l);var S={};w(S,{Analytics:()=>b});module.exports=x(S);var p=`\nlocal key = KEYS[1]\nlocal field = ARGV[1]\n\nlocal data = redis.call(\"ZRANGE\", key, 0, -1, \"WITHSCORES\")\nlocal count = {}\n\nfor i = 1, #data, 2 do\n  local json_str = data[i]\n  local score = tonumber(data[i + 1])\n  local obj = cjson.decode(json_str)\n\n  local fieldValue = obj[field]\n\n  if count[fieldValue] == nil then\n    count[fieldValue] = score\n  else\n    count[fieldValue] = count[fieldValue] + score\n  end\nend\n\nlocal result = {}\nfor k, v in pairs(count) do\n  table.insert(result, {k, v})\nend\n\nreturn result\n`,f=`\nlocal prefix = KEYS[1]\nlocal first_timestamp = tonumber(ARGV[1]) -- First timestamp to check\nlocal increment = tonumber(ARGV[2])       -- Increment between each timestamp\nlocal num_timestamps = tonumber(ARGV[3])  -- Number of timestampts to check (24 for a day and 24 * 7 for a week)\nlocal num_elements = tonumber(ARGV[4])    -- Number of elements to fetch in each category\nlocal check_at_most = tonumber(ARGV[5])   -- Number of elements to check at most.\n\nlocal keys = {}\nfor i = 1, num_timestamps do\n  local timestamp = first_timestamp - (i - 1) * increment\n  table.insert(keys, prefix .. \":\" .. timestamp)\nend\n\n-- get the union of the groups\nlocal zunion_params = {\"ZUNION\", num_timestamps, unpack(keys)}\ntable.insert(zunion_params, \"WITHSCORES\")\nlocal result = redis.call(unpack(zunion_params))\n\n-- select num_elements many items\nlocal true_group = {}\nlocal false_group = {}\nlocal denied_group = {}\nlocal true_count = 0\nlocal false_count = 0\nlocal denied_count = 0\nlocal i = #result - 1\n\n-- index to stop at after going through \"checkAtMost\" many items:\nlocal cutoff_index = #result - 2 * check_at_most\n\n-- iterate over the results\nwhile (true_count + false_count + denied_count) < (num_elements * 3) and 1 <= i and i >= cutoff_index do\n  local score = tonumber(result[i + 1])\n  if score > 0 then\n    local element = result[i]\n    if string.find(element, \"success\\\\\":true\") and true_count < num_elements then\n      table.insert(true_group, {score, element})\n      true_count = true_count + 1\n    elseif string.find(element, \"success\\\\\":false\") and false_count < num_elements then\n      table.insert(false_group, {score, element})\n      false_count = false_count + 1\n    elseif string.find(element, \"success\\\\\":\\\\\"denied\") and denied_count < num_elements then\n      table.insert(denied_group, {score, element})\n      denied_count = denied_count + 1\n    end\n  end\n  i = i - 2\nend\n\nreturn {true_group, false_group, denied_group}\n`,h=`\nlocal prefix = KEYS[1]\nlocal first_timestamp = tonumber(ARGV[1])\nlocal increment = tonumber(ARGV[2])\nlocal num_timestamps = tonumber(ARGV[3])\n\nlocal keys = {}\nfor i = 1, num_timestamps do\n  local timestamp = first_timestamp - (i - 1) * increment\n  table.insert(keys, prefix .. \":\" .. timestamp)\nend\n\n-- get the union of the groups\nlocal zunion_params = {\"ZUNION\", num_timestamps, unpack(keys)}\ntable.insert(zunion_params, \"WITHSCORES\")\nlocal result = redis.call(unpack(zunion_params))\n\nreturn result\n`;var b=class{redis;prefix;bucketSize;constructor(e){this.redis=e.redis,this.prefix=e.prefix??\"@upstash/analytics\",this.bucketSize=this.parseWindow(e.window)}validateTableName(e){if(!/^[a-zA-Z0-9_-]+$/.test(e))throw new Error(`Invalid table name: ${e}. Table names can only contain letters, numbers, dashes and underscores.`)}parseWindow(e){if(typeof e==\"number\"){if(e<=0)throw new Error(`Invalid window: ${e}`);return e}let t=/^(\\d+)([smhd])$/;if(!t.test(e))throw new Error(`Invalid window: ${e}`);let[,i,s]=e.match(t),n=parseInt(i);switch(s){case\"s\":return n*1e3;case\"m\":return n*1e3*60;case\"h\":return n*1e3*60*60;case\"d\":return n*1e3*60*60*24;default:throw new Error(`Invalid window unit: ${s}`)}}getBucket(e){let t=e??Date.now();return Math.floor(t/this.bucketSize)*this.bucketSize}async ingest(e,...t){this.validateTableName(e),await Promise.all(t.map(async i=>{let s=this.getBucket(i.time),n=[this.prefix,e,s].join(\":\");await this.redis.zincrby(n,1,JSON.stringify({...i,time:void 0}))}))}formatBucketAggregate(e,t,i){let s={};return e.forEach(([n,r])=>{t==\"success\"&&(n=n===1?\"true\":n===null?\"false\":n),s[t]=s[t]||{},s[t][(n??\"null\").toString()]=r}),{time:i,...s}}async aggregateBucket(e,t,i){this.validateTableName(e);let s=this.getBucket(i),n=[this.prefix,e,s].join(\":\"),r=await this.redis.eval(p,[n],[t]);return this.formatBucketAggregate(r,t,s)}async aggregateBuckets(e,t,i,s){this.validateTableName(e);let n=this.getBucket(s),r=[];for(let o=0;o<i;o+=1)r.push(this.aggregateBucket(e,t,n)),n=n-this.bucketSize;return Promise.all(r)}async aggregateBucketsWithPipeline(e,t,i,s,n){this.validateTableName(e),n=n??48;let r=this.getBucket(s),o=[],c=this.redis.pipeline(),u=[];for(let a=1;a<=i;a+=1){let d=[this.prefix,e,r].join(\":\");c.eval(p,[d],[t]),o.push(r),r=r-this.bucketSize,(a%n==0||a==i)&&(u.push(c.exec()),c=this.redis.pipeline())}return(await Promise.all(u)).flat().map((a,d)=>this.formatBucketAggregate(a,t,o[d]))}async getAllowedBlocked(e,t,i){this.validateTableName(e);let s=[this.prefix,e].join(\":\"),n=this.getBucket(i),r=await this.redis.eval(h,[s],[n,this.bucketSize,t]),o={};for(let c=0;c<r.length;c+=2){let u=r[c],m=u.identifier,a=+r[c+1];o[m]||(o[m]={success:0,blocked:0}),o[m][u.success?\"success\":\"blocked\"]=a}return o}async getMostAllowedBlocked(e,t,i,s,n){this.validateTableName(e);let r=[this.prefix,e].join(\":\"),o=this.getBucket(s),c=n??i*5,[u,m,a]=await this.redis.eval(f,[r],[o,this.bucketSize,t,i,c]);return{allowed:this.toDicts(u),ratelimited:this.toDicts(m),denied:this.toDicts(a)}}toDicts(e){let t=[];for(let i=0;i<e.length;i+=1){let s=+e[i][0],n=e[i][1];t.push({identifier:n.identifier,count:s})}return t}};0&&(module.exports={Analytics});\n"], "names": [], "mappings": "AAAA;AAAa,IAAI,IAAE,OAAO,cAAc;AAAC,IAAI,IAAE,OAAO,wBAAwB;AAAC,IAAI,IAAE,OAAO,mBAAmB;AAAC,IAAI,IAAE,OAAO,SAAS,CAAC,cAAc;AAAC,IAAI,IAAE,CAAC,GAAE;IAAK,IAAI,IAAI,KAAK,EAAE,EAAE,GAAE,GAAE;QAAC,KAAI,CAAC,CAAC,EAAE;QAAC,YAAW,CAAC;IAAC;AAAE,GAAE,IAAE,CAAC,GAAE,GAAE,GAAE;IAAK,IAAG,KAAG,OAAO,KAAG,YAAU,OAAO,KAAG,YAAW,KAAI,IAAI,KAAK,EAAE,GAAG,CAAC,EAAE,IAAI,CAAC,GAAE,MAAI,MAAI,KAAG,EAAE,GAAE,GAAE;QAAC,KAAI,IAAI,CAAC,CAAC,EAAE;QAAC,YAAW,CAAC,CAAC,IAAE,EAAE,GAAE,EAAE,KAAG,EAAE,UAAU;IAAA;IAAG,OAAO;AAAC;AAAE,IAAI,IAAE,CAAA,IAAG,EAAE,EAAE,CAAC,GAAE,cAAa;QAAC,OAAM,CAAC;IAAC,IAAG;AAAG,IAAI,IAAE,CAAC;AAAE,EAAE,GAAE;IAAC,WAAU,IAAI;AAAC;AAAG,OAAO,OAAO,GAAC,EAAE;AAAG,IAAI,IAAE,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2Bpe,CAAC,EAAC,IAAE,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmDL,CAAC,EAAC,IAAE,CAAC;;;;;;;;;;;;;;;;;;AAkBL,CAAC;AAAC,IAAI,IAAE;IAAM,MAAM;IAAA,OAAO;IAAA,WAAW;IAAA,YAAY,CAAC,CAAC;QAAC,IAAI,CAAC,KAAK,GAAC,EAAE,KAAK,EAAC,IAAI,CAAC,MAAM,GAAC,EAAE,MAAM,IAAE,sBAAqB,IAAI,CAAC,UAAU,GAAC,IAAI,CAAC,WAAW,CAAC,EAAE,MAAM;IAAC;IAAC,kBAAkB,CAAC,EAAC;QAAC,IAAG,CAAC,mBAAmB,IAAI,CAAC,IAAG,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,EAAE,wEAAwE,CAAC;IAAC;IAAC,YAAY,CAAC,EAAC;QAAC,IAAG,OAAO,KAAG,UAAS;YAAC,IAAG,KAAG,GAAE,MAAM,IAAI,MAAM,CAAC,gBAAgB,EAAE,GAAG;YAAE,OAAO;QAAC;QAAC,IAAI,IAAE;QAAkB,IAAG,CAAC,EAAE,IAAI,CAAC,IAAG,MAAM,IAAI,MAAM,CAAC,gBAAgB,EAAE,GAAG;QAAE,IAAG,GAAE,GAAE,EAAE,GAAC,EAAE,KAAK,CAAC,IAAG,IAAE,SAAS;QAAG,OAAO;YAAG,KAAI;gBAAI,OAAO,IAAE;YAAI,KAAI;gBAAI,OAAO,IAAE,MAAI;YAAG,KAAI;gBAAI,OAAO,IAAE,MAAI,KAAG;YAAG,KAAI;gBAAI,OAAO,IAAE,MAAI,KAAG,KAAG;YAAG;gBAAQ,MAAM,IAAI,MAAM,CAAC,qBAAqB,EAAE,GAAG;QAAC;IAAC;IAAC,UAAU,CAAC,EAAC;QAAC,IAAI,IAAE,KAAG,KAAK,GAAG;QAAG,OAAO,KAAK,KAAK,CAAC,IAAE,IAAI,CAAC,UAAU,IAAE,IAAI,CAAC,UAAU;IAAA;IAAC,MAAM,OAAO,CAAC,EAAC,GAAG,CAAC,EAAC;QAAC,IAAI,CAAC,iBAAiB,CAAC,IAAG,MAAM,QAAQ,GAAG,CAAC,EAAE,GAAG,CAAC,OAAM;YAAI,IAAI,IAAE,IAAI,CAAC,SAAS,CAAC,EAAE,IAAI,GAAE,IAAE;gBAAC,IAAI,CAAC,MAAM;gBAAC;gBAAE;aAAE,CAAC,IAAI,CAAC;YAAK,MAAM,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,GAAE,GAAE,KAAK,SAAS,CAAC;gBAAC,GAAG,CAAC;gBAAC,MAAK,KAAK;YAAC;QAAG;IAAG;IAAC,sBAAsB,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC;QAAC,IAAI,IAAE,CAAC;QAAE,OAAO,EAAE,OAAO,CAAC,CAAC,CAAC,GAAE,EAAE;YAAI,KAAG,aAAW,CAAC,IAAE,MAAI,IAAE,SAAO,MAAI,OAAK,UAAQ,CAAC,GAAE,CAAC,CAAC,EAAE,GAAC,CAAC,CAAC,EAAE,IAAE,CAAC,GAAE,CAAC,CAAC,EAAE,CAAC,CAAC,KAAG,MAAM,EAAE,QAAQ,GAAG,GAAC;QAAC,IAAG;YAAC,MAAK;YAAE,GAAG,CAAC;QAAA;IAAC;IAAC,MAAM,gBAAgB,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC;QAAC,IAAI,CAAC,iBAAiB,CAAC;QAAG,IAAI,IAAE,IAAI,CAAC,SAAS,CAAC,IAAG,IAAE;YAAC,IAAI,CAAC,MAAM;YAAC;YAAE;SAAE,CAAC,IAAI,CAAC,MAAK,IAAE,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAE;YAAC;SAAE,EAAC;YAAC;SAAE;QAAE,OAAO,IAAI,CAAC,qBAAqB,CAAC,GAAE,GAAE;IAAE;IAAC,MAAM,iBAAiB,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC;QAAC,IAAI,CAAC,iBAAiB,CAAC;QAAG,IAAI,IAAE,IAAI,CAAC,SAAS,CAAC,IAAG,IAAE,EAAE;QAAC,IAAI,IAAI,IAAE,GAAE,IAAE,GAAE,KAAG,EAAE,EAAE,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,GAAE,GAAE,KAAI,IAAE,IAAE,IAAI,CAAC,UAAU;QAAC,OAAO,QAAQ,GAAG,CAAC;IAAE;IAAC,MAAM,6BAA6B,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC;QAAC,IAAI,CAAC,iBAAiB,CAAC,IAAG,IAAE,KAAG;QAAG,IAAI,IAAE,IAAI,CAAC,SAAS,CAAC,IAAG,IAAE,EAAE,EAAC,IAAE,IAAI,CAAC,KAAK,CAAC,QAAQ,IAAG,IAAE,EAAE;QAAC,IAAI,IAAI,IAAE,GAAE,KAAG,GAAE,KAAG,EAAE;YAAC,IAAI,IAAE;gBAAC,IAAI,CAAC,MAAM;gBAAC;gBAAE;aAAE,CAAC,IAAI,CAAC;YAAK,EAAE,IAAI,CAAC,GAAE;gBAAC;aAAE,EAAC;gBAAC;aAAE,GAAE,EAAE,IAAI,CAAC,IAAG,IAAE,IAAE,IAAI,CAAC,UAAU,EAAC,CAAC,IAAE,KAAG,KAAG,KAAG,CAAC,KAAG,CAAC,EAAE,IAAI,CAAC,EAAE,IAAI,KAAI,IAAE,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE;QAAC;QAAC,OAAM,CAAC,MAAM,QAAQ,GAAG,CAAC,EAAE,EAAE,IAAI,GAAG,GAAG,CAAC,CAAC,GAAE,IAAI,IAAI,CAAC,qBAAqB,CAAC,GAAE,GAAE,CAAC,CAAC,EAAE;IAAE;IAAC,MAAM,kBAAkB,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC;QAAC,IAAI,CAAC,iBAAiB,CAAC;QAAG,IAAI,IAAE;YAAC,IAAI,CAAC,MAAM;YAAC;SAAE,CAAC,IAAI,CAAC,MAAK,IAAE,IAAI,CAAC,SAAS,CAAC,IAAG,IAAE,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAE;YAAC;SAAE,EAAC;YAAC;YAAE,IAAI,CAAC,UAAU;YAAC;SAAE,GAAE,IAAE,CAAC;QAAE,IAAI,IAAI,IAAE,GAAE,IAAE,EAAE,MAAM,EAAC,KAAG,EAAE;YAAC,IAAI,IAAE,CAAC,CAAC,EAAE,EAAC,IAAE,EAAE,UAAU,EAAC,IAAE,CAAC,CAAC,CAAC,IAAE,EAAE;YAAC,CAAC,CAAC,EAAE,IAAE,CAAC,CAAC,CAAC,EAAE,GAAC;gBAAC,SAAQ;gBAAE,SAAQ;YAAC,CAAC,GAAE,CAAC,CAAC,EAAE,CAAC,EAAE,OAAO,GAAC,YAAU,UAAU,GAAC;QAAC;QAAC,OAAO;IAAC;IAAC,MAAM,sBAAsB,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC;QAAC,IAAI,CAAC,iBAAiB,CAAC;QAAG,IAAI,IAAE;YAAC,IAAI,CAAC,MAAM;YAAC;SAAE,CAAC,IAAI,CAAC,MAAK,IAAE,IAAI,CAAC,SAAS,CAAC,IAAG,IAAE,KAAG,IAAE,GAAE,CAAC,GAAE,GAAE,EAAE,GAAC,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAE;YAAC;SAAE,EAAC;YAAC;YAAE,IAAI,CAAC,UAAU;YAAC;YAAE;YAAE;SAAE;QAAE,OAAM;YAAC,SAAQ,IAAI,CAAC,OAAO,CAAC;YAAG,aAAY,IAAI,CAAC,OAAO,CAAC;YAAG,QAAO,IAAI,CAAC,OAAO,CAAC;QAAE;IAAC;IAAC,QAAQ,CAAC,EAAC;QAAC,IAAI,IAAE,EAAE;QAAC,IAAI,IAAI,IAAE,GAAE,IAAE,EAAE,MAAM,EAAC,KAAG,EAAE;YAAC,IAAI,IAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAC,IAAE,CAAC,CAAC,EAAE,CAAC,EAAE;YAAC,EAAE,IAAI,CAAC;gBAAC,YAAW,EAAE,UAAU;gBAAC,OAAM;YAAC;QAAE;QAAC,OAAO;IAAC;AAAC;AAAE,KAAG,CAAC,OAAO,OAAO,GAAC;IAAC;AAAS,CAAC", "ignoreList": [0]}}, {"offset": {"line": 6262, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/@upstash/ratelimit/src/index.ts", "turbopack:///[project]/node_modules/@upstash/ratelimit/src/analytics.ts", "turbopack:///[project]/node_modules/@upstash/ratelimit/src/cache.ts", "turbopack:///[project]/node_modules/@upstash/ratelimit/src/duration.ts", "turbopack:///[project]/node_modules/@upstash/ratelimit/src/hash.ts", "turbopack:///[project]/node_modules/@upstash/ratelimit/src/lua-scripts/single.ts", "turbopack:///[project]/node_modules/@upstash/ratelimit/src/lua-scripts/multi.ts", "turbopack:///[project]/node_modules/@upstash/ratelimit/src/lua-scripts/reset.ts", "turbopack:///[project]/node_modules/@upstash/ratelimit/src/lua-scripts/hash.ts", "turbopack:///[project]/node_modules/@upstash/ratelimit/src/types.ts", "turbopack:///[project]/node_modules/@upstash/ratelimit/src/deny-list/scripts.ts", "turbopack:///[project]/node_modules/@upstash/ratelimit/src/deny-list/ip-deny-list.ts", "turbopack:///[project]/node_modules/@upstash/ratelimit/src/deny-list/time.ts", "turbopack:///[project]/node_modules/@upstash/ratelimit/src/deny-list/deny-list.ts", "turbopack:///[project]/node_modules/@upstash/ratelimit/src/ratelimit.ts", "turbopack:///[project]/node_modules/@upstash/ratelimit/src/multi.ts", "turbopack:///[project]/node_modules/@upstash/ratelimit/src/single.ts"], "sourcesContent": ["\n\n\n\n\n\n\n\n\n\n\n\nexport {Analytics, type AnalyticsConfig} from \"./analytics\";\nexport {MultiRegionRatelimit, type MultiRegionRatelimitConfig} from \"./multi\";\nexport {RegionRatelimit as Ratelimit, type RegionRatelimitConfig as RatelimitConfig} from \"./single\";\nexport {type Algorithm} from \"./types\";\nexport * as IpDenyList from \"./deny-list/ip-deny-list\";\nexport {type Duration} from \"./duration\";", "import type { Aggregate } from \"@upstash/core-analytics\";\nimport { Analytics as CoreAnalytics } from \"@upstash/core-analytics\";\nimport type { Redis } from \"./types\";\n\nexport type Geo = {\n  country?: string;\n  city?: string;\n  region?: string;\n  ip?: string;\n};\n\n/**\n * denotes the success field in the analytics submission.\n * Set to true when ratelimit check passes. False when request is ratelimited.\n * Set to \"denied\" when some request value is in deny list.\n */\nexport type EventSuccess = boolean | \"denied\"\n\nexport type Event = Geo & {\n  identifier: string;\n  time: number;\n  success: EventSuccess;\n};\n\nexport type AnalyticsConfig = {\n  redis: Redis;\n  prefix?: string;\n};\n\n/**\n * The Analytics package is experimental and can change at any time.\n */\nexport class Analytics {\n  private readonly analytics: CoreAnalytics;\n  private readonly table = \"events\";\n\n  constructor(config: AnalyticsConfig) {\n    this.analytics = new CoreAnalytics({\n      // @ts-expect-error we need to fix the types in core-analytics, it should only require the methods it needs, not the whole sdk\n      redis: config.redis,\n      window: \"1h\",\n      prefix: config.prefix ?? \"@upstash/ratelimit\",\n      retention: \"90d\",\n    });\n  }\n\n  /**\n   * Try to extract the geo information from the request\n   *\n   * This handles Vercel's `req.geo` and  and Cloudflare's `request.cf` properties\n   * @param req\n   * @returns\n   */\n  public extractGeo(req: { geo?: Geo; cf?: Geo }): Geo {\n    if (req.geo !== undefined) {\n      return req.geo;\n    }\n    if (req.cf !== undefined) {\n      return req.cf;\n    }\n\n    return {};\n  }\n\n  public async record(event: Event): Promise<void> {\n    await this.analytics.ingest(this.table, event);\n  }\n\n  public async series<TFilter extends keyof Omit<Event, \"time\">>(\n    filter: TFilter,\n    cutoff: number,\n  ): Promise<Aggregate[]> {\n    const timestampCount = Math.min(\n      (\n        this.analytics.getBucket(Date.now())\n        - this.analytics.getBucket(cutoff)\n      ) / (60 * 60 * 1000),\n      256\n    )\n    return this.analytics.aggregateBucketsWithPipeline(this.table, filter, timestampCount)\n  }\n\n  public async getUsage(cutoff = 0): Promise<Record<string, { success: number; blocked: number }>> {\n    \n    const timestampCount = Math.min(\n      (\n        this.analytics.getBucket(Date.now())\n        - this.analytics.getBucket(cutoff)\n      ) / (60 * 60 * 1000),\n      256\n    )\n    const records = await this.analytics.getAllowedBlocked(this.table, timestampCount)\n    return records;\n  }\n\n  public async getUsageOverTime<TFilter extends keyof Omit<Event, \"time\">>(\n    timestampCount: number, groupby: TFilter\n  ): Promise<Aggregate[]> {\n    const result = await this.analytics.aggregateBucketsWithPipeline(this.table, groupby, timestampCount)\n    return result\n  }\n\n  public async getMostAllowedBlocked(timestampCount: number, getTop?: number, checkAtMost?: number) {\n    getTop = getTop ?? 5\n    const timestamp = undefined // let the analytics handle getting the timestamp\n    return this.analytics.getMostAllowedBlocked(this.table, timestampCount, getTop, timestamp, checkAtMost)\n  }\n}\n", "import type { EphemeralC<PERSON> } from \"./types\";\n\nexport class <PERSON>ache implements EphemeralCache {\n  /**\n   * Stores identifier -> reset (in milliseconds)\n   */\n  private readonly cache: Map<string, number>;\n\n  constructor(cache: Map<string, number>) {\n    this.cache = cache;\n  }\n\n  public isBlocked(identifier: string): { blocked: boolean; reset: number } {\n    if (!this.cache.has(identifier)) {\n      return { blocked: false, reset: 0 };\n    }\n    const reset = this.cache.get(identifier)!;\n    if (reset < Date.now()) {\n      this.cache.delete(identifier);\n      return { blocked: false, reset: 0 };\n    }\n\n    return { blocked: true, reset: reset };\n  }\n\n  public blockUntil(identifier: string, reset: number): void {\n    this.cache.set(identifier, reset);\n  }\n\n  public set(key: string, value: number): void {\n    this.cache.set(key, value);\n  }\n  public get(key: string): number | null {\n    return this.cache.get(key) || null;\n  }\n\n  public incr(key: string): number {\n    let value = this.cache.get(key) ?? 0;\n    value += 1;\n    this.cache.set(key, value);\n    return value;\n  }\n\n  public pop(key: string): void {\n    this.cache.delete(key)\n  }\n\n  public empty(): void {\n    this.cache.clear()\n  }\n\n  public size(): number {\n    return this.cache.size;\n  }\n}\n", "type Unit = \"ms\" | \"s\" | \"m\" | \"h\" | \"d\";\nexport type Duration = `${number} ${Unit}` | `${number}${Unit}`;\n\n/**\n * Convert a human readable duration to milliseconds\n */\nexport function ms(d: Duration): number {\n  const match = d.match(/^(\\d+)\\s?(ms|s|m|h|d)$/);\n  if (!match) {\n    throw new Error(`Unable to parse window size: ${d}`);\n  }\n  const time = Number.parseInt(match[1]);\n  const unit = match[2] as Unit;\n\n  switch (unit) {\n    case \"ms\": {\n      return time;\n    }\n    case \"s\": {\n      return time * 1000;\n    }\n    case \"m\": {\n      return time * 1000 * 60;\n    }\n    case \"h\": {\n      return time * 1000 * 60 * 60;\n    }\n    case \"d\": {\n      return time * 1000 * 60 * 60 * 24;\n    }\n\n    default: {\n      throw new Error(`Unable to parse window size: ${d}`);\n    }\n  }\n}\n", "import type { ScriptInfo } from \"./lua-scripts/hash\";\nimport type { RegionContext } from \"./types\";\n\n/**\n * Runs the specified script with EVALSHA using the scriptHash parameter.\n * \n * If the EVALSHA fails, loads the script to redis and runs again with the\n * hash returned from Redis.\n * \n * @param ctx Regional or multi region context\n * @param script ScriptInfo of script to run. Contains the script and its hash\n * @param keys eval keys\n * @param args eval args\n */\nexport const safeEval = async (\n  ctx: RegionContext,\n  script: ScriptInfo,\n  keys: any[],\n  args: any[],\n) => {\n  try {\n    return await ctx.redis.evalsha(script.hash, keys, args)\n  } catch (error) {\n    if (`${error}`.includes(\"NOSCRIPT\")) {\n      const hash = await ctx.redis.scriptLoad(script.script)\n\n      if (hash !== script.hash) {\n        console.warn(\n          \"Upstash Ratelimit: Expected hash and the hash received from Redis\"\n          + \" are different. Ratelimit will work as usual but performance will\"\n          + \" be reduced.\"\n        );\n      }\n\n      return await ctx.redis.evalsha(hash, keys, args)\n    }\n    throw error;\n  }\n}", "export const fixedWindowLimitScript = `\n  local key           = KEYS[1]\n  local window        = ARGV[1]\n  local incrementBy   = ARGV[2] -- increment rate per request at a given value, default is 1\n\n  local r = redis.call(\"INCRBY\", key, incrementBy)\n  if r == tonumber(incrementBy) then\n  -- The first time this key is set, the value will be equal to incrementBy.\n  -- So we only need the expire command once\n  redis.call(\"PEXPIRE\", key, window)\n  end\n\n  return r\n`;\n\nexport const fixedWindowRemainingTokensScript = `\n      local key = KEYS[1]\n      local tokens = 0\n\n      local value = redis.call('GET', key)\n      if value then\n          tokens = value\n      end\n      return tokens\n    `;\n\nexport const slidingWindowLimitScript = `\n  local currentKey  = KEYS[1]           -- identifier including prefixes\n  local previousKey = KEYS[2]           -- key of the previous bucket\n  local tokens      = tonumber(ARGV[1]) -- tokens per window\n  local now         = ARGV[2]           -- current timestamp in milliseconds\n  local window      = ARGV[3]           -- interval in milliseconds\n  local incrementBy = ARGV[4]           -- increment rate per request at a given value, default is 1\n\n  local requestsInCurrentWindow = redis.call(\"GET\", currentKey)\n  if requestsInCurrentWindow == false then\n    requestsInCurrentWindow = 0\n  end\n\n  local requestsInPreviousWindow = redis.call(\"GET\", previousKey)\n  if requestsInPreviousWindow == false then\n    requestsInPreviousWindow = 0\n  end\n  local percentageInCurrent = ( now % window ) / window\n  -- weighted requests to consider from the previous window\n  requestsInPreviousWindow = math.floor(( 1 - percentageInCurrent ) * requestsInPreviousWindow)\n  if requestsInPreviousWindow + requestsInCurrentWindow >= tokens then\n    return -1\n  end\n\n  local newValue = redis.call(\"INCRBY\", currentKey, incrementBy)\n  if newValue == tonumber(incrementBy) then\n    -- The first time this key is set, the value will be equal to incrementBy.\n    -- So we only need the expire command once\n    redis.call(\"PEXPIRE\", currentKey, window * 2 + 1000) -- Enough time to overlap with a new window + 1 second\n  end\n  return tokens - ( newValue + requestsInPreviousWindow )\n`;\n\nexport const slidingWindowRemainingTokensScript = `\n  local currentKey  = KEYS[1]           -- identifier including prefixes\n  local previousKey = KEYS[2]           -- key of the previous bucket\n  local now         = ARGV[1]           -- current timestamp in milliseconds\n  local window      = ARGV[2]           -- interval in milliseconds\n\n  local requestsInCurrentWindow = redis.call(\"GET\", currentKey)\n  if requestsInCurrentWindow == false then\n    requestsInCurrentWindow = 0\n  end\n\n  local requestsInPreviousWindow = redis.call(\"GET\", previousKey)\n  if requestsInPreviousWindow == false then\n    requestsInPreviousWindow = 0\n  end\n\n  local percentageInCurrent = ( now % window ) / window\n  -- weighted requests to consider from the previous window\n  requestsInPreviousWindow = math.floor(( 1 - percentageInCurrent ) * requestsInPreviousWindow)\n\n  return requestsInPreviousWindow + requestsInCurrentWindow\n`;\n\nexport const tokenBucketLimitScript = `\n  local key         = KEYS[1]           -- identifier including prefixes\n  local maxTokens   = tonumber(ARGV[1]) -- maximum number of tokens\n  local interval    = tonumber(ARGV[2]) -- size of the window in milliseconds\n  local refillRate  = tonumber(ARGV[3]) -- how many tokens are refilled after each interval\n  local now         = tonumber(ARGV[4]) -- current timestamp in milliseconds\n  local incrementBy = tonumber(ARGV[5]) -- how many tokens to consume, default is 1\n        \n  local bucket = redis.call(\"HMGET\", key, \"refilledAt\", \"tokens\")\n        \n  local refilledAt\n  local tokens\n\n  if bucket[1] == false then\n    refilledAt = now\n    tokens = maxTokens\n  else\n    refilledAt = tonumber(bucket[1])\n    tokens = tonumber(bucket[2])\n  end\n        \n  if now >= refilledAt + interval then\n    local numRefills = math.floor((now - refilledAt) / interval)\n    tokens = math.min(maxTokens, tokens + numRefills * refillRate)\n\n    refilledAt = refilledAt + numRefills * interval\n  end\n\n  if tokens == 0 then\n    return {-1, refilledAt + interval}\n  end\n\n  local remaining = tokens - incrementBy\n  local expireAt = math.ceil(((maxTokens - remaining) / refillRate)) * interval\n        \n  redis.call(\"HSET\", key, \"refilledAt\", refilledAt, \"tokens\", remaining)\n  redis.call(\"PEXPIRE\", key, expireAt)\n  return {remaining, refilledAt + interval}\n`;\n\nexport const tokenBucketIdentifierNotFound = -1\n\nexport const tokenBucketRemainingTokensScript = `\n  local key         = KEYS[1]\n  local maxTokens   = tonumber(ARGV[1])\n        \n  local bucket = redis.call(\"HMGET\", key, \"refilledAt\", \"tokens\")\n\n  if bucket[1] == false then\n    return {maxTokens, ${tokenBucketIdentifierNotFound}}\n  end\n        \n  return {tonumber(bucket[2]), tonumber(bucket[1])}\n`;\n\nexport const cachedFixedWindowLimitScript = `\n  local key     = KEYS[1]\n  local window  = ARGV[1]\n  local incrementBy   = ARGV[2] -- increment rate per request at a given value, default is 1\n\n  local r = redis.call(\"INCRBY\", key, incrementBy)\n  if r == incrementBy then\n  -- The first time this key is set, the value will be equal to incrementBy.\n  -- So we only need the expire command once\n  redis.call(\"PEXPIRE\", key, window)\n  end\n      \n  return r\n`;\n\nexport const cachedFixedWindowRemainingTokenScript = `\n  local key = KEYS[1]\n  local tokens = 0\n\n  local value = redis.call('GET', key)\n  if value then\n      tokens = value\n  end\n  return tokens\n`;\n", "export const fixedWindowLimitScript = `\n\tlocal key           = KEYS[1]\n\tlocal id            = ARGV[1]\n\tlocal window        = ARGV[2]\n\tlocal incrementBy   = tonumber(ARGV[3])\n\n\tredis.call(\"HSET\", key, id, incrementBy)\n\tlocal fields = redis.call(\"HGETALL\", key)\n\tif #fields == 2 and tonumber(fields[2])==incrementBy then\n\t-- The first time this key is set, and the value will be equal to incrementBy.\n\t-- So we only need the expire command once\n\t  redis.call(\"PEXPIRE\", key, window)\n\tend\n\n\treturn fields\n`;\nexport const fixedWindowRemainingTokensScript = `\n      local key = KEYS[1]\n      local tokens = 0\n\n      local fields = redis.call(\"HGETALL\", key)\n\n      return fields\n    `;\n\nexport const slidingWindowLimitScript = `\n\tlocal currentKey    = KEYS[1]           -- identifier including prefixes\n\tlocal previousKey   = KEYS[2]           -- key of the previous bucket\n\tlocal tokens        = tonumber(ARGV[1]) -- tokens per window\n\tlocal now           = ARGV[2]           -- current timestamp in milliseconds\n\tlocal window        = ARGV[3]           -- interval in milliseconds\n\tlocal requestId     = ARGV[4]           -- uuid for this request\n\tlocal incrementBy   = tonumber(ARGV[5]) -- custom rate, default is  1\n\n\tlocal currentFields = redis.call(\"HGETALL\", currentKey)\n\tlocal requestsInCurrentWindow = 0\n\tfor i = 2, #currentFields, 2 do\n\trequestsInCurrentWindow = requestsInCurrentWindow + tonumber(currentFields[i])\n\tend\n\n\tlocal previousFields = redis.call(\"HGETALL\", previousKey)\n\tlocal requestsInPreviousWindow = 0\n\tfor i = 2, #previousFields, 2 do\n\trequestsInPreviousWindow = requestsInPreviousWindow + tonumber(previousFields[i])\n\tend\n\n\tlocal percentageInCurrent = ( now % window) / window\n\tif requestsInPreviousWindow * (1 - percentageInCurrent ) + requestsInCurrentWindow >= tokens then\n\t  return {currentFields, previousFields, false}\n\tend\n\n\tredis.call(\"HSET\", currentKey, requestId, incrementBy)\n\n\tif requestsInCurrentWindow == 0 then \n\t  -- The first time this key is set, the value will be equal to incrementBy.\n\t  -- So we only need the expire command once\n\t  redis.call(\"PEXPIRE\", currentKey, window * 2 + 1000) -- Enough time to overlap with a new window + 1 second\n\tend\n\treturn {currentFields, previousFields, true}\n`;\n\nexport const slidingWindowRemainingTokensScript = `\n\tlocal currentKey    = KEYS[1]           -- identifier including prefixes\n\tlocal previousKey   = KEYS[2]           -- key of the previous bucket\n\tlocal now         \t= ARGV[1]           -- current timestamp in milliseconds\n  \tlocal window      \t= ARGV[2]           -- interval in milliseconds\n\n\tlocal currentFields = redis.call(\"HGETALL\", currentKey)\n\tlocal requestsInCurrentWindow = 0\n\tfor i = 2, #currentFields, 2 do\n\trequestsInCurrentWindow = requestsInCurrentWindow + tonumber(currentFields[i])\n\tend\n\n\tlocal previousFields = redis.call(\"HGETALL\", previousKey)\n\tlocal requestsInPreviousWindow = 0\n\tfor i = 2, #previousFields, 2 do\n\trequestsInPreviousWindow = requestsInPreviousWindow + tonumber(previousFields[i])\n\tend\n\n\tlocal percentageInCurrent = ( now % window) / window\n  \trequestsInPreviousWindow = math.floor(( 1 - percentageInCurrent ) * requestsInPreviousWindow)\n\t\n\treturn requestsInCurrentWindow + requestsInPreviousWindow\n`;\n", "export const resetScript = `\n      local pattern = KEYS[1]\n\n      -- Initialize cursor to start from 0\n      local cursor = \"0\"\n\n      repeat\n          -- Scan for keys matching the pattern\n          local scan_result = redis.call('SCAN', cursor, 'MATCH', pattern)\n\n          -- Extract cursor for the next iteration\n          cursor = scan_result[1]\n\n          -- Extract keys from the scan result\n          local keys = scan_result[2]\n\n          for i=1, #keys do\n          redis.call('DEL', keys[i])\n          end\n\n      -- Continue scanning until cursor is 0 (end of keyspace)\n      until cursor == \"0\"\n    `;\n", "import * as Single from \"./single\"\nimport * as Multi from \"./multi\"\nimport { resetScript } from \"./reset\"\n\nexport type ScriptInfo = {\n  script: string,\n  hash: string\n}\n\ntype Algorithm = {\n  limit: ScriptInfo,\n  getRemaining: ScriptInfo,\n}\n\ntype AlgorithmKind = \n  | \"fixedWindow\"\n  | \"slidingWindow\"\n  | \"tokenBucket\"\n  | \"cachedFixedWindow\"\n\nexport const SCRIPTS: {\n  singleRegion: Record<AlgorithmKind, Algorithm>,\n  multiRegion: Record<Exclude<AlgorithmKind, \"tokenBucket\" | \"cachedFixedWindow\">, Algorithm>,\n} = {\n  singleRegion: {\n    fixedWindow: {\n      limit: {\n        script: Single.fixedWindowLimitScript,\n        hash: \"b13943e359636db027ad280f1def143f02158c13\"\n      },\n      getRemaining: {\n        script: Single.fixedWindowRemainingTokensScript,\n        hash: \"8c4c341934502aee132643ffbe58ead3450e5208\"\n      },\n    },\n    slidingWindow: {\n      limit: {\n        script: Single.slidingWindowLimitScript,\n        hash: \"e1391e429b699c780eb0480350cd5b7280fd9213\"\n      },\n      getRemaining: {\n        script: Single.slidingWindowRemainingTokensScript,\n        hash: \"65a73ac5a05bf9712903bc304b77268980c1c417\"\n      },\n    },\n    tokenBucket: {\n      limit: {\n        script: Single.tokenBucketLimitScript,\n        hash: \"5bece90aeef8189a8cfd28995b479529e270b3c6\"\n      },\n      getRemaining: {\n        script: Single.tokenBucketRemainingTokensScript,\n        hash: \"a15be2bb1db2a15f7c82db06146f9d08983900d0\"\n      },\n    },\n    cachedFixedWindow: {\n      limit: {\n        script: Single.cachedFixedWindowLimitScript,\n        hash: \"c26b12703dd137939b9a69a3a9b18e906a2d940f\"\n      },\n      getRemaining: {\n        script: Single.cachedFixedWindowRemainingTokenScript,\n        hash: \"8e8f222ccae68b595ee6e3f3bf2199629a62b91a\"\n      },\n    }\n  },\n  multiRegion: {\n    fixedWindow: {\n      limit: {\n        script: Multi.fixedWindowLimitScript,\n        hash: \"a8c14f3835aa87bd70e5e2116081b81664abcf5c\"\n      },\n      getRemaining: {\n        script: Multi.fixedWindowRemainingTokensScript,\n        hash: \"8ab8322d0ed5fe5ac8eb08f0c2e4557f1b4816fd\"\n      },\n    },\n    slidingWindow: {\n      limit: {\n        script: Multi.slidingWindowLimitScript,\n        hash: \"cb4fdc2575056df7c6d422764df0de3a08d6753b\"\n      },\n      getRemaining: {\n        script: Multi.slidingWindowRemainingTokensScript,\n        hash: \"558c9306b7ec54abb50747fe0b17e5d44bd24868\"\n      },\n    },\n  }\n}\n\n/** COMMON */\nexport const RESET_SCRIPT: ScriptInfo = {\n  script: resetScript,\n  hash: \"54bd274ddc59fb3be0f42deee2f64322a10e2b50\"\n}", "import type { Redis as RedisCore } from \"@upstash/redis\";\nimport type { <PERSON>eo } from \"./analytics\";\n\n/**\n * EphemeralCache is used to block certain identifiers right away in case they have already exceeded the ratelimit.\n */\nexport type EphemeralCache = {\n  isBlocked: (identifier: string) => { blocked: boolean; reset: number };\n  blockUntil: (identifier: string, reset: number) => void;\n\n  set: (key: string, value: number) => void;\n  get: (key: string) => number | null;\n\n  incr: (key: string) => number;\n\n  pop: (key: string) => void;\n  empty: () => void;\n\n  size: () => number;\n}\n\nexport type RegionContext = {\n  redis: Redis;\n  cache?: EphemeralCache,\n};\nexport type MultiRegionContext = { regionContexts: Omit<RegionContext[], \"cache\">; cache?: EphemeralCache };\n\nexport type RatelimitResponseType = \"timeout\" | \"cacheBlock\" | \"denyList\"\n\nexport type Context = RegionContext | MultiRegionContext;\nexport type RatelimitResponse = {\n  /**\n   * Whether the request may pass(true) or exceeded the limit(false)\n   */\n  success: boolean;\n  /**\n   * Maximum number of requests allowed within a window.\n   */\n  limit: number;\n  /**\n   * How many requests the user has left within the current window.\n   */\n  remaining: number;\n  /**\n   * Unix timestamp in milliseconds when the limits are reset.\n   */\n  reset: number;\n\n  /**\n   * For the MultiRegion setup we do some synchronizing in the background, after returning the current limit.\n   * Or when analytics is enabled, we send the analytics asynchronously after returning the limit.\n   * In most case you can simply ignore this.\n   *\n   * On Vercel Edge or Cloudflare workers, you need to explicitly handle the pending Promise like this:\n   *\n   * ```ts\n   * const { pending } = await ratelimit.limit(\"id\")\n   * context.waitUntil(pending)\n   * ```\n   *\n   * See `waitUntil` documentation in\n   * [Cloudflare](https://developers.cloudflare.com/workers/runtime-apis/handlers/fetch/#contextwaituntil)\n   * and [Vercel](https://vercel.com/docs/functions/edge-middleware/middleware-api#waituntil)\n   * for more details.\n   * ```\n   */\n  pending: Promise<unknown>;\n\n  /**\n   * Reason behind the result in `success` field.\n   * - Is set to \"timeout\" when request times out\n   * - Is set to \"cacheBlock\" when an identifier is blocked through cache without calling redis because it was\n   *    rate limited previously.\n   * - Is set to \"denyList\" when identifier or one of ip/user-agent/country parameters is in deny list. To enable\n   *    deny list, see `enableProtection` parameter. To edit the deny list, see the Upstash Ratelimit Dashboard\n   *    at https://console.upstash.com/ratelimit.\n   * - Is set to undefined if rate limit check had to use Redis. This happens in cases when `success` field in\n   *    the response is true. It can also happen the first time sucecss is false.\n   */\n  reason?: RatelimitResponseType;\n\n  /**\n   * The value which was in the deny list if reason: \"denyList\"\n   */\n  deniedValue?: DeniedValue\n};\n\nexport type Algorithm<TContext> = () => {\n  limit: (\n    ctx: TContext,\n    identifier: string,\n    rate?: number,\n    opts?: {\n      cache?: EphemeralCache;\n    },\n  ) => Promise<RatelimitResponse>;\n  getRemaining: (ctx: TContext, identifier: string) => Promise<{\n    remaining: number,\n    reset: number\n  }>;\n  resetTokens: (ctx: TContext, identifier: string) => Promise<void>;\n};\n\nexport type IsDenied = 0 | 1;\n\nexport type DeniedValue = string | undefined;\nexport type DenyListResponse = { deniedValue: DeniedValue, invalidIpDenyList: boolean }\n\nexport const DenyListExtension = \"denyList\" as const\nexport const IpDenyListKey = \"ipDenyList\" as const\nexport const IpDenyListStatusKey = \"ipDenyListStatus\" as const\n\nexport type LimitPayload = [RatelimitResponse, DenyListResponse];\nexport type LimitOptions = {\n  geo?: Geo,\n  rate?: number,\n  ip?: string,\n  userAgent?: string,\n  country?: string\n}\n\nexport type Redis = RedisCore\n", "export const checkDenyListScript = `\n  -- Checks if values provideed in ARGV are present in the deny lists.\n  -- This is done using the allDenyListsKey below.\n\n  -- Additionally, checks the status of the ip deny list using the\n  -- ipDenyListStatusKey below. Here are the possible states of the\n  -- ipDenyListStatusKey key:\n  -- * status == -1: set to \"disabled\" with no TTL\n  -- * status == -2: not set, meaning that is was set before but expired\n  -- * status  >  0: set to \"valid\", with a TTL\n  --\n  -- In the case of status == -2, we set the status to \"pending\" with\n  -- 30 second ttl. During this time, the process which got status == -2\n  -- will update the ip deny list.\n\n  local allDenyListsKey     = KEYS[1]\n  local ipDenyListStatusKey = KEYS[2]\n\n  local results = redis.call('SMISMEMBER', allDenyListsKey, unpack(ARGV))\n  local status  = redis.call('TTL', ipDenyListStatusKey)\n  if status == -2 then\n    redis.call('SETEX', ipDenyList<PERSON>tat<PERSON><PERSON><PERSON>, 30, \"pending\")\n  end\n\n  return { results, status }\n`", "import type { Redis } from \"../types\";\nimport { DenyListEx<PERSON>ion, IpDenyList<PERSON>ey, IpDenyListStatusKey } from \"../types\"\nimport { getIpListTTL } from \"./time\"\n\nconst baseUrl = \"https://raw.githubusercontent.com/stamparm/ipsum/master/levels\"\n\nexport class ThresholdError extends Error {\n  constructor(threshold: number) {\n    super(`Allowed threshold values are from 1 to 8, 1 and 8 included. Received: ${threshold}`);\n    this.name = \"ThresholdError\";\n  }\n}\n\n/**\n * Fetches the ips from the ipsum.txt at github\n * \n * In the repo we are using, 30+ ip lists are aggregated. The results are\n * stores in text files from 1 to 8.\n * https://github.com/stamparm/ipsum/tree/master/levels\n * \n * X.txt file holds ips which are in at least X of the lists.\n *\n * @param threshold ips with less than or equal to the threshold are not included\n * @returns list of ips\n */\nconst getIpDenyList = async (threshold: number) => {\n  if (typeof threshold !== \"number\" || threshold < 1 || threshold > 8) {\n    throw new ThresholdError(threshold)\n  }\n\n  try {\n    // Fetch data from the URL\n    const response = await fetch(`${baseUrl}/${threshold}.txt`)\n    if (!response.ok) {\n      throw new Error(`Error fetching data: ${response.statusText}`)\n    }\n    const data = await response.text()\n\n    // Process the data\n    const lines = data.split(\"\\n\")\n    return lines.filter((value) => value.length > 0) // remove empty values\n  } catch (error) {\n    throw new Error(`Failed to fetch ip deny list: ${error}`)\n  }\n}\n\n/**\n * Gets the list of ips from the github source which are not in the\n * deny list already\n * \n * First, gets the ip list from github using the threshold. Then, calls redis with\n * a transaction which does the following:\n * - subtract the current ip deny list from all\n * - delete current ip deny list\n * - recreate ip deny list with the ips from github. Ips already in the users own lists\n *   are excluded.\n * - status key is set to valid with ttl until next 2 AM UTC, which is a bit later than\n *   when the list is updated on github.\n *\n * @param redis redis instance\n * @param prefix ratelimit prefix\n * @param threshold ips with less than or equal to the threshold are not included\n * @param ttl time to live in milliseconds for the status flag. Optional. If not\n *  passed, ttl is infferred from current time.\n * @returns list of ips which are not in the deny list\n */\nexport const updateIpDenyList = async (\n  redis: Redis,\n  prefix: string,\n  threshold: number,\n  ttl?: number\n) => {\n  const allIps = await getIpDenyList(threshold)\n\n  const allDenyLists = [prefix, DenyListExtension, \"all\"].join(\":\")\n  const ipDenyList = [prefix, DenyListExtension, IpDenyListKey].join(\":\")\n  const statusKey = [prefix, IpDenyListStatusKey].join(\":\")\n\n  const transaction = redis.multi()\n\n  // remove the old ip deny list from the all set\n  transaction.sdiffstore(allDenyLists, allDenyLists, ipDenyList)\n\n  // delete the old ip deny list and create new one\n  transaction.del(ipDenyList)\n\n  transaction.sadd(ipDenyList, allIps.at(0), ...allIps.slice(1))\n\n  // make all deny list and ip deny list disjoint by removing duplicate\n  // ones from ip deny list\n  transaction.sdiffstore(ipDenyList, ipDenyList, allDenyLists)\n\n  // add remaining ips to all list\n  transaction.sunionstore(allDenyLists, allDenyLists, ipDenyList)\n\n  // set status key with ttl\n  transaction.set(statusKey, \"valid\", {px: ttl ?? getIpListTTL()})\n\n  return await transaction.exec()\n}\n\n/**\n * Disables the ip deny list by removing the ip deny list from the all\n * set and removing the ip deny list. Also sets the status key to disabled\n * with no ttl.\n * \n * @param redis redis instance\n * @param prefix ratelimit prefix\n * @returns \n */\nexport const disableIpDenyList = async (redis: Redis, prefix: string) => {\n  const allDenyListsKey = [prefix, DenyListExtension, \"all\"].join(\":\")\n  const ipDenyListKey = [prefix, DenyListExtension, IpDenyListKey].join(\":\")\n  const statusKey = [prefix, IpDenyListStatusKey].join(\":\")\n\n  const transaction = redis.multi()\n\n  // remove the old ip deny list from the all set\n  transaction.sdiffstore(allDenyListsKey, allDenyListsKey, ipDenyListKey)\n\n  // delete the old ip deny list\n  transaction.del(ipDenyListKey)\n\n  // set to disabled\n  // this way, the TTL command in checkDenyListScript will return -1.\n  transaction.set(statusKey, \"disabled\")\n\n  return await transaction.exec()\n}\n", "\n// Number of milliseconds in one hour\nconst MILLISECONDS_IN_HOUR = 60 * 60 * 1000;\n\n// Number of milliseconds in one day\nconst MILLISECONDS_IN_DAY = 24 * MILLISECONDS_IN_HOUR;\n\n// Number of milliseconds from the current time to 2 AM UTC\nconst MILLISECONDS_TO_2AM = 2 * MILLISECONDS_IN_HOUR;\n\nexport const getIpListTTL = (time?: number) => {\n  const now = time || Date.now();\n\n  // Time since the last 2 AM UTC\n  const timeSinceLast2AM = (now - MILLISECONDS_TO_2AM) % MILLISECONDS_IN_DAY;\n\n  // Remaining time until the next 2 AM UTC\n  return MILLISECONDS_IN_DAY - timeSinceLast2AM;\n}\n  ", "import type { DeniedValue, DenyListResponse, LimitPayload} from \"../types\";\nimport { DenyListExtension, IpDenyListStatusKey } from \"../types\"\nimport type { RatelimitResponse, Redis } from \"../types\"\nimport { Cache } from \"../cache\";\nimport { checkDenyListScript } from \"./scripts\";\nimport { updateIpDenyList } from \"./ip-deny-list\";\n\n\nconst denyListCache = new Cache(new Map());\n\n/**\n * Checks items in members list and returns the first denied member\n * in denyListCache if there are any.\n * \n * @param members list of values to check against the cache\n * @returns a member from the cache. If there is none, returns undefined\n */\nexport const checkDenyListCache = (members: string[]): DeniedValue => {\n  return members.find(\n    member => denyListCache.isBlocked(member).blocked\n  );\n}\n\n/**\n * Blocks a member for 1 minute.\n * \n * If there are more than 1000 elements in the cache, empties\n * it so that the cache doesn't grow in size indefinetely.\n * \n * @param member member to block\n */\nconst blockMember = (member: string) => {\n  if (denyListCache.size() > 1000) denyListCache.empty();\n  denyListCache.blockUntil(member, Date.now() + 60_000);\n}\n\n/**\n * Checks if identifier or any of the values are in any of\n * the denied lists in Redis.\n * \n * If some value is in a deny list, we block the identifier for a minute.\n * \n * @param redis redis client\n * @param prefix ratelimit prefix\n * @param members List of values (identifier, ip, user agent, country)\n * @returns true if a member is in deny list at Redis\n */\nexport const checkDenyList = async (\n  redis: Redis,\n  prefix: string,\n  members: string[]\n): Promise<DenyListResponse> => {\n  const [ deniedValues, ipDenyListStatus ] = await redis.eval(\n    checkDenyListScript,\n    [\n      [prefix, DenyListExtension, \"all\"].join(\":\"),\n      [prefix, IpDenyListStatusKey].join(\":\"),\n    ],\n    members\n  ) as [boolean[], number];\n\n  let deniedValue: DeniedValue = undefined;\n  deniedValues.map((memberDenied, index) => {\n    if (memberDenied) {\n      blockMember(members[index])\n      deniedValue = members[index]\n    }\n  })\n\n  return {\n    deniedValue,\n    invalidIpDenyList: ipDenyListStatus === -2\n  };\n};\n\n/**\n * Overrides the rate limit response if deny list\n * response indicates that value is in deny list.\n * \n * @param ratelimitResponse \n * @param denyListResponse \n * @returns \n */\nexport const resolveLimitPayload = (\n  redis: Redis,\n  prefix: string,\n  [ratelimitResponse, denyListResponse]: LimitPayload,\n  threshold: number\n): RatelimitResponse => {\n\n  if (denyListResponse.deniedValue) {\n    ratelimitResponse.success = false;\n    ratelimitResponse.remaining = 0;\n    ratelimitResponse.reason = \"denyList\";\n    ratelimitResponse.deniedValue = denyListResponse.deniedValue\n  }\n\n  if (denyListResponse.invalidIpDenyList) {\n    const updatePromise = updateIpDenyList(redis, prefix, threshold)\n    ratelimitResponse.pending = Promise.all([\n      ratelimitResponse.pending,\n      updatePromise\n    ])\n  }\n\n  return ratelimitResponse;\n};\n\n/**\n * \n * @returns Default response to return when some item\n *  is in deny list.\n */\nexport const defaultDeniedResponse = (deniedValue: string): RatelimitResponse => {\n  return {\n    success: false,\n    limit: 0,\n    remaining: 0,\n    reset: 0,\n    pending: Promise.resolve(),\n    reason: \"denyList\",\n    deniedValue: deniedValue\n  }\n}\n", "import { Analytics } from \"./analytics\";\nimport { C<PERSON> } from \"./cache\";\nimport type { Algorithm, Context, LimitOptions, LimitPayload, RatelimitResponse, Redis } from \"./types\";\nimport { checkDenyList, checkDenyListCache, defaultDeniedResponse, resolveLimitPayload } from \"./deny-list/index\";\n\nexport class TimeoutError extends Error {\n  constructor() {\n    super(\"Timeout\");\n    this.name = \"TimeoutError\";\n  }\n}\nexport type RatelimitConfig<TContext> = {\n  /**\n   * The ratelimiter function to use.\n   *\n   * Choose one of the predefined ones or implement your own.\n   * Available algorithms are exposed via static methods:\n   * - Ratelimiter.fixedWindow\n   * - Ratelimiter.slidingWindow\n   * - Ratelimiter.tokenBucket\n   */\n\n  limiter: Algorithm<TContext>;\n\n  ctx: TContext;\n  /**\n   * All keys in redis are prefixed with this.\n   *\n   * @default `@upstash/ratelimit`\n   */\n  prefix?: string;\n\n  /**\n   * If enabled, the ratelimiter will keep a global cache of identifiers, that have\n   * exhausted their ratelimit. In serverless environments this is only possible if\n   * you create the ratelimiter instance outside of your handler function. While the\n   * function is still hot, the ratelimiter can block requests without having to\n   * request data from redis, thus saving time and money.\n   *\n   * Whenever an identifier has exceeded its limit, the ratelimiter will add it to an\n   * internal list together with its reset timestamp. If the same identifier makes a\n   * new request before it is reset, we can immediately reject it.\n   *\n   * Set to `false` to disable.\n   *\n   * If left undefined, a map is created automatically, but it can only work\n   * if the map or the  ratelimit instance is created outside your serverless function handler.\n   */\n  ephemeralCache?: Map<string, number> | false;\n\n  /**\n   * If set, the ratelimiter will allow requests to pass after this many milliseconds.\n   *\n   * Use this if you want to allow requests in case of network problems\n   *\n   * @default 5000\n   */\n  timeout?: number;\n\n  /**\n   * If enabled, the ratelimiter will store analytics data in redis, which you can check out at\n   * https://console.upstash.com/ratelimit\n   *\n   * @default false\n   */\n  analytics?: boolean;\n\n  /**\n   * Enables deny list. If set to true, requests with identifier or ip/user-agent/countrie\n   * in the deny list will be rejected automatically. To edit the deny list, check out the\n   * ratelimit dashboard at https://console.upstash.com/ratelimit\n   * \n   * @default false\n   */\n  enableProtection?: boolean\n\n  denyListThreshold?: number\n};\n\n/**\n * Ratelimiter using serverless redis from https://upstash.com/\n *\n * @example\n * ```ts\n * const { limit } = new Ratelimit({\n *    redis: Redis.fromEnv(),\n *    limiter: Ratelimit.slidingWindow(\n *      10,     // Allow 10 requests per window of 30 minutes\n *      \"30 m\", // interval of 30 minutes\n *    ),\n * })\n *\n * ```\n */\nexport abstract class Ratelimit<TContext extends Context> {\n  protected readonly limiter: Algorithm<TContext>;\n\n  protected readonly ctx: TContext;\n\n  protected readonly prefix: string;\n\n  protected readonly timeout: number;\n\n  protected readonly primaryRedis: Redis;\n\n  protected readonly analytics?: Analytics;\n\n  protected readonly enableProtection: boolean;\n\n  protected readonly denyListThreshold: number\n\n  constructor(config: RatelimitConfig<TContext>) {\n    this.ctx = config.ctx;\n    this.limiter = config.limiter;\n    this.timeout = config.timeout ?? 5000;\n    this.prefix = config.prefix ?? \"@upstash/ratelimit\";\n\n    this.enableProtection = config.enableProtection ?? false;\n    this.denyListThreshold = config.denyListThreshold ?? 6;\n\n    this.primaryRedis = (\"redis\" in this.ctx) ? this.ctx.redis : this.ctx.regionContexts[0].redis\n    this.analytics = config.analytics\n      ? new Analytics({\n        redis: this.primaryRedis,\n        prefix: this.prefix,\n      })\n      : undefined;\n\n    if (config.ephemeralCache instanceof Map) {\n      this.ctx.cache = new Cache(config.ephemeralCache);\n    } else if (config.ephemeralCache === undefined) {\n      this.ctx.cache = new Cache(new Map());\n    }\n  }\n\n  /**\n   * Determine if a request should pass or be rejected based on the identifier and previously chosen ratelimit.\n   *\n   * Use this if you want to reject all requests that you can not handle right now.\n   *\n   * @example\n   * ```ts\n   *  const ratelimit = new Ratelimit({\n   *    redis: Redis.fromEnv(),\n   *    limiter: Ratelimit.slidingWindow(10, \"10 s\")\n   *  })\n   *\n   *  const { success } = await ratelimit.limit(id)\n   *  if (!success){\n   *    return \"Nope\"\n   *  }\n   *  return \"Yes\"\n   * ```\n   *\n   * @param req.rate - The rate at which tokens will be added or consumed from the token bucket. A higher rate allows for more requests to be processed. Defaults to 1 token per interval if not specified.\n   *\n   * Usage with `req.rate`\n   * @example\n   * ```ts\n   *  const ratelimit = new Ratelimit({\n   *    redis: Redis.fromEnv(),\n   *    limiter: Ratelimit.slidingWindow(100, \"10 s\")\n   *  })\n   *\n   *  const { success } = await ratelimit.limit(id, {rate: 10})\n   *  if (!success){\n   *    return \"Nope\"\n   *  }\n   *  return \"Yes\"\n   * ```\n   */\n  public limit = async (\n    identifier: string,\n    req?: LimitOptions,\n  ): Promise<RatelimitResponse> => {\n\n    let timeoutId: any = null;\n    try {\n      const response = this.getRatelimitResponse(identifier, req);\n      const { responseArray, newTimeoutId } = this.applyTimeout(response);\n      timeoutId = newTimeoutId;\n\n      const timedResponse = await Promise.race(responseArray);\n      const finalResponse = this.submitAnalytics(timedResponse, identifier, req);\n      return finalResponse;\n    } finally {\n      if (timeoutId) {\n        clearTimeout(timeoutId);\n      }\n    }\n  };\n\n  /**\n   * Block until the request may pass or timeout is reached.\n   *\n   * This method returns a promise that resolves as soon as the request may be processed\n   * or after the timeout has been reached.\n   *\n   * Use this if you want to delay the request until it is ready to get processed.\n   *\n   * @example\n   * ```ts\n   *  const ratelimit = new Ratelimit({\n   *    redis: Redis.fromEnv(),\n   *    limiter: Ratelimit.slidingWindow(10, \"10 s\")\n   *  })\n   *\n   *  const { success } = await ratelimit.blockUntilReady(id, 60_000)\n   *  if (!success){\n   *    return \"Nope\"\n   *  }\n   *  return \"Yes\"\n   * ```\n   */\n  public blockUntilReady = async (\n    /**\n     * An identifier per user or api.\n     * Choose a userID, or api token, or ip address.\n     *\n     * If you want to limit your api across all users, you can set a constant string.\n     */\n    identifier: string,\n    /**\n     * Maximum duration to wait in milliseconds.\n     * After this time the request will be denied.\n     */\n    timeout: number,\n  ): Promise<RatelimitResponse> => {\n    if (timeout <= 0) {\n      throw new Error(\"timeout must be positive\");\n    }\n    let res: RatelimitResponse;\n\n    const deadline = Date.now() + timeout;\n    while (true) {\n      res = await this.limit(identifier);\n      if (res.success) {\n        break;\n      }\n      if (res.reset === 0) {\n        throw new Error(\"This should not happen\");\n      }\n\n      const wait = Math.min(res.reset, deadline) - Date.now();\n      await new Promise((r) => setTimeout(r, wait));\n\n      if (Date.now() > deadline) {\n        break;\n      }\n    }\n    return res!;\n  };\n\n  public resetUsedTokens = async (identifier: string) => {\n    const pattern = [this.prefix, identifier].join(\":\");\n    await this.limiter().resetTokens(this.ctx, pattern);\n  };\n\n  /**\n   * Returns the remaining token count together with a reset timestamps\n   * \n   * @param identifier identifir to check\n   * @returns object with `remaining` and reset fields. `remaining` denotes\n   *          the remaining tokens and reset denotes the timestamp when the\n   *          tokens reset.\n   */\n  public getRemaining = async (identifier: string): Promise<{\n    remaining: number;\n    reset: number;\n  }> => {\n    const pattern = [this.prefix, identifier].join(\":\");\n\n    return await this.limiter().getRemaining(this.ctx, pattern);\n  };\n\n  /**\n   * Checks if the identifier or the values in req are in the deny list cache.\n   * If so, returns the default denied response.\n   * \n   * Otherwise, calls redis to check the rate limit and deny list. Returns after\n   * resolving the result. Resolving is overriding the rate limit result if\n   * the some value is in deny list.\n   * \n   * @param identifier identifier to block\n   * @param req options with ip, user agent, country, rate and geo info\n   * @returns rate limit response\n   */\n  private getRatelimitResponse = async (\n    identifier: string,\n    req?: LimitOptions\n  ): Promise<RatelimitResponse> => {\n    const key = this.getKey(identifier);\n    const definedMembers = this.getDefinedMembers(identifier, req);\n\n    const deniedValue = checkDenyListCache(definedMembers)\n\n    const result: LimitPayload = deniedValue ? [defaultDeniedResponse(deniedValue), { deniedValue, invalidIpDenyList: false }] : (await Promise.all([\n      this.limiter().limit(this.ctx, key, req?.rate),\n      this.enableProtection\n        ? checkDenyList(this.primaryRedis, this.prefix, definedMembers)\n        : { deniedValue: undefined, invalidIpDenyList: false }\n    ]));\n\n    return resolveLimitPayload(this.primaryRedis, this.prefix, result, this.denyListThreshold)\n  };\n\n  /**\n   * Creates an array with the original response promise and a timeout promise\n   * if this.timeout > 0.\n   * \n   * @param response Ratelimit response promise\n   * @returns array with the response and timeout promise. also includes the timeout id\n   */\n  private applyTimeout = (response: Promise<RatelimitResponse>) => {\n    let newTimeoutId: any = null;\n    const responseArray: Array<Promise<RatelimitResponse>> = [response];\n\n    if (this.timeout > 0) {\n      const timeoutResponse = new Promise<RatelimitResponse>((resolve) => {\n        newTimeoutId = setTimeout(() => {\n          resolve({\n            success: true,\n            limit: 0,\n            remaining: 0,\n            reset: 0,\n            pending: Promise.resolve(),\n            reason: \"timeout\"\n          });\n        }, this.timeout);\n      })\n      responseArray.push(timeoutResponse);\n    }\n\n    return {\n      responseArray,\n      newTimeoutId,\n    }\n  }\n\n  /**\n   * submits analytics if this.analytics is set\n   * \n   * @param ratelimitResponse final rate limit response\n   * @param identifier identifier to submit\n   * @param req limit options\n   * @returns rate limit response after updating the .pending field\n   */\n  private submitAnalytics = (\n    ratelimitResponse: RatelimitResponse,\n    identifier: string,\n    req?: Pick<LimitOptions, \"geo\">,\n  ) => {\n    if (this.analytics) {\n      try {\n        const geo = req ? this.analytics.extractGeo(req) : undefined;\n        const analyticsP = this.analytics\n          .record({\n            identifier: ratelimitResponse.reason === \"denyList\" // if in denyList, use denied value as identifier\n              ? ratelimitResponse.deniedValue!\n              : identifier,\n            time: Date.now(),\n            success: ratelimitResponse.reason === \"denyList\" // if in denyList, label success as \"denied\"\n              ? \"denied\"\n              : ratelimitResponse.success,\n            ...geo,\n          })\n          .catch((error) => {\n            let errorMessage = \"Failed to record analytics\"\n            if (`${error}`.includes(\"WRONGTYPE\")) {\n              errorMessage = `\n    Failed to record analytics. See the information below:\n\n    This can occur when you uprade to Ratelimit version 1.1.2\n    or later from an earlier version.\n\n    This occurs simply because the way we store analytics data\n    has changed. To avoid getting this error, disable analytics\n    for *an hour*, then simply enable it back.\\n\n    `\n            }\n            console.warn(errorMessage, error);\n          });\n        ratelimitResponse.pending = Promise.all([ratelimitResponse.pending, analyticsP]);\n      } catch (error) {\n        console.warn(\"Failed to record analytics\", error);\n      };\n    };\n    return ratelimitResponse;\n  }\n\n  private getKey = (identifier: string): string => {\n    return [this.prefix, identifier].join(\":\");\n  }\n\n  /**\n   * returns a list of defined values from\n   * [identifier, req.ip, req.userAgent, req.country]\n   * \n   * @param identifier identifier\n   * @param req limit options\n   * @returns list of defined values\n   */\n  private getDefinedMembers = (\n    identifier: string,\n    req?: Pick<LimitOptions, \"ip\" | \"userAgent\" | \"country\">\n  ): string[] => {\n    const members = [identifier, req?.ip, req?.userAgent, req?.country];\n    return (members as string[]).filter(Boolean);\n  }\n}\n", "import { Cache } from \"./cache\";\nimport type { Duration } from \"./duration\";\nimport { ms } from \"./duration\";\nimport { safeEval } from \"./hash\";\nimport { RESET_SCRIPT, SCRIPTS } from \"./lua-scripts/hash\";\n\n\nimport { Ratelimit } from \"./ratelimit\";\nimport type { Algorithm, MultiRegionContext } from \"./types\";\n\nimport type { Redis } from \"./types\";\n\nfunction randomId(): string {\n  let result = \"\";\n  const characters = \"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789\";\n  const charactersLength = characters.length;\n  for (let i = 0; i < 16; i++) {\n    result += characters.charAt(Math.floor(Math.random() * charactersLength));\n  }\n  return result;\n}\n\nexport type MultiRegionRatelimitConfig = {\n  /**\n   * Instances of `@upstash/redis`\n   * @see https://github.com/upstash/upstash-redis#quick-start\n   */\n  redis: Redis[];\n  /**\n   * The ratelimiter function to use.\n   *\n   * Choose one of the predefined ones or implement your own.\n   * Available algorithms are exposed via static methods:\n   * - MultiRegionRatelimit.fixedWindow\n   */\n  limiter: Algorithm<MultiRegionContext>;\n  /**\n   * All keys in redis are prefixed with this.\n   *\n   * @default `@upstash/ratelimit`\n   */\n  prefix?: string;\n\n  /**\n   * If enabled, the ratelimiter will keep a global cache of identifiers, that have\n   * exhausted their ratelimit. In serverless environments this is only possible if\n   * you create the ratelimiter instance outside of your handler function. While the\n   * function is still hot, the ratelimiter can block requests without having to\n   * request data from redis, thus saving time and money.\n   *\n   * Whenever an identifier has exceeded its limit, the ratelimiter will add it to an\n   * internal list together with its reset timestamp. If the same identifier makes a\n   * new request before it is reset, we can immediately reject it.\n   *\n   * Set to `false` to disable.\n   *\n   * If left undefined, a map is created automatically, but it can only work\n   * if the map or the ratelimit instance is created outside your serverless function handler.\n   */\n  ephemeralCache?: Map<string, number> | false;\n\n  /**\n   * If set, the ratelimiter will allow requests to pass after this many milliseconds.\n   *\n   * Use this if you want to allow requests in case of network problems\n   */\n  timeout?: number;\n\n  /**\n   * If enabled, the ratelimiter will store analytics data in redis, which you can check out at\n   * https://console.upstash.com/ratelimit\n   *\n   * @default false\n   */\n  analytics?: boolean;\n\n  /**\n   * If enabled, lua scripts will be sent to Redis with SCRIPT LOAD durint the first request.\n   * In the subsequent requests, hash of the script will be used to invoke it\n   * \n   * @default true\n   */\n  cacheScripts?: boolean;\n};\n\n/**\n * Ratelimiter using serverless redis from https://upstash.com/\n *\n * @example\n * ```ts\n * const { limit } = new MultiRegionRatelimit({\n *    redis: Redis.fromEnv(),\n *    limiter: MultiRegionRatelimit.fixedWindow(\n *      10,     // Allow 10 requests per window of 30 minutes\n *      \"30 m\", // interval of 30 minutes\n *    )\n * })\n *\n * ```\n */\nexport class MultiRegionRatelimit extends Ratelimit<MultiRegionContext> {\n  /**\n   * Create a new Ratelimit instance by providing a `@upstash/redis` instance and the algorithn of your choice.\n   */\n  constructor(config: MultiRegionRatelimitConfig) {\n    super({\n      prefix: config.prefix,\n      limiter: config.limiter,\n      timeout: config.timeout,\n      analytics: config.analytics,\n      ctx: {\n        regionContexts: config.redis.map(redis => ({\n          redis: redis,\n        })),\n        cache: config.ephemeralCache ? new Cache(config.ephemeralCache) : undefined,\n      },\n    });\n  }\n\n  /**\n   * Each request inside a fixed time increases a counter.\n   * Once the counter reaches the maximum allowed number, all further requests are\n   * rejected.\n   *\n   * **Pro:**\n   *\n   * - Newer requests are not starved by old ones.\n   * - Low storage cost.\n   *\n   * **Con:**\n   *\n   * A burst of requests near the boundary of a window can result in a very\n   * high request rate because two windows will be filled with requests quickly.\n   *\n   * @param tokens - How many requests a user can make in each time window.\n   * @param window - A fixed timeframe\n   */\n  static fixedWindow(\n    /**\n     * How many requests are allowed per window.\n     */\n    tokens: number,\n    /**\n     * The duration in which `tokens` requests are allowed.\n     */\n    window: Duration,\n  ): Algorithm<MultiRegionContext> {\n    const windowDuration = ms(window);\n\n    return () => ({\n      async limit(ctx: MultiRegionContext, identifier: string, rate?: number) {\n        if (ctx.cache) {\n          const { blocked, reset } = ctx.cache.isBlocked(identifier);\n          if (blocked) {\n            return {\n              success: false,\n              limit: tokens,\n              remaining: 0,\n              reset: reset,\n              pending: Promise.resolve(),\n              reason: \"cacheBlock\"\n            };\n          }\n        }\n\n        const requestId = randomId();\n        const bucket = Math.floor(Date.now() / windowDuration);\n        const key = [identifier, bucket].join(\":\");\n        const incrementBy = rate ? Math.max(1, rate) : 1;\n\n        const dbs: { redis: Redis; request: Promise<string[]> }[] = ctx.regionContexts.map((regionContext) => ({\n          redis: regionContext.redis,\n          request: safeEval(\n            regionContext,\n            SCRIPTS.multiRegion.fixedWindow.limit,\n            [key],\n            [requestId, windowDuration, incrementBy],\n          ) as Promise<string[]>,\n        }));\n\n        // The firstResponse is an array of string at every EVEN indexes and rate at which the tokens are used at every ODD indexes\n        const firstResponse = await Promise.any(dbs.map((s) => s.request));\n\n        const usedTokens = firstResponse.reduce((accTokens: number, usedToken, index) => {\n          let parsedToken = 0;\n          if (index % 2) {\n            parsedToken = Number.parseInt(usedToken);\n          }\n\n          return accTokens + parsedToken;\n        }, 0);\n\n        const remaining = tokens - usedTokens;\n\n        /**\n         * If the length between two databases does not match, we sync the two databases\n         */\n        async function sync() {\n          const individualIDs = await Promise.all(dbs.map((s) => s.request));\n\n          const allIDs = [...new Set(\n            individualIDs.flat()\n              .reduce((acc: string[], curr, index) => {\n                if (index % 2 === 0) {\n                  acc.push(curr);\n                }\n                return acc;\n              }, []),\n          ).values()];\n\n          for (const db of dbs) {\n            const usedDbTokensRequest = await db.request;\n            const usedDbTokens = usedDbTokensRequest.reduce(\n              (accTokens: number, usedToken, index) => {\n                let parsedToken = 0;\n                if (index % 2) {\n                  parsedToken = Number.parseInt(usedToken);\n                }\n\n                return accTokens + parsedToken;\n              },\n              0,\n            );\n\n            const dbIdsRequest = await db.request;\n            const dbIds = dbIdsRequest.reduce((ids: string[], currentId, index) => {\n              if (index % 2 === 0) {\n                ids.push(currentId);\n              }\n              return ids;\n            }, []);\n            /**\n             * If the bucket in this db is already full, it doesn't matter which ids it contains.\n             * So we do not have to sync.\n             */\n            if (usedDbTokens >= tokens) {\n              continue;\n            }\n            const diff = allIDs.filter((id) => !dbIds.includes(id));\n            /**\n             * Don't waste a request if there is nothing to send\n             */\n            if (diff.length === 0) {\n              continue;\n            }\n\n            for (const requestId of diff) {\n              await db.redis.hset(key, { [requestId]: incrementBy });\n            }\n          }\n        }\n\n        /**\n         * Do not await sync. This should not run in the critical path.\n         */\n\n        const success = remaining > 0;\n        const reset = (bucket + 1) * windowDuration;\n\n        if (ctx.cache && !success) {\n          ctx.cache.blockUntil(identifier, reset);\n        }\n        return {\n          success,\n          limit: tokens,\n          remaining,\n          reset,\n          pending: sync(),\n        };\n      },\n      async getRemaining(ctx: MultiRegionContext, identifier: string) {\n        const bucket = Math.floor(Date.now() / windowDuration);\n        const key = [identifier, bucket].join(\":\");\n\n        const dbs: { redis: Redis; request: Promise<string[]> }[] = ctx.regionContexts.map((regionContext) => ({\n          redis: regionContext.redis,\n          request: safeEval(\n            regionContext,\n            SCRIPTS.multiRegion.fixedWindow.getRemaining,\n            [key],\n            [null]\n          ) as Promise<string[]>,\n        }));\n\n        // The firstResponse is an array of string at every EVEN indexes and rate at which the tokens are used at every ODD indexes\n        const firstResponse = await Promise.any(dbs.map((s) => s.request));\n        const usedTokens = firstResponse.reduce((accTokens: number, usedToken, index) => {\n          let parsedToken = 0;\n          if (index % 2) {\n            parsedToken = Number.parseInt(usedToken);\n          }\n\n          return accTokens + parsedToken;\n        }, 0);\n\n        return {\n          remaining: Math.max(0, tokens - usedTokens),\n          reset: (bucket + 1) * windowDuration\n        };\n      },\n      async resetTokens(ctx: MultiRegionContext, identifier: string) {\n        const pattern = [identifier, \"*\"].join(\":\");\n        if (ctx.cache) {\n          ctx.cache.pop(identifier)\n        }\n\n        await Promise.all(ctx.regionContexts.map((regionContext) => {\n          safeEval(\n            regionContext,\n            RESET_SCRIPT,\n            [pattern],\n            [null]\n          );\n        }))\n      },\n    });\n  }\n\n  /**\n   * Combined approach of `slidingLogs` and `fixedWindow` with lower storage\n   * costs than `slidingLogs` and improved boundary behavior by calculating a\n   * weighted score between two windows.\n   *\n   * **Pro:**\n   *\n   * Good performance allows this to scale to very high loads.\n   *\n   * **Con:**\n   *\n   * Nothing major.\n   *\n   * @param tokens - How many requests a user can make in each time window.\n   * @param window - The duration in which the user can max X requests.\n   */\n  static slidingWindow(\n    /**\n     * How many requests are allowed per window.\n     */\n    tokens: number,\n    /**\n     * The duration in which `tokens` requests are allowed.\n     */\n    window: Duration,\n  ): Algorithm<MultiRegionContext> {\n    const windowSize = ms(window);\n\n    const windowDuration = ms(window);\n\n    return () => ({\n      async limit(ctx: MultiRegionContext, identifier: string, rate?: number) {\n        if (ctx.cache) {\n          const { blocked, reset } = ctx.cache.isBlocked(identifier);\n          if (blocked) {\n            return {\n              success: false,\n              limit: tokens,\n              remaining: 0,\n              reset: reset,\n              pending: Promise.resolve(),\n              reason: \"cacheBlock\"\n            };\n          }\n        }\n\n        const requestId = randomId();\n        const now = Date.now();\n\n        const currentWindow = Math.floor(now / windowSize);\n        const currentKey = [identifier, currentWindow].join(\":\");\n        const previousWindow = currentWindow - 1;\n        const previousKey = [identifier, previousWindow].join(\":\");\n        const incrementBy = rate ? Math.max(1, rate) : 1;\n\n        const dbs = ctx.regionContexts.map((regionContext) => ({\n          redis: regionContext.redis,\n          request: safeEval(\n            regionContext,\n            SCRIPTS.multiRegion.slidingWindow.limit,\n            [currentKey, previousKey],\n            [tokens, now, windowDuration, requestId, incrementBy],\n            // lua seems to return `1` for true and `null` for false\n          ) as Promise<[string[], string[], 1 | null]>,\n        }));\n\n        const percentageInCurrent = (now % windowDuration) / windowDuration;\n        const [current, previous, success] = await Promise.any(dbs.map((s) => s.request));\n\n        // in the case of success, the new request is not included in the current array.\n        // add it manually\n        if (success) {\n          current.push(requestId, incrementBy.toString())\n        }\n\n        const previousUsedTokens = previous.reduce((accTokens: number, usedToken, index) => {\n          let parsedToken = 0;\n          if (index % 2) {\n            parsedToken = Number.parseInt(usedToken);\n          }\n\n          return accTokens + parsedToken;\n        }, 0);\n\n        const currentUsedTokens = current.reduce((accTokens: number, usedToken, index) => {\n          let parsedToken = 0;\n          if (index % 2) {\n            parsedToken = Number.parseInt(usedToken);\n          }\n\n          return accTokens + parsedToken;\n        }, 0);\n\n        const previousPartialUsed = Math.ceil(previousUsedTokens * (1 - percentageInCurrent));\n\n        const usedTokens = previousPartialUsed + currentUsedTokens;\n\n        const remaining = tokens - usedTokens;\n\n        /**\n         * If a database differs from the consensus, we sync it\n         */\n        async function sync() {\n          const res = await Promise.all(dbs.map((s) => s.request));\n\n          const allCurrentIds = [...new Set(\n            res\n              .flatMap(([current]) => current)\n              .reduce((acc: string[], curr, index) => {\n                if (index % 2 === 0) {\n                  acc.push(curr);\n                }\n                return acc;\n              }, []),\n          ).values()];\n\n          for (const db of dbs) {\n            const [current, _previous, _success] = await db.request;\n            const dbIds = current.reduce((ids: string[], currentId, index) => {\n              if (index % 2 === 0) {\n                ids.push(currentId);\n              }\n              return ids;\n            }, []);\n\n            const usedDbTokens = current.reduce((accTokens: number, usedToken, index) => {\n              let parsedToken = 0;\n              if (index % 2) {\n                parsedToken = Number.parseInt(usedToken);\n              }\n\n              return accTokens + parsedToken;\n            }, 0);\n            /**\n             * If the bucket in this db is already full, it doesn't matter which ids it contains.\n             * So we do not have to sync.\n             */\n            if (usedDbTokens >= tokens) {\n              continue;\n            }\n            const diff = allCurrentIds.filter((id) => !dbIds.includes(id));\n            /**\n             * Don't waste a request if there is nothing to send\n             */\n            if (diff.length === 0) {\n              continue;\n            }\n\n            for (const requestId of diff) {\n              await db.redis.hset(currentKey, { [requestId]: incrementBy });\n            }\n          }\n        }\n\n        // const success = remaining >= 0;\n        const reset = (currentWindow + 1) * windowDuration;\n        if (ctx.cache && !success) {\n          ctx.cache.blockUntil(identifier, reset);\n        }\n        return {\n          success: Boolean(success),\n          limit: tokens,\n          remaining: Math.max(0, remaining),\n          reset,\n          pending: sync(),\n        };\n      },\n      async getRemaining(ctx: MultiRegionContext, identifier: string) {\n        const now = Date.now();\n\n        const currentWindow = Math.floor(now / windowSize);\n        const currentKey = [identifier, currentWindow].join(\":\");\n        const previousWindow = currentWindow - 1;\n        const previousKey = [identifier, previousWindow].join(\":\");\n\n        const dbs = ctx.regionContexts.map((regionContext) => ({\n          redis: regionContext.redis,\n          request: safeEval(\n            regionContext,\n            SCRIPTS.multiRegion.slidingWindow.getRemaining,\n            [currentKey, previousKey],\n            [now, windowSize],\n            // lua seems to return `1` for true and `null` for false\n          ) as Promise<number>,\n        }));\n\n        const usedTokens = await Promise.any(dbs.map((s) => s.request));\n        return {\n          remaining: Math.max(0, tokens - usedTokens),\n          reset: (currentWindow + 1) * windowSize\n        };\n      },\n      async resetTokens(ctx: MultiRegionContext, identifier: string) {\n        const pattern = [identifier, \"*\"].join(\":\");\n        if (ctx.cache) {\n          ctx.cache.pop(identifier)\n        }\n\n\n        await Promise.all(ctx.regionContexts.map((regionContext) => {\n          safeEval(\n            regionContext,\n            RESET_SCRIPT,\n            [pattern],\n            [null]\n          );\n        }))\n      },\n    });\n  }\n}\n", "import type { Duration } from \"./duration\";\nimport { ms } from \"./duration\";\nimport { safeEval } from \"./hash\";\nimport { RESET_SCRIPT, SCRIPTS } from \"./lua-scripts/hash\";\nimport { tokenBucketIdentifierNotFound } from \"./lua-scripts/single\";\n\nimport { Ratelimit } from \"./ratelimit\";\nimport type { Algorithm, RegionContext } from \"./types\";\nimport type { Redis as RedisCore } from \"./types\";\n\n// Fix for https://github.com/upstash/ratelimit-js/issues/125\ntype Redis = Pick<RedisCore, \"get\" | \"set\">\n\nexport type RegionRatelimitConfig = {\n  /**\n   * Instance of `@upstash/redis`\n   * @see https://github.com/upstash/upstash-redis#quick-start\n   */\n  redis: Redis;\n  /**\n   * The ratelimiter function to use.\n   *\n   * Choose one of the predefined ones or implement your own.\n   * Available algorithms are exposed via static methods:\n   * - Ratelimiter.fixedWindow\n   * - Ratelimiter.slidingWindow\n   * - Ratelimiter.tokenBucket\n   */\n  limiter: Algorithm<RegionContext>;\n  /**\n   * All keys in redis are prefixed with this.\n   *\n   * @default `@upstash/ratelimit`\n   */\n  prefix?: string;\n\n  /**\n   * If enabled, the ratelimiter will keep a global cache of identifiers, that have\n   * exhausted their ratelimit. In serverless environments this is only possible if\n   * you create the ratelimiter instance outside of your handler function. While the\n   * function is still hot, the ratelimiter can block requests without having to\n   * request data from redis, thus saving time and money.\n   *\n   * Whenever an identifier has exceeded its limit, the ratelimiter will add it to an\n   * internal list together with its reset timestamp. If the same identifier makes a\n   * new request before it is reset, we can immediately reject it.\n   *\n   * Set to `false` to disable.\n   *\n   * If left undefined, a map is created automatically, but it can only work\n   * if the map or the ratelimit instance is created outside your serverless function handler.\n   */\n  ephemeralCache?: Map<string, number> | false;\n\n  /**\n   * If set, the ratelimiter will allow requests to pass after this many milliseconds.\n   *\n   * Use this if you want to allow requests in case of network problems\n   */\n  timeout?: number;\n\n  /**\n   * If enabled, the ratelimiter will store analytics data in redis, which you can check out at\n   * https://console.upstash.com/ratelimit\n   *\n   * @default false\n   */\n  analytics?: boolean;\n\n  /**\n   * @deprecated Has no affect since v2.0.3. Instead, hash values of scripts are\n   * hardcoded in the sdk and it attempts to run the script using EVALSHA (with the hash).\n   * If it fails, runs script load.\n   * \n   * Previously, if enabled, lua scripts were sent to Redis with SCRIPT LOAD durint the first request.\n   * In the subsequent requests, hash of the script would be used to invoke the scripts\n   * \n   * @default true\n   */\n  cacheScripts?: boolean;\n\n  /**\n   * @default false\n   */\n  enableProtection?: boolean\n\n  /**\n   * @default 6\n   */\n  denyListThreshold?: number\n};\n\n/**\n * Ratelimiter using serverless redis from https://upstash.com/\n *\n * @example\n * ```ts\n * const { limit } = new Ratelimit({\n *    redis: Redis.fromEnv(),\n *    limiter: Ratelimit.slidingWindow(\n *      \"30 m\", // interval of 30 minutes\n *      10,     // Allow 10 requests per window of 30 minutes\n *    )\n * })\n *\n * ```\n */\nexport class RegionRatelimit extends Ratelimit<RegionContext> {\n  /**\n   * Create a new Ratelimit instance by providing a `@upstash/redis` instance and the algorithm of your choice.\n   */\n\n  constructor(config: RegionRatelimitConfig) {\n    super({\n      prefix: config.prefix,\n      limiter: config.limiter,\n      timeout: config.timeout,\n      analytics: config.analytics,\n      ctx: {\n        redis: config.redis as RedisCore,\n      },\n      ephemeralCache: config.ephemeralCache,\n      enableProtection: config.enableProtection,\n      denyListThreshold: config.denyListThreshold\n    });\n  }\n\n  /**\n   * Each request inside a fixed time increases a counter.\n   * Once the counter reaches the maximum allowed number, all further requests are\n   * rejected.\n   *\n   * **Pro:**\n   *\n   * - Newer requests are not starved by old ones.\n   * - Low storage cost.\n   *\n   * **Con:**\n   *\n   * A burst of requests near the boundary of a window can result in a very\n   * high request rate because two windows will be filled with requests quickly.\n   *\n   * @param tokens - How many requests a user can make in each time window.\n   * @param window - A fixed timeframe\n   */\n  static fixedWindow(\n    /**\n     * How many requests are allowed per window.\n     */\n    tokens: number,\n    /**\n     * The duration in which `tokens` requests are allowed.\n     */\n    window: Duration,\n  ): Algorithm<RegionContext> {\n    const windowDuration = ms(window);\n    return () => ({\n      async limit(ctx: RegionContext, identifier: string, rate?: number) {\n        const bucket = Math.floor(Date.now() / windowDuration);\n        const key = [identifier, bucket].join(\":\");\n        if (ctx.cache) {\n          const { blocked, reset } = ctx.cache.isBlocked(identifier);\n          if (blocked) {\n            return {\n              success: false,\n              limit: tokens,\n              remaining: 0,\n              reset: reset,\n              pending: Promise.resolve(),\n              reason: \"cacheBlock\"\n            };\n          }\n        }\n\n        const incrementBy = rate ? Math.max(1, rate) : 1;\n\n        const usedTokensAfterUpdate = await safeEval(\n          ctx,\n          SCRIPTS.singleRegion.fixedWindow.limit,\n          [key],\n          [windowDuration, incrementBy],\n        ) as number;\n\n        const success = usedTokensAfterUpdate <= tokens;\n\n        const remainingTokens = Math.max(0, tokens - usedTokensAfterUpdate);\n\n        const reset = (bucket + 1) * windowDuration;\n        if (ctx.cache && !success) {\n          ctx.cache.blockUntil(identifier, reset);\n        }\n\n        return {\n          success,\n          limit: tokens,\n          remaining: remainingTokens,\n          reset,\n          pending: Promise.resolve(),\n        };\n      },\n      async getRemaining(ctx: RegionContext, identifier: string) {\n        const bucket = Math.floor(Date.now() / windowDuration);\n        const key = [identifier, bucket].join(\":\");\n\n        const usedTokens = await safeEval(\n          ctx,\n          SCRIPTS.singleRegion.fixedWindow.getRemaining,\n          [key],\n          [null],\n        ) as number;\n\n        return {\n          remaining: Math.max(0, tokens - usedTokens),\n          reset: (bucket + 1) * windowDuration\n        };\n      },\n      async resetTokens(ctx: RegionContext, identifier: string) {\n        const pattern = [identifier, \"*\"].join(\":\");\n        if (ctx.cache) {\n          ctx.cache.pop(identifier)\n        }\n\n        await safeEval(\n          ctx,\n          RESET_SCRIPT,\n          [pattern],\n          [null],\n        ) as number;\n      },\n    });\n  }\n\n  /**\n   * Combined approach of `slidingLogs` and `fixedWindow` with lower storage\n   * costs than `slidingLogs` and improved boundary behavior by calculating a\n   * weighted score between two windows.\n   *\n   * **Pro:**\n   *\n   * Good performance allows this to scale to very high loads.\n   *\n   * **Con:**\n   *\n   * Nothing major.\n   *\n   * @param tokens - How many requests a user can make in each time window.\n   * @param window - The duration in which the user can max X requests.\n   */\n  static slidingWindow(\n    /**\n     * How many requests are allowed per window.\n     */\n    tokens: number,\n    /**\n     * The duration in which `tokens` requests are allowed.\n     */\n    window: Duration,\n  ): Algorithm<RegionContext> {\n    const windowSize = ms(window);\n    return () => ({\n      async limit(ctx: RegionContext, identifier: string, rate?: number) {\n        const now = Date.now();\n\n        const currentWindow = Math.floor(now / windowSize);\n        const currentKey = [identifier, currentWindow].join(\":\");\n        const previousWindow = currentWindow - 1;\n        const previousKey = [identifier, previousWindow].join(\":\");\n\n        if (ctx.cache) {\n          const { blocked, reset } = ctx.cache.isBlocked(identifier);\n          if (blocked) {\n            return {\n              success: false,\n              limit: tokens,\n              remaining: 0,\n              reset: reset,\n              pending: Promise.resolve(),\n              reason: \"cacheBlock\"\n            };\n          }\n        }\n\n        const incrementBy = rate ? Math.max(1, rate) : 1;\n\n        const remainingTokens = await safeEval(\n          ctx,\n          SCRIPTS.singleRegion.slidingWindow.limit,\n          [currentKey, previousKey],\n          [tokens, now, windowSize, incrementBy],\n        ) as number;\n\n        const success = remainingTokens >= 0;\n\n        const reset = (currentWindow + 1) * windowSize;\n        if (ctx.cache && !success) {\n          ctx.cache.blockUntil(identifier, reset);\n        }\n        return {\n          success,\n          limit: tokens,\n          remaining: Math.max(0, remainingTokens),\n          reset,\n          pending: Promise.resolve(),\n        };\n      },\n      async getRemaining(ctx: RegionContext, identifier: string) {\n        const now = Date.now();\n        const currentWindow = Math.floor(now / windowSize);\n        const currentKey = [identifier, currentWindow].join(\":\");\n        const previousWindow = currentWindow - 1;\n        const previousKey = [identifier, previousWindow].join(\":\");\n\n        const usedTokens = await safeEval(\n          ctx,\n          SCRIPTS.singleRegion.slidingWindow.getRemaining,\n          [currentKey, previousKey],\n          [now, windowSize],\n        ) as number;\n\n        return {\n          remaining: Math.max(0, tokens - usedTokens),\n          reset: (currentWindow + 1) * windowSize\n        }\n      },\n      async resetTokens(ctx: RegionContext, identifier: string) {\n        const pattern = [identifier, \"*\"].join(\":\");\n        if (ctx.cache) {\n          ctx.cache.pop(identifier)\n        }\n\n        await safeEval(\n          ctx,\n          RESET_SCRIPT,\n          [pattern],\n          [null],\n        ) as number;\n      },\n    });\n  }\n\n  /**\n   * You have a bucket filled with `{maxTokens}` tokens that refills constantly\n   * at `{refillRate}` per `{interval}`.\n   * Every request will remove one token from the bucket and if there is no\n   * token to take, the request is rejected.\n   *\n   * **Pro:**\n   *\n   * - Bursts of requests are smoothed out and you can process them at a constant\n   * rate.\n   * - Allows to set a higher initial burst limit by setting `maxTokens` higher\n   * than `refillRate`\n   */\n  static tokenBucket(\n    /**\n     * How many tokens are refilled per `interval`\n     *\n     * An interval of `10s` and refillRate of 5 will cause a new token to be added every 2 seconds.\n     */\n    refillRate: number,\n    /**\n     * The interval for the `refillRate`\n     */\n    interval: Duration,\n    /**\n     * Maximum number of tokens.\n     * A newly created bucket starts with this many tokens.\n     * Useful to allow higher burst limits.\n     */\n    maxTokens: number,\n  ): Algorithm<RegionContext> {\n    const intervalDuration = ms(interval);\n    return () => ({\n      async limit(ctx: RegionContext, identifier: string, rate?: number) {\n        if (ctx.cache) {\n          const { blocked, reset } = ctx.cache.isBlocked(identifier);\n          if (blocked) {\n            return {\n              success: false,\n              limit: maxTokens,\n              remaining: 0,\n              reset: reset,\n              pending: Promise.resolve(),\n              reason: \"cacheBlock\"\n            };\n          }\n        }\n\n        const now = Date.now();\n\n        const incrementBy = rate ? Math.max(1, rate) : 1;\n\n        const [remaining, reset] = await safeEval(\n          ctx,\n          SCRIPTS.singleRegion.tokenBucket.limit,\n          [identifier],\n          [maxTokens, intervalDuration, refillRate, now, incrementBy],\n        ) as [number, number];\n\n        const success = remaining >= 0;\n        if (ctx.cache && !success) {\n          ctx.cache.blockUntil(identifier, reset);\n        }\n\n        return {\n          success,\n          limit: maxTokens,\n          remaining,\n          reset,\n          pending: Promise.resolve(),\n        };\n      },\n      async getRemaining(ctx: RegionContext, identifier: string) {\n\n        const [remainingTokens, refilledAt] = await safeEval(\n          ctx,\n          SCRIPTS.singleRegion.tokenBucket.getRemaining,\n          [identifier],\n          [maxTokens],\n        ) as [number, number];\n\n        const freshRefillAt = Date.now() + intervalDuration\n        const identifierRefillsAt = refilledAt + intervalDuration\n\n        return {\n          remaining: remainingTokens,\n          reset: refilledAt === tokenBucketIdentifierNotFound ? freshRefillAt : identifierRefillsAt\n        };\n      },\n      async resetTokens(ctx: RegionContext, identifier: string) {\n        const pattern = identifier;\n        if (ctx.cache) {\n          ctx.cache.pop(identifier)\n        }\n\n        await safeEval(\n          ctx,\n          RESET_SCRIPT,\n          [pattern],\n          [null],\n        ) as number;\n      },\n    });\n  }\n  /**\n   * cachedFixedWindow first uses the local cache to decide if a request may pass and then updates\n   * it asynchronously.\n   * This is experimental and not yet recommended for production use.\n   *\n   * @experimental\n   *\n   * Each request inside a fixed time increases a counter.\n   * Once the counter reaches the maximum allowed number, all further requests are\n   * rejected.\n   *\n   * **Pro:**\n   *\n   * - Newer requests are not starved by old ones.\n   * - Low storage cost.\n   *\n   * **Con:**\n   *\n   * A burst of requests near the boundary of a window can result in a very\n   * high request rate because two windows will be filled with requests quickly.\n   *\n   * @param tokens - How many requests a user can make in each time window.\n   * @param window - A fixed timeframe\n   */\n  static cachedFixedWindow(\n    /**\n     * How many requests are allowed per window.\n     */\n    tokens: number,\n    /**\n     * The duration in which `tokens` requests are allowed.\n     */\n    window: Duration,\n  ): Algorithm<RegionContext> {\n    const windowDuration = ms(window);\n\n    return () => ({\n      async limit(ctx: RegionContext, identifier: string, rate?: number) {\n        if (!ctx.cache) {\n          throw new Error(\"This algorithm requires a cache\");\n        }\n        const bucket = Math.floor(Date.now() / windowDuration);\n        const key = [identifier, bucket].join(\":\");\n        const reset = (bucket + 1) * windowDuration;\n        const incrementBy = rate ? Math.max(1, rate) : 1;\n\n        const hit = typeof ctx.cache.get(key) === \"number\";\n        if (hit) {\n          const cachedTokensAfterUpdate = ctx.cache.incr(key);\n          const success = cachedTokensAfterUpdate < tokens;\n\n          const pending = success\n            ? safeEval(\n              ctx,\n              SCRIPTS.singleRegion.cachedFixedWindow.limit,\n              [key],\n              [windowDuration, incrementBy]\n            )\n            : Promise.resolve();\n\n          return {\n            success,\n            limit: tokens,\n            remaining: tokens - cachedTokensAfterUpdate,\n            reset: reset,\n            pending,\n          };\n        }\n\n        const usedTokensAfterUpdate = await safeEval(\n          ctx,\n          SCRIPTS.singleRegion.cachedFixedWindow.limit,\n          [key],\n          [windowDuration, incrementBy]\n        ) as number;\n        ctx.cache.set(key, usedTokensAfterUpdate);\n        const remaining = tokens - usedTokensAfterUpdate;\n\n        return {\n          success: remaining >= 0,\n          limit: tokens,\n          remaining,\n          reset: reset,\n          pending: Promise.resolve(),\n        };\n      },\n      async getRemaining(ctx: RegionContext, identifier: string) {\n        if (!ctx.cache) {\n          throw new Error(\"This algorithm requires a cache\");\n        }\n\n        const bucket = Math.floor(Date.now() / windowDuration);\n        const key = [identifier, bucket].join(\":\");\n\n        const hit = typeof ctx.cache.get(key) === \"number\";\n        if (hit) {\n          const cachedUsedTokens = ctx.cache.get(key) ?? 0;\n          return {\n            remaining: Math.max(0, tokens - cachedUsedTokens),\n            reset: (bucket + 1) * windowDuration\n          };\n        }\n\n        const usedTokens = await safeEval(\n          ctx,\n          SCRIPTS.singleRegion.cachedFixedWindow.getRemaining,\n          [key],\n          [null],\n        ) as number;\n        return {\n          remaining: Math.max(0, tokens - usedTokens),\n          reset: (bucket + 1) * windowDuration\n        };\n      },\n      async resetTokens(ctx: RegionContext, identifier: string) {\n        // Empty the cache\n        if (!ctx.cache) {\n          throw new Error(\"This algorithm requires a cache\");\n        }\n\n        const bucket = Math.floor(Date.now() / windowDuration);\n        const key = [identifier, bucket].join(\":\");\n        ctx.cache.pop(key)\n\n        const pattern = [identifier, \"*\"].join(\":\");\n\n        await safeEval(\n          ctx,\n          RESET_SCRIPT,\n          [pattern],\n          [null],\n        ) as number;\n      },\n    });\n  }\n}\n"], "names": ["CoreAnalytics", "fixedWindowLimitScript", "fixedWindowRemainingTokensScript", "slidingWindowLimitScript", "slidingWindowRemainingTokensScript", "fixedWindowLimitScript", "fixedWindowRemainingTokensScript", "slidingWindowLimitScript", "slidingWindowRemainingTokensScript", "reset", "requestId", "current", "reset"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAA,cAAA,CAAA;AAAA,SAAA,aAAA;IAAA,WAAA,IAAA;IAAA,YAAA,IAAA;IAAA,sBAAA,IAAA;IAAA,WAAA,IAAA;AAAA;AAAA,OAAA,OAAA,GAAA,aAAA;;ACCA,IAAA,wBAA2C;AA+BpC,IAAM,YAAN,MAAgB;IACJ,UAAA;IACA,QAAQ,SAAA;IAEzB,YAAY,MAAA,CAAyB;QACnC,IAAA,CAAK,SAAA,GAAY,IAAI,sBAAAA,SAAAA,CAAc;YAAA,8HAAA;YAEjC,OAAO,OAAO,KAAA;YACd,QAAQ;YACR,QAAQ,OAAO,MAAA,IAAU;YACzB,WAAW;QACb,CAAC;IACH;IAAA;;;;;;GAAA,GASO,WAAW,GAAA,EAAmC;QACnD,IAAI,IAAI,GAAA,KAAQ,KAAA,GAAW;YACzB,OAAO,IAAI,GAAA;QACb;QACA,IAAI,IAAI,EAAA,KAAO,KAAA,GAAW;YACxB,OAAO,IAAI,EAAA;QACb;QAEA,OAAO,CAAC;IACV;IAEA,MAAa,OAAO,KAAA,EAA6B;QAC/C,MAAM,IAAA,CAAK,SAAA,CAAU,MAAA,CAAO,IAAA,CAAK,KAAA,EAAO,KAAK;IAC/C;IAEA,MAAa,OACX,MAAA,EACA,MAAA,EACsB;QACtB,MAAM,iBAAiB,KAAK,GAAA,CAAA,CAExB,IAAA,CAAK,SAAA,CAAU,SAAA,CAAU,KAAK,GAAA,CAAI,CAAC,IACjC,IAAA,CAAK,SAAA,CAAU,SAAA,CAAU,MAAM,CAAA,IAAA,CAC9B,KAAK,KAAK,GAAA,GACf;QAEF,OAAO,IAAA,CAAK,SAAA,CAAU,4BAAA,CAA6B,IAAA,CAAK,KAAA,EAAO,QAAQ,cAAc;IACvF;IAEA,MAAa,SAAS,SAAS,CAAA,EAAkE;QAE/F,MAAM,iBAAiB,KAAK,GAAA,CAAA,CAExB,IAAA,CAAK,SAAA,CAAU,SAAA,CAAU,KAAK,GAAA,CAAI,CAAC,IACjC,IAAA,CAAK,SAAA,CAAU,SAAA,CAAU,MAAM,CAAA,IAAA,CAC9B,KAAK,KAAK,GAAA,GACf;QAEF,MAAM,UAAU,MAAM,IAAA,CAAK,SAAA,CAAU,iBAAA,CAAkB,IAAA,CAAK,KAAA,EAAO,cAAc;QACjF,OAAO;IACT;IAEA,MAAa,iBACX,cAAA,EAAwB,OAAA,EACF;QACtB,MAAM,SAAS,MAAM,IAAA,CAAK,SAAA,CAAU,4BAAA,CAA6B,IAAA,CAAK,KAAA,EAAO,SAAS,cAAc;QACpG,OAAO;IACT;IAEA,MAAa,sBAAsB,cAAA,EAAwB,MAAA,EAAiB,WAAA,EAAsB;QAChG,SAAS,UAAU;QACnB,MAAM,YAAY,KAAA;QAClB,OAAO,IAAA,CAAK,SAAA,CAAU,qBAAA,CAAsB,IAAA,CAAK,KAAA,EAAO,gBAAgB,QAAQ,WAAW,WAAW;IACxG;AACF;;ACzGO,IAAM,QAAN,MAAsC;IAAA;;GAAA,GAI1B,MAAA;IAEjB,YAAY,KAAA,CAA4B;QACtC,IAAA,CAAK,KAAA,GAAQ;IACf;IAEO,UAAU,UAAA,EAAyD;QACxE,IAAI,CAAC,IAAA,CAAK,KAAA,CAAM,GAAA,CAAI,UAAU,GAAG;YAC/B,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAE;QACpC;QACA,MAAM,QAAQ,IAAA,CAAK,KAAA,CAAM,GAAA,CAAI,UAAU;QACvC,IAAI,QAAQ,KAAK,GAAA,CAAI,GAAG;YACtB,IAAA,CAAK,KAAA,CAAM,MAAA,CAAO,UAAU;YAC5B,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAE;QACpC;QAEA,OAAO;YAAE,SAAS;YAAM;QAAa;IACvC;IAEO,WAAW,UAAA,EAAoB,KAAA,EAAqB;QACzD,IAAA,CAAK,KAAA,CAAM,GAAA,CAAI,YAAY,KAAK;IAClC;IAEO,IAAI,GAAA,EAAa,KAAA,EAAqB;QAC3C,IAAA,CAAK,KAAA,CAAM,GAAA,CAAI,KAAK,KAAK;IAC3B;IACO,IAAI,GAAA,EAA4B;QACrC,OAAO,IAAA,CAAK,KAAA,CAAM,GAAA,CAAI,GAAG,KAAK;IAChC;IAEO,KAAK,GAAA,EAAqB;QAC/B,IAAI,QAAQ,IAAA,CAAK,KAAA,CAAM,GAAA,CAAI,GAAG,KAAK;QACnC,SAAS;QACT,IAAA,CAAK,KAAA,CAAM,GAAA,CAAI,KAAK,KAAK;QACzB,OAAO;IACT;IAEO,IAAI,GAAA,EAAmB;QAC5B,IAAA,CAAK,KAAA,CAAM,MAAA,CAAO,GAAG;IACvB;IAEO,QAAc;QACnB,IAAA,CAAK,KAAA,CAAM,KAAA,CAAM;IACnB;IAEO,OAAe;QACpB,OAAO,IAAA,CAAK,KAAA,CAAM,IAAA;IACpB;AACF;;AChDO,SAAS,GAAG,CAAA,EAAqB;IACtC,MAAM,QAAQ,EAAE,KAAA,CAAM,wBAAwB;IAC9C,IAAI,CAAC,OAAO;QACV,MAAM,IAAI,MAAM,CAAA,6BAAA,EAAgC,CAAC,EAAE;IACrD;IACA,MAAM,OAAO,OAAO,QAAA,CAAS,KAAA,CAAM,CAAC,CAAC;IACrC,MAAM,OAAO,KAAA,CAAM,CAAC,CAAA;IAEpB,OAAQ,MAAM;QACZ,KAAK;YAAM;gBACT,OAAO;YACT;QACA,KAAK;YAAK;gBACR,OAAO,OAAO;YAChB;QACA,KAAK;YAAK;gBACR,OAAO,OAAO,MAAO;YACvB;QACA,KAAK;YAAK;gBACR,OAAO,OAAO,MAAO,KAAK;YAC5B;QACA,KAAK;YAAK;gBACR,OAAO,OAAO,MAAO,KAAK,KAAK;YACjC;QAEA;YAAS;gBACP,MAAM,IAAI,MAAM,CAAA,6BAAA,EAAgC,CAAC,EAAE;YACrD;IACF;AACF;;ACrBO,IAAM,WAAW,OACtB,KACA,QACA,MACA,SACG;IACH,IAAI;QACF,OAAO,MAAM,IAAI,KAAA,CAAM,OAAA,CAAQ,OAAO,IAAA,EAAM,MAAM,IAAI;IACxD,EAAA,OAAS,OAAO;QACd,IAAI,GAAG,KAAK,EAAA,CAAG,QAAA,CAAS,UAAU,GAAG;YACnC,MAAM,OAAO,MAAM,IAAI,KAAA,CAAM,UAAA,CAAW,OAAO,MAAM;YAErD,IAAI,SAAS,OAAO,IAAA,EAAM;gBACxB,QAAQ,IAAA,CACN;YAIJ;YAEA,OAAO,MAAM,IAAI,KAAA,CAAM,OAAA,CAAQ,MAAM,MAAM,IAAI;QACjD;QACA,MAAM;IACR;AACF;;ACtCO,IAAM,yBAAyB,CAAA;;;;;;;;;;;;;AAAA,CAAA;AAe/B,IAAM,mCAAmC,CAAA;;;;;;;;;IAAA,CAAA;AAWzC,IAAM,2BAA2B,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,CAAA;AAiCjC,IAAM,qCAAqC,CAAA;;;;;;;;;;;;;;;;;;;;;AAAA,CAAA;AAuB3C,IAAM,yBAAyB,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,CAAA;AAwC/B,IAAM,gCAAgC,CAAA;AAEtC,IAAM,mCAAmC,CAAA;;;;;;;uBAAA,EAOvB,6BAA6B,CAAA;;;;AAAA,CAAA;AAM/C,IAAM,+BAA+B,CAAA;;;;;;;;;;;;;AAAA,CAAA;AAerC,IAAM,wCAAwC,CAAA;;;;;;;;;AAAA,CAAA;;ACxJ9C,IAAMC,0BAAyB,CAAA;;;;;;;;;;;;;;;AAAA,CAAA;AAgB/B,IAAMC,oCAAmC,CAAA;;;;;;;IAAA,CAAA;AASzC,IAAMC,4BAA2B,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,CAAA;AAoCjC,IAAMC,sCAAqC,CAAA;;;;;;;;;;;;;;;;;;;;;;AAAA,CAAA;;AC7D3C,IAAM,cAAc,CAAA;;;;;;;;;;;;;;;;;;;;;;IAAA,CAAA;;ACoBpB,IAAM,UAGT;IACF,cAAc;QACZ,aAAa;YACX,OAAO;gBACL,QAAe;gBACf,MAAM;YACR;YACA,cAAc;gBACZ,QAAe;gBACf,MAAM;YACR;QACF;QACA,eAAe;YACb,OAAO;gBACL,QAAe;gBACf,MAAM;YACR;YACA,cAAc;gBACZ,QAAe;gBACf,MAAM;YACR;QACF;QACA,aAAa;YACX,OAAO;gBACL,QAAe;gBACf,MAAM;YACR;YACA,cAAc;gBACZ,QAAe;gBACf,MAAM;YACR;QACF;QACA,mBAAmB;YACjB,OAAO;gBACL,QAAe;gBACf,MAAM;YACR;YACA,cAAc;gBACZ,QAAe;gBACf,MAAM;YACR;QACF;IACF;IACA,aAAa;QACX,aAAa;YACX,OAAO;gBACL,QAAcC;gBACd,MAAM;YACR;YACA,cAAc;gBACZ,QAAcC;gBACd,MAAM;YACR;QACF;QACA,eAAe;YACb,OAAO;gBACL,QAAcC;gBACd,MAAM;YACR;YACA,cAAc;gBACZ,QAAcC;gBACd,MAAM;YACR;QACF;IACF;AACF;AAGO,IAAM,eAA2B;IACtC,QAAQ;IACR,MAAM;AACR;;ACcO,IAAM,oBAAoB;AAC1B,IAAM,gBAAgB;AACtB,IAAM,sBAAsB;;AC9G5B,IAAM,sBAAsB,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,CAAA;;ACAnC,IAAA,uBAAA,CAAA;AAAA,SAAA,sBAAA;IAAA,gBAAA,IAAA;IAAA,mBAAA,IAAA;IAAA,kBAAA,IAAA;AAAA;;ACEA,IAAM,uBAAuB,KAAK,KAAK;AAGvC,IAAM,sBAAsB,KAAK;AAGjC,IAAM,sBAAsB,IAAI;AAEzB,IAAM,eAAe,CAAC,SAAkB;IAC7C,MAAM,MAAM,QAAQ,KAAK,GAAA,CAAI;IAG7B,MAAM,mBAAA,CAAoB,MAAM,mBAAA,IAAuB;IAGvD,OAAO,sBAAsB;AAC/B;;ADdA,IAAM,UAAU;AAET,IAAM,iBAAN,cAA6B,MAAM;IACxC,YAAY,SAAA,CAAmB;QAC7B,KAAA,CAAM,CAAA,sEAAA,EAAyE,SAAS,EAAE;QAC1F,IAAA,CAAK,IAAA,GAAO;IACd;AACF;AAcA,IAAM,gBAAgB,OAAO,cAAsB;IACjD,IAAI,OAAO,cAAc,YAAY,YAAY,KAAK,YAAY,GAAG;QACnE,MAAM,IAAI,eAAe,SAAS;IACpC;IAEA,IAAI;QAEF,MAAM,WAAW,MAAM,MAAM,GAAG,OAAO,CAAA,CAAA,EAAI,SAAS,CAAA,IAAA,CAAM;QAC1D,IAAI,CAAC,SAAS,EAAA,EAAI;YAChB,MAAM,IAAI,MAAM,CAAA,qBAAA,EAAwB,SAAS,UAAU,EAAE;QAC/D;QACA,MAAM,OAAO,MAAM,SAAS,IAAA,CAAK;QAGjC,MAAM,QAAQ,KAAK,KAAA,CAAM,IAAI;QAC7B,OAAO,MAAM,MAAA,CAAO,CAAC,QAAU,MAAM,MAAA,GAAS,CAAC;IACjD,EAAA,OAAS,OAAO;QACd,MAAM,IAAI,MAAM,CAAA,8BAAA,EAAiC,KAAK,EAAE;IAC1D;AACF;AAsBO,IAAM,mBAAmB,OAC9B,OACA,QACA,WACA,QACG;IACH,MAAM,SAAS,MAAM,cAAc,SAAS;IAE5C,MAAM,eAAe;QAAC;QAAQ;QAAmB,KAAK;KAAA,CAAE,IAAA,CAAK,GAAG;IAChE,MAAM,aAAa;QAAC;QAAQ;QAAmB,aAAa;KAAA,CAAE,IAAA,CAAK,GAAG;IACtE,MAAM,YAAY;QAAC;QAAQ,mBAAmB;KAAA,CAAE,IAAA,CAAK,GAAG;IAExD,MAAM,cAAc,MAAM,KAAA,CAAM;IAGhC,YAAY,UAAA,CAAW,cAAc,cAAc,UAAU;IAG7D,YAAY,GAAA,CAAI,UAAU;IAE1B,YAAY,IAAA,CAAK,YAAY,OAAO,EAAA,CAAG,CAAC,GAAG,GAAG,OAAO,KAAA,CAAM,CAAC,CAAC;IAI7D,YAAY,UAAA,CAAW,YAAY,YAAY,YAAY;IAG3D,YAAY,WAAA,CAAY,cAAc,cAAc,UAAU;IAG9D,YAAY,GAAA,CAAI,WAAW,SAAS;QAAC,IAAI,OAAO,aAAa;IAAC,CAAC;IAE/D,OAAO,MAAM,YAAY,IAAA,CAAK;AAChC;AAWO,IAAM,oBAAoB,OAAO,OAAc,WAAmB;IACvE,MAAM,kBAAkB;QAAC;QAAQ;QAAmB,KAAK;KAAA,CAAE,IAAA,CAAK,GAAG;IACnE,MAAM,gBAAgB;QAAC;QAAQ;QAAmB,aAAa;KAAA,CAAE,IAAA,CAAK,GAAG;IACzE,MAAM,YAAY;QAAC;QAAQ,mBAAmB;KAAA,CAAE,IAAA,CAAK,GAAG;IAExD,MAAM,cAAc,MAAM,KAAA,CAAM;IAGhC,YAAY,UAAA,CAAW,iBAAiB,iBAAiB,aAAa;IAGtE,YAAY,GAAA,CAAI,aAAa;IAI7B,YAAY,GAAA,CAAI,WAAW,UAAU;IAErC,OAAO,MAAM,YAAY,IAAA,CAAK;AAChC;;AExHA,IAAM,gBAAgB,IAAI,MAAM,aAAA,GAAA,IAAI,IAAI,CAAC;AASlC,IAAM,qBAAqB,CAAC,YAAmC;IACpE,OAAO,QAAQ,IAAA,CACb,CAAA,SAAU,cAAc,SAAA,CAAU,MAAM,EAAE,OAAA;AAE9C;AAUA,IAAM,cAAc,CAAC,WAAmB;IACtC,IAAI,cAAc,IAAA,CAAK,IAAI,KAAM,cAAc,KAAA,CAAM;IACrD,cAAc,UAAA,CAAW,QAAQ,KAAK,GAAA,CAAI,IAAI,GAAM;AACtD;AAaO,IAAM,gBAAgB,OAC3B,OACA,QACA,YAC8B;IAC9B,MAAM,CAAE,cAAc,gBAAiB,CAAA,GAAI,MAAM,MAAM,IAAA,CACrD,qBACA;QACE;YAAC;YAAQ;YAAmB,KAAK;SAAA,CAAE,IAAA,CAAK,GAAG;QAC3C;YAAC;YAAQ,mBAAmB;SAAA,CAAE,IAAA,CAAK,GAAG;KACxC,EACA;IAGF,IAAI,cAA2B,KAAA;IAC/B,aAAa,GAAA,CAAI,CAAC,cAAc,UAAU;QACxC,IAAI,cAAc;YAChB,YAAY,OAAA,CAAQ,KAAK,CAAC;YAC1B,cAAc,OAAA,CAAQ,KAAK,CAAA;QAC7B;IACF,CAAC;IAED,OAAO;QACL;QACA,mBAAmB,qBAAqB,CAAA;IAC1C;AACF;AAUO,IAAM,sBAAsB,CACjC,OACA,QACA,CAAC,mBAAmB,gBAAgB,CAAA,EACpC,cACsB;IAEtB,IAAI,iBAAiB,WAAA,EAAa;QAChC,kBAAkB,OAAA,GAAU;QAC5B,kBAAkB,SAAA,GAAY;QAC9B,kBAAkB,MAAA,GAAS;QAC3B,kBAAkB,WAAA,GAAc,iBAAiB,WAAA;IACnD;IAEA,IAAI,iBAAiB,iBAAA,EAAmB;QACtC,MAAM,gBAAgB,iBAAiB,OAAO,QAAQ,SAAS;QAC/D,kBAAkB,OAAA,GAAU,QAAQ,GAAA,CAAI;YACtC,kBAAkB,OAAA;YAClB;SACD;IACH;IAEA,OAAO;AACT;AAOO,IAAM,wBAAwB,CAAC,gBAA2C;IAC/E,OAAO;QACL,SAAS;QACT,OAAO;QACP,WAAW;QACX,OAAO;QACP,SAAS,QAAQ,OAAA,CAAQ;QACzB,QAAQ;QACR;IACF;AACF;;AC7BO,IAAe,YAAf,MAAmD;IACrC,QAAA;IAEA,IAAA;IAEA,OAAA;IAEA,QAAA;IAEA,aAAA;IAEA,UAAA;IAEA,iBAAA;IAEA,kBAAA;IAEnB,YAAY,MAAA,CAAmC;QAC7C,IAAA,CAAK,GAAA,GAAM,OAAO,GAAA;QAClB,IAAA,CAAK,OAAA,GAAU,OAAO,OAAA;QACtB,IAAA,CAAK,OAAA,GAAU,OAAO,OAAA,IAAW;QACjC,IAAA,CAAK,MAAA,GAAS,OAAO,MAAA,IAAU;QAE/B,IAAA,CAAK,gBAAA,GAAmB,OAAO,gBAAA,IAAoB;QACnD,IAAA,CAAK,iBAAA,GAAoB,OAAO,iBAAA,IAAqB;QAErD,IAAA,CAAK,YAAA,GAAgB,WAAW,IAAA,CAAK,GAAA,GAAO,IAAA,CAAK,GAAA,CAAI,KAAA,GAAQ,IAAA,CAAK,GAAA,CAAI,cAAA,CAAe,CAAC,CAAA,CAAE,KAAA;QACxF,IAAA,CAAK,SAAA,GAAY,OAAO,SAAA,GACpB,IAAI,UAAU;YACd,OAAO,IAAA,CAAK,YAAA;YACZ,QAAQ,IAAA,CAAK,MAAA;QACf,CAAC,IACC,KAAA;QAEJ,IAAI,OAAO,cAAA,YAA0B,KAAK;YACxC,IAAA,CAAK,GAAA,CAAI,KAAA,GAAQ,IAAI,MAAM,OAAO,cAAc;QAClD,OAAA,IAAW,OAAO,cAAA,KAAmB,KAAA,GAAW;YAC9C,IAAA,CAAK,GAAA,CAAI,KAAA,GAAQ,IAAI,MAAM,aAAA,GAAA,IAAI,IAAI,CAAC;QACtC;IACF;IAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAAA,GAsCO,QAAQ,OACb,YACA,QAC+B;QAE/B,IAAI,YAAiB;QACrB,IAAI;YACF,MAAM,WAAW,IAAA,CAAK,oBAAA,CAAqB,YAAY,GAAG;YAC1D,MAAM,EAAE,aAAA,EAAe,YAAA,CAAa,CAAA,GAAI,IAAA,CAAK,YAAA,CAAa,QAAQ;YAClE,YAAY;YAEZ,MAAM,gBAAgB,MAAM,QAAQ,IAAA,CAAK,aAAa;YACtD,MAAM,gBAAgB,IAAA,CAAK,eAAA,CAAgB,eAAe,YAAY,GAAG;YACzE,OAAO;QACT,SAAE;YACA,IAAI,WAAW;gBACb,aAAa,SAAS;YACxB;QACF;IACF,EAAA;IAAA;;;;;;;;;;;;;;;;;;;;;GAAA,GAwBO,kBAAkB,OAOvB,YAKA,YAC+B;QAC/B,IAAI,WAAW,GAAG;YAChB,MAAM,IAAI,MAAM,0BAA0B;QAC5C;QACA,IAAI;QAEJ,MAAM,WAAW,KAAK,GAAA,CAAI,IAAI;QAC9B,MAAO,KAAM;YACX,MAAM,MAAM,IAAA,CAAK,KAAA,CAAM,UAAU;YACjC,IAAI,IAAI,OAAA,EAAS;gBACf;YACF;YACA,IAAI,IAAI,KAAA,KAAU,GAAG;gBACnB,MAAM,IAAI,MAAM,wBAAwB;YAC1C;YAEA,MAAM,OAAO,KAAK,GAAA,CAAI,IAAI,KAAA,EAAO,QAAQ,IAAI,KAAK,GAAA,CAAI;YACtD,MAAM,IAAI,QAAQ,CAAC,IAAM,WAAW,GAAG,IAAI,CAAC;YAE5C,IAAI,KAAK,GAAA,CAAI,IAAI,UAAU;gBACzB;YACF;QACF;QACA,OAAO;IACT,EAAA;IAEO,kBAAkB,OAAO,eAAuB;QACrD,MAAM,UAAU;YAAC,IAAA,CAAK,MAAA;YAAQ,UAAU;SAAA,CAAE,IAAA,CAAK,GAAG;QAClD,MAAM,IAAA,CAAK,OAAA,CAAQ,EAAE,WAAA,CAAY,IAAA,CAAK,GAAA,EAAK,OAAO;IACpD,EAAA;IAAA;;;;;;;GAAA,GAUO,eAAe,OAAO,eAGvB;QACJ,MAAM,UAAU;YAAC,IAAA,CAAK,MAAA;YAAQ,UAAU;SAAA,CAAE,IAAA,CAAK,GAAG;QAElD,OAAO,MAAM,IAAA,CAAK,OAAA,CAAQ,EAAE,YAAA,CAAa,IAAA,CAAK,GAAA,EAAK,OAAO;IAC5D,EAAA;IAAA;;;;;;;;;;;GAAA,GAcQ,uBAAuB,OAC7B,YACA,QAC+B;QAC/B,MAAM,MAAM,IAAA,CAAK,MAAA,CAAO,UAAU;QAClC,MAAM,iBAAiB,IAAA,CAAK,iBAAA,CAAkB,YAAY,GAAG;QAE7D,MAAM,cAAc,mBAAmB,cAAc;QAErD,MAAM,SAAuB,cAAc;YAAC,sBAAsB,WAAW;YAAG;gBAAE;gBAAa,mBAAmB;YAAM,CAAC;SAAA,GAAK,MAAM,QAAQ,GAAA,CAAI;YAC9I,IAAA,CAAK,OAAA,CAAQ,EAAE,KAAA,CAAM,IAAA,CAAK,GAAA,EAAK,KAAK,KAAK,IAAI;YAC7C,IAAA,CAAK,gBAAA,GACD,cAAc,IAAA,CAAK,YAAA,EAAc,IAAA,CAAK,MAAA,EAAQ,cAAc,IAC5D;gBAAE,aAAa,KAAA;gBAAW,mBAAmB;YAAM;SACxD;QAED,OAAO,oBAAoB,IAAA,CAAK,YAAA,EAAc,IAAA,CAAK,MAAA,EAAQ,QAAQ,IAAA,CAAK,iBAAiB;IAC3F,EAAA;IAAA;;;;;;GAAA,GASQ,eAAe,CAAC,aAAyC;QAC/D,IAAI,eAAoB;QACxB,MAAM,gBAAmD;YAAC,QAAQ;SAAA;QAElE,IAAI,IAAA,CAAK,OAAA,GAAU,GAAG;YACpB,MAAM,kBAAkB,IAAI,QAA2B,CAAC,YAAY;gBAClE,eAAe,WAAW,MAAM;oBAC9B,QAAQ;wBACN,SAAS;wBACT,OAAO;wBACP,WAAW;wBACX,OAAO;wBACP,SAAS,QAAQ,OAAA,CAAQ;wBACzB,QAAQ;oBACV,CAAC;gBACH,GAAG,IAAA,CAAK,OAAO;YACjB,CAAC;YACD,cAAc,IAAA,CAAK,eAAe;QACpC;QAEA,OAAO;YACL;YACA;QACF;IACF,EAAA;IAAA;;;;;;;GAAA,GAUQ,kBAAkB,CACxB,mBACA,YACA,QACG;QACH,IAAI,IAAA,CAAK,SAAA,EAAW;YAClB,IAAI;gBACF,MAAM,MAAM,MAAM,IAAA,CAAK,SAAA,CAAU,UAAA,CAAW,GAAG,IAAI,KAAA;gBACnD,MAAM,aAAa,IAAA,CAAK,SAAA,CACrB,MAAA,CAAO;oBACN,YAAY,kBAAkB,MAAA,KAAW,aACrC,kBAAkB,WAAA,GAClB;oBACJ,MAAM,KAAK,GAAA,CAAI;oBACf,SAAS,kBAAkB,MAAA,KAAW,aAClC,WACA,kBAAkB,OAAA;oBACtB,GAAG,GAAA;gBACL,CAAC,EACA,KAAA,CAAM,CAAC,UAAU;oBAChB,IAAI,eAAe;oBACnB,IAAI,GAAG,KAAK,EAAA,CAAG,QAAA,CAAS,WAAW,GAAG;wBACpC,eAAe,CAAA;;;;;;;;;;IAAA,CAAA;oBAUjB;oBACA,QAAQ,IAAA,CAAK,cAAc,KAAK;gBAClC,CAAC;gBACH,kBAAkB,OAAA,GAAU,QAAQ,GAAA,CAAI;oBAAC,kBAAkB,OAAA;oBAAS,UAAU;iBAAC;YACjF,EAAA,OAAS,OAAO;gBACd,QAAQ,IAAA,CAAK,8BAA8B,KAAK;YAClD;;QACF;;QACA,OAAO;IACT,EAAA;IAEQ,SAAS,CAAC,eAA+B;QAC/C,OAAO;YAAC,IAAA,CAAK,MAAA;YAAQ,UAAU;SAAA,CAAE,IAAA,CAAK,GAAG;IAC3C,EAAA;IAAA;;;;;;;GAAA,GAUQ,oBAAoB,CAC1B,YACA,QACa;QACb,MAAM,UAAU;YAAC;YAAY,KAAK;YAAI,KAAK;YAAW,KAAK,OAAO;SAAA;QAClE,OAAQ,QAAqB,MAAA,CAAO,OAAO;IAC7C,EAAA;AACF;;AC7YA,SAAS,WAAmB;IAC1B,IAAI,SAAS;IACb,MAAM,aAAa;IACnB,MAAM,mBAAmB,WAAW,MAAA;IACpC,IAAA,IAAS,IAAI,GAAG,IAAI,IAAI,IAAK;QAC3B,UAAU,WAAW,MAAA,CAAO,KAAK,KAAA,CAAM,KAAK,MAAA,CAAO,IAAI,gBAAgB,CAAC;IAC1E;IACA,OAAO;AACT;AAgFO,IAAM,uBAAN,cAAmC,UAA8B;IAAA;;GAAA,GAItE,YAAY,MAAA,CAAoC;QAC9C,KAAA,CAAM;YACJ,QAAQ,OAAO,MAAA;YACf,SAAS,OAAO,OAAA;YAChB,SAAS,OAAO,OAAA;YAChB,WAAW,OAAO,SAAA;YAClB,KAAK;gBACH,gBAAgB,OAAO,KAAA,CAAM,GAAA,CAAI,CAAA,QAAA,CAAU;wBACzC;oBACF,CAAA,CAAE;gBACF,OAAO,OAAO,cAAA,GAAiB,IAAI,MAAM,OAAO,cAAc,IAAI,KAAA;YACpE;QACF,CAAC;IACH;IAAA;;;;;;;;;;;;;;;;;GAAA,GAoBA,OAAO,YAIL,MAAA,EAIA,MAAA,EAC+B;QAC/B,MAAM,iBAAiB,GAAG,MAAM;QAEhC,OAAO,IAAA,CAAO;gBACZ,MAAM,OAAM,GAAA,EAAyB,UAAA,EAAoB,IAAA,EAAe;oBACtE,IAAI,IAAI,KAAA,EAAO;wBACb,MAAM,EAAE,OAAA,EAAS,OAAAC,MAAAA,CAAM,CAAA,GAAI,IAAI,KAAA,CAAM,SAAA,CAAU,UAAU;wBACzD,IAAI,SAAS;4BACX,OAAO;gCACL,SAAS;gCACT,OAAO;gCACP,WAAW;gCACX,OAAOA;gCACP,SAAS,QAAQ,OAAA,CAAQ;gCACzB,QAAQ;4BACV;wBACF;oBACF;oBAEA,MAAM,YAAY,SAAS;oBAC3B,MAAM,SAAS,KAAK,KAAA,CAAM,KAAK,GAAA,CAAI,IAAI,cAAc;oBACrD,MAAM,MAAM;wBAAC;wBAAY,MAAM;qBAAA,CAAE,IAAA,CAAK,GAAG;oBACzC,MAAM,cAAc,OAAO,KAAK,GAAA,CAAI,GAAG,IAAI,IAAI;oBAE/C,MAAM,MAAsD,IAAI,cAAA,CAAe,GAAA,CAAI,CAAC,gBAAA,CAAmB;4BACrG,OAAO,cAAc,KAAA;4BACrB,SAAS,SACP,eACA,QAAQ,WAAA,CAAY,WAAA,CAAY,KAAA,EAChC;gCAAC,GAAG;6BAAA,EACJ;gCAAC;gCAAW;gCAAgB,WAAW;6BAAA;wBAE3C,CAAA,CAAE;oBAGF,MAAM,gBAAgB,MAAM,QAAQ,GAAA,CAAI,IAAI,GAAA,CAAI,CAAC,IAAM,EAAE,OAAO,CAAC;oBAEjE,MAAM,aAAa,cAAc,MAAA,CAAO,CAAC,WAAmB,WAAW,UAAU;wBAC/E,IAAI,cAAc;wBAClB,IAAI,QAAQ,GAAG;4BACb,cAAc,OAAO,QAAA,CAAS,SAAS;wBACzC;wBAEA,OAAO,YAAY;oBACrB,GAAG,CAAC;oBAEJ,MAAM,YAAY,SAAS;oBAK3B,eAAe,OAAO;wBACpB,MAAM,gBAAgB,MAAM,QAAQ,GAAA,CAAI,IAAI,GAAA,CAAI,CAAC,IAAM,EAAE,OAAO,CAAC;wBAEjE,MAAM,SAAS,CAAC;+BAAG,IAAI,IACrB,cAAc,IAAA,CAAK,EAChB,MAAA,CAAO,CAAC,KAAe,MAAM,UAAU;gCACtC,IAAI,QAAQ,MAAM,GAAG;oCACnB,IAAI,IAAA,CAAK,IAAI;gCACf;gCACA,OAAO;4BACT,GAAG,CAAC,CAAC,GACP,MAAA,CAAO,CAAC;yBAAA;wBAEV,KAAA,MAAW,MAAM,IAAK;4BACpB,MAAM,sBAAsB,MAAM,GAAG,OAAA;4BACrC,MAAM,eAAe,oBAAoB,MAAA,CACvC,CAAC,WAAmB,WAAW,UAAU;gCACvC,IAAI,cAAc;gCAClB,IAAI,QAAQ,GAAG;oCACb,cAAc,OAAO,QAAA,CAAS,SAAS;gCACzC;gCAEA,OAAO,YAAY;4BACrB,GACA;4BAGF,MAAM,eAAe,MAAM,GAAG,OAAA;4BAC9B,MAAM,QAAQ,aAAa,MAAA,CAAO,CAAC,KAAe,WAAW,UAAU;gCACrE,IAAI,QAAQ,MAAM,GAAG;oCACnB,IAAI,IAAA,CAAK,SAAS;gCACpB;gCACA,OAAO;4BACT,GAAG,CAAC,CAAC;4BAKL,IAAI,gBAAgB,QAAQ;gCAC1B;4BACF;4BACA,MAAM,OAAO,OAAO,MAAA,CAAO,CAAC,KAAO,CAAC,MAAM,QAAA,CAAS,EAAE,CAAC;4BAItD,IAAI,KAAK,MAAA,KAAW,GAAG;gCACrB;4BACF;4BAEA,KAAA,MAAWC,cAAa,KAAM;gCAC5B,MAAM,GAAG,KAAA,CAAM,IAAA,CAAK,KAAK;oCAAE,CAACA,UAAS,CAAA,EAAG;gCAAY,CAAC;4BACvD;wBACF;oBACF;oBAMA,MAAM,UAAU,YAAY;oBAC5B,MAAM,QAAA,CAAS,SAAS,CAAA,IAAK;oBAE7B,IAAI,IAAI,KAAA,IAAS,CAAC,SAAS;wBACzB,IAAI,KAAA,CAAM,UAAA,CAAW,YAAY,KAAK;oBACxC;oBACA,OAAO;wBACL;wBACA,OAAO;wBACP;wBACA;wBACA,SAAS,KAAK;oBAChB;gBACF;gBACA,MAAM,cAAa,GAAA,EAAyB,UAAA,EAAoB;oBAC9D,MAAM,SAAS,KAAK,KAAA,CAAM,KAAK,GAAA,CAAI,IAAI,cAAc;oBACrD,MAAM,MAAM;wBAAC;wBAAY,MAAM;qBAAA,CAAE,IAAA,CAAK,GAAG;oBAEzC,MAAM,MAAsD,IAAI,cAAA,CAAe,GAAA,CAAI,CAAC,gBAAA,CAAmB;4BACrG,OAAO,cAAc,KAAA;4BACrB,SAAS,SACP,eACA,QAAQ,WAAA,CAAY,WAAA,CAAY,YAAA,EAChC;gCAAC,GAAG;6BAAA,EACJ;gCAAC,IAAI;6BAAA;wBAET,CAAA,CAAE;oBAGF,MAAM,gBAAgB,MAAM,QAAQ,GAAA,CAAI,IAAI,GAAA,CAAI,CAAC,IAAM,EAAE,OAAO,CAAC;oBACjE,MAAM,aAAa,cAAc,MAAA,CAAO,CAAC,WAAmB,WAAW,UAAU;wBAC/E,IAAI,cAAc;wBAClB,IAAI,QAAQ,GAAG;4BACb,cAAc,OAAO,QAAA,CAAS,SAAS;wBACzC;wBAEA,OAAO,YAAY;oBACrB,GAAG,CAAC;oBAEJ,OAAO;wBACL,WAAW,KAAK,GAAA,CAAI,GAAG,SAAS,UAAU;wBAC1C,OAAA,CAAQ,SAAS,CAAA,IAAK;oBACxB;gBACF;gBACA,MAAM,aAAY,GAAA,EAAyB,UAAA,EAAoB;oBAC7D,MAAM,UAAU;wBAAC;wBAAY,GAAG;qBAAA,CAAE,IAAA,CAAK,GAAG;oBAC1C,IAAI,IAAI,KAAA,EAAO;wBACb,IAAI,KAAA,CAAM,GAAA,CAAI,UAAU;oBAC1B;oBAEA,MAAM,QAAQ,GAAA,CAAI,IAAI,cAAA,CAAe,GAAA,CAAI,CAAC,kBAAkB;wBAC1D,SACE,eACA,cACA;4BAAC,OAAO;yBAAA,EACR;4BAAC,IAAI;yBAAA;oBAET,CAAC,CAAC;gBACJ;YACF,CAAA;IACF;IAAA;;;;;;;;;;;;;;;GAAA,GAkBA,OAAO,cAIL,MAAA,EAIA,MAAA,EAC+B;QAC/B,MAAM,aAAa,GAAG,MAAM;QAE5B,MAAM,iBAAiB,GAAG,MAAM;QAEhC,OAAO,IAAA,CAAO;gBACZ,MAAM,OAAM,GAAA,EAAyB,UAAA,EAAoB,IAAA,EAAe;oBACtE,IAAI,IAAI,KAAA,EAAO;wBACb,MAAM,EAAE,OAAA,EAAS,OAAAD,MAAAA,CAAM,CAAA,GAAI,IAAI,KAAA,CAAM,SAAA,CAAU,UAAU;wBACzD,IAAI,SAAS;4BACX,OAAO;gCACL,SAAS;gCACT,OAAO;gCACP,WAAW;gCACX,OAAOA;gCACP,SAAS,QAAQ,OAAA,CAAQ;gCACzB,QAAQ;4BACV;wBACF;oBACF;oBAEA,MAAM,YAAY,SAAS;oBAC3B,MAAM,MAAM,KAAK,GAAA,CAAI;oBAErB,MAAM,gBAAgB,KAAK,KAAA,CAAM,MAAM,UAAU;oBACjD,MAAM,aAAa;wBAAC;wBAAY,aAAa;qBAAA,CAAE,IAAA,CAAK,GAAG;oBACvD,MAAM,iBAAiB,gBAAgB;oBACvC,MAAM,cAAc;wBAAC;wBAAY,cAAc;qBAAA,CAAE,IAAA,CAAK,GAAG;oBACzD,MAAM,cAAc,OAAO,KAAK,GAAA,CAAI,GAAG,IAAI,IAAI;oBAE/C,MAAM,MAAM,IAAI,cAAA,CAAe,GAAA,CAAI,CAAC,gBAAA,CAAmB;4BACrD,OAAO,cAAc,KAAA;4BACrB,SAAS,SACP,eACA,QAAQ,WAAA,CAAY,aAAA,CAAc,KAAA,EAClC;gCAAC;gCAAY,WAAW;6BAAA,EACxB;gCAAC;gCAAQ;gCAAK;gCAAgB;gCAAW,WAAW;6BAAA;wBAGxD,CAAA,CAAE;oBAEF,MAAM,sBAAuB,MAAM,iBAAkB;oBACrD,MAAM,CAAC,SAAS,UAAU,OAAO,CAAA,GAAI,MAAM,QAAQ,GAAA,CAAI,IAAI,GAAA,CAAI,CAAC,IAAM,EAAE,OAAO,CAAC;oBAIhF,IAAI,SAAS;wBACX,QAAQ,IAAA,CAAK,WAAW,YAAY,QAAA,CAAS,CAAC;oBAChD;oBAEA,MAAM,qBAAqB,SAAS,MAAA,CAAO,CAAC,WAAmB,WAAW,UAAU;wBAClF,IAAI,cAAc;wBAClB,IAAI,QAAQ,GAAG;4BACb,cAAc,OAAO,QAAA,CAAS,SAAS;wBACzC;wBAEA,OAAO,YAAY;oBACrB,GAAG,CAAC;oBAEJ,MAAM,oBAAoB,QAAQ,MAAA,CAAO,CAAC,WAAmB,WAAW,UAAU;wBAChF,IAAI,cAAc;wBAClB,IAAI,QAAQ,GAAG;4BACb,cAAc,OAAO,QAAA,CAAS,SAAS;wBACzC;wBAEA,OAAO,YAAY;oBACrB,GAAG,CAAC;oBAEJ,MAAM,sBAAsB,KAAK,IAAA,CAAK,qBAAA,CAAsB,IAAI,mBAAA,CAAoB;oBAEpF,MAAM,aAAa,sBAAsB;oBAEzC,MAAM,YAAY,SAAS;oBAK3B,eAAe,OAAO;wBACpB,MAAM,MAAM,MAAM,QAAQ,GAAA,CAAI,IAAI,GAAA,CAAI,CAAC,IAAM,EAAE,OAAO,CAAC;wBAEvD,MAAM,gBAAgB,CAAC;+BAAG,IAAI,IAC5B,IACG,OAAA,CAAQ,CAAC,CAACE,QAAO,CAAA,GAAMA,QAAO,EAC9B,MAAA,CAAO,CAAC,KAAe,MAAM,UAAU;gCACtC,IAAI,QAAQ,MAAM,GAAG;oCACnB,IAAI,IAAA,CAAK,IAAI;gCACf;gCACA,OAAO;4BACT,GAAG,CAAC,CAAC,GACP,MAAA,CAAO,CAAC;yBAAA;wBAEV,KAAA,MAAW,MAAM,IAAK;4BACpB,MAAM,CAACA,UAAS,WAAW,QAAQ,CAAA,GAAI,MAAM,GAAG,OAAA;4BAChD,MAAM,QAAQA,SAAQ,MAAA,CAAO,CAAC,KAAe,WAAW,UAAU;gCAChE,IAAI,QAAQ,MAAM,GAAG;oCACnB,IAAI,IAAA,CAAK,SAAS;gCACpB;gCACA,OAAO;4BACT,GAAG,CAAC,CAAC;4BAEL,MAAM,eAAeA,SAAQ,MAAA,CAAO,CAAC,WAAmB,WAAW,UAAU;gCAC3E,IAAI,cAAc;gCAClB,IAAI,QAAQ,GAAG;oCACb,cAAc,OAAO,QAAA,CAAS,SAAS;gCACzC;gCAEA,OAAO,YAAY;4BACrB,GAAG,CAAC;4BAKJ,IAAI,gBAAgB,QAAQ;gCAC1B;4BACF;4BACA,MAAM,OAAO,cAAc,MAAA,CAAO,CAAC,KAAO,CAAC,MAAM,QAAA,CAAS,EAAE,CAAC;4BAI7D,IAAI,KAAK,MAAA,KAAW,GAAG;gCACrB;4BACF;4BAEA,KAAA,MAAWD,cAAa,KAAM;gCAC5B,MAAM,GAAG,KAAA,CAAM,IAAA,CAAK,YAAY;oCAAE,CAACA,UAAS,CAAA,EAAG;gCAAY,CAAC;4BAC9D;wBACF;oBACF;oBAGA,MAAM,QAAA,CAAS,gBAAgB,CAAA,IAAK;oBACpC,IAAI,IAAI,KAAA,IAAS,CAAC,SAAS;wBACzB,IAAI,KAAA,CAAM,UAAA,CAAW,YAAY,KAAK;oBACxC;oBACA,OAAO;wBACL,SAAS,QAAQ,OAAO;wBACxB,OAAO;wBACP,WAAW,KAAK,GAAA,CAAI,GAAG,SAAS;wBAChC;wBACA,SAAS,KAAK;oBAChB;gBACF;gBACA,MAAM,cAAa,GAAA,EAAyB,UAAA,EAAoB;oBAC9D,MAAM,MAAM,KAAK,GAAA,CAAI;oBAErB,MAAM,gBAAgB,KAAK,KAAA,CAAM,MAAM,UAAU;oBACjD,MAAM,aAAa;wBAAC;wBAAY,aAAa;qBAAA,CAAE,IAAA,CAAK,GAAG;oBACvD,MAAM,iBAAiB,gBAAgB;oBACvC,MAAM,cAAc;wBAAC;wBAAY,cAAc;qBAAA,CAAE,IAAA,CAAK,GAAG;oBAEzD,MAAM,MAAM,IAAI,cAAA,CAAe,GAAA,CAAI,CAAC,gBAAA,CAAmB;4BACrD,OAAO,cAAc,KAAA;4BACrB,SAAS,SACP,eACA,QAAQ,WAAA,CAAY,aAAA,CAAc,YAAA,EAClC;gCAAC;gCAAY,WAAW;6BAAA,EACxB;gCAAC;gCAAK,UAAU;6BAAA;wBAGpB,CAAA,CAAE;oBAEF,MAAM,aAAa,MAAM,QAAQ,GAAA,CAAI,IAAI,GAAA,CAAI,CAAC,IAAM,EAAE,OAAO,CAAC;oBAC9D,OAAO;wBACL,WAAW,KAAK,GAAA,CAAI,GAAG,SAAS,UAAU;wBAC1C,OAAA,CAAQ,gBAAgB,CAAA,IAAK;oBAC/B;gBACF;gBACA,MAAM,aAAY,GAAA,EAAyB,UAAA,EAAoB;oBAC7D,MAAM,UAAU;wBAAC;wBAAY,GAAG;qBAAA,CAAE,IAAA,CAAK,GAAG;oBAC1C,IAAI,IAAI,KAAA,EAAO;wBACb,IAAI,KAAA,CAAM,GAAA,CAAI,UAAU;oBAC1B;oBAGA,MAAM,QAAQ,GAAA,CAAI,IAAI,cAAA,CAAe,GAAA,CAAI,CAAC,kBAAkB;wBAC1D,SACE,eACA,cACA;4BAAC,OAAO;yBAAA,EACR;4BAAC,IAAI;yBAAA;oBAET,CAAC,CAAC;gBACJ;YACF,CAAA;IACF;AACF;;ACraO,IAAM,kBAAN,cAA8B,UAAyB;IAAA;;GAAA,GAK5D,YAAY,MAAA,CAA+B;QACzC,KAAA,CAAM;YACJ,QAAQ,OAAO,MAAA;YACf,SAAS,OAAO,OAAA;YAChB,SAAS,OAAO,OAAA;YAChB,WAAW,OAAO,SAAA;YAClB,KAAK;gBACH,OAAO,OAAO,KAAA;YAChB;YACA,gBAAgB,OAAO,cAAA;YACvB,kBAAkB,OAAO,gBAAA;YACzB,mBAAmB,OAAO,iBAAA;QAC5B,CAAC;IACH;IAAA;;;;;;;;;;;;;;;;;GAAA,GAoBA,OAAO,YAIL,MAAA,EAIA,MAAA,EAC0B;QAC1B,MAAM,iBAAiB,GAAG,MAAM;QAChC,OAAO,IAAA,CAAO;gBACZ,MAAM,OAAM,GAAA,EAAoB,UAAA,EAAoB,IAAA,EAAe;oBACjE,MAAM,SAAS,KAAK,KAAA,CAAM,KAAK,GAAA,CAAI,IAAI,cAAc;oBACrD,MAAM,MAAM;wBAAC;wBAAY,MAAM;qBAAA,CAAE,IAAA,CAAK,GAAG;oBACzC,IAAI,IAAI,KAAA,EAAO;wBACb,MAAM,EAAE,OAAA,EAAS,OAAAE,MAAAA,CAAM,CAAA,GAAI,IAAI,KAAA,CAAM,SAAA,CAAU,UAAU;wBACzD,IAAI,SAAS;4BACX,OAAO;gCACL,SAAS;gCACT,OAAO;gCACP,WAAW;gCACX,OAAOA;gCACP,SAAS,QAAQ,OAAA,CAAQ;gCACzB,QAAQ;4BACV;wBACF;oBACF;oBAEA,MAAM,cAAc,OAAO,KAAK,GAAA,CAAI,GAAG,IAAI,IAAI;oBAE/C,MAAM,wBAAwB,MAAM,SAClC,KACA,QAAQ,YAAA,CAAa,WAAA,CAAY,KAAA,EACjC;wBAAC,GAAG;qBAAA,EACJ;wBAAC;wBAAgB,WAAW;qBAAA;oBAG9B,MAAM,UAAU,yBAAyB;oBAEzC,MAAM,kBAAkB,KAAK,GAAA,CAAI,GAAG,SAAS,qBAAqB;oBAElE,MAAM,QAAA,CAAS,SAAS,CAAA,IAAK;oBAC7B,IAAI,IAAI,KAAA,IAAS,CAAC,SAAS;wBACzB,IAAI,KAAA,CAAM,UAAA,CAAW,YAAY,KAAK;oBACxC;oBAEA,OAAO;wBACL;wBACA,OAAO;wBACP,WAAW;wBACX;wBACA,SAAS,QAAQ,OAAA,CAAQ;oBAC3B;gBACF;gBACA,MAAM,cAAa,GAAA,EAAoB,UAAA,EAAoB;oBACzD,MAAM,SAAS,KAAK,KAAA,CAAM,KAAK,GAAA,CAAI,IAAI,cAAc;oBACrD,MAAM,MAAM;wBAAC;wBAAY,MAAM;qBAAA,CAAE,IAAA,CAAK,GAAG;oBAEzC,MAAM,aAAa,MAAM,SACvB,KACA,QAAQ,YAAA,CAAa,WAAA,CAAY,YAAA,EACjC;wBAAC,GAAG;qBAAA,EACJ;wBAAC,IAAI;qBAAA;oBAGP,OAAO;wBACL,WAAW,KAAK,GAAA,CAAI,GAAG,SAAS,UAAU;wBAC1C,OAAA,CAAQ,SAAS,CAAA,IAAK;oBACxB;gBACF;gBACA,MAAM,aAAY,GAAA,EAAoB,UAAA,EAAoB;oBACxD,MAAM,UAAU;wBAAC;wBAAY,GAAG;qBAAA,CAAE,IAAA,CAAK,GAAG;oBAC1C,IAAI,IAAI,KAAA,EAAO;wBACb,IAAI,KAAA,CAAM,GAAA,CAAI,UAAU;oBAC1B;oBAEA,MAAM,SACJ,KACA,cACA;wBAAC,OAAO;qBAAA,EACR;wBAAC,IAAI;qBAAA;gBAET;YACF,CAAA;IACF;IAAA;;;;;;;;;;;;;;;GAAA,GAkBA,OAAO,cAIL,MAAA,EAIA,MAAA,EAC0B;QAC1B,MAAM,aAAa,GAAG,MAAM;QAC5B,OAAO,IAAA,CAAO;gBACZ,MAAM,OAAM,GAAA,EAAoB,UAAA,EAAoB,IAAA,EAAe;oBACjE,MAAM,MAAM,KAAK,GAAA,CAAI;oBAErB,MAAM,gBAAgB,KAAK,KAAA,CAAM,MAAM,UAAU;oBACjD,MAAM,aAAa;wBAAC;wBAAY,aAAa;qBAAA,CAAE,IAAA,CAAK,GAAG;oBACvD,MAAM,iBAAiB,gBAAgB;oBACvC,MAAM,cAAc;wBAAC;wBAAY,cAAc;qBAAA,CAAE,IAAA,CAAK,GAAG;oBAEzD,IAAI,IAAI,KAAA,EAAO;wBACb,MAAM,EAAE,OAAA,EAAS,OAAAA,MAAAA,CAAM,CAAA,GAAI,IAAI,KAAA,CAAM,SAAA,CAAU,UAAU;wBACzD,IAAI,SAAS;4BACX,OAAO;gCACL,SAAS;gCACT,OAAO;gCACP,WAAW;gCACX,OAAOA;gCACP,SAAS,QAAQ,OAAA,CAAQ;gCACzB,QAAQ;4BACV;wBACF;oBACF;oBAEA,MAAM,cAAc,OAAO,KAAK,GAAA,CAAI,GAAG,IAAI,IAAI;oBAE/C,MAAM,kBAAkB,MAAM,SAC5B,KACA,QAAQ,YAAA,CAAa,aAAA,CAAc,KAAA,EACnC;wBAAC;wBAAY,WAAW;qBAAA,EACxB;wBAAC;wBAAQ;wBAAK;wBAAY,WAAW;qBAAA;oBAGvC,MAAM,UAAU,mBAAmB;oBAEnC,MAAM,QAAA,CAAS,gBAAgB,CAAA,IAAK;oBACpC,IAAI,IAAI,KAAA,IAAS,CAAC,SAAS;wBACzB,IAAI,KAAA,CAAM,UAAA,CAAW,YAAY,KAAK;oBACxC;oBACA,OAAO;wBACL;wBACA,OAAO;wBACP,WAAW,KAAK,GAAA,CAAI,GAAG,eAAe;wBACtC;wBACA,SAAS,QAAQ,OAAA,CAAQ;oBAC3B;gBACF;gBACA,MAAM,cAAa,GAAA,EAAoB,UAAA,EAAoB;oBACzD,MAAM,MAAM,KAAK,GAAA,CAAI;oBACrB,MAAM,gBAAgB,KAAK,KAAA,CAAM,MAAM,UAAU;oBACjD,MAAM,aAAa;wBAAC;wBAAY,aAAa;qBAAA,CAAE,IAAA,CAAK,GAAG;oBACvD,MAAM,iBAAiB,gBAAgB;oBACvC,MAAM,cAAc;wBAAC;wBAAY,cAAc;qBAAA,CAAE,IAAA,CAAK,GAAG;oBAEzD,MAAM,aAAa,MAAM,SACvB,KACA,QAAQ,YAAA,CAAa,aAAA,CAAc,YAAA,EACnC;wBAAC;wBAAY,WAAW;qBAAA,EACxB;wBAAC;wBAAK,UAAU;qBAAA;oBAGlB,OAAO;wBACL,WAAW,KAAK,GAAA,CAAI,GAAG,SAAS,UAAU;wBAC1C,OAAA,CAAQ,gBAAgB,CAAA,IAAK;oBAC/B;gBACF;gBACA,MAAM,aAAY,GAAA,EAAoB,UAAA,EAAoB;oBACxD,MAAM,UAAU;wBAAC;wBAAY,GAAG;qBAAA,CAAE,IAAA,CAAK,GAAG;oBAC1C,IAAI,IAAI,KAAA,EAAO;wBACb,IAAI,KAAA,CAAM,GAAA,CAAI,UAAU;oBAC1B;oBAEA,MAAM,SACJ,KACA,cACA;wBAAC,OAAO;qBAAA,EACR;wBAAC,IAAI;qBAAA;gBAET;YACF,CAAA;IACF;IAAA;;;;;;;;;;;;GAAA,GAeA,OAAO,YAML,UAAA,EAIA,QAAA,EAMA,SAAA,EAC0B;QAC1B,MAAM,mBAAmB,GAAG,QAAQ;QACpC,OAAO,IAAA,CAAO;gBACZ,MAAM,OAAM,GAAA,EAAoB,UAAA,EAAoB,IAAA,EAAe;oBACjE,IAAI,IAAI,KAAA,EAAO;wBACb,MAAM,EAAE,OAAA,EAAS,OAAAA,MAAAA,CAAM,CAAA,GAAI,IAAI,KAAA,CAAM,SAAA,CAAU,UAAU;wBACzD,IAAI,SAAS;4BACX,OAAO;gCACL,SAAS;gCACT,OAAO;gCACP,WAAW;gCACX,OAAOA;gCACP,SAAS,QAAQ,OAAA,CAAQ;gCACzB,QAAQ;4BACV;wBACF;oBACF;oBAEA,MAAM,MAAM,KAAK,GAAA,CAAI;oBAErB,MAAM,cAAc,OAAO,KAAK,GAAA,CAAI,GAAG,IAAI,IAAI;oBAE/C,MAAM,CAAC,WAAW,KAAK,CAAA,GAAI,MAAM,SAC/B,KACA,QAAQ,YAAA,CAAa,WAAA,CAAY,KAAA,EACjC;wBAAC,UAAU;qBAAA,EACX;wBAAC;wBAAW;wBAAkB;wBAAY;wBAAK,WAAW;qBAAA;oBAG5D,MAAM,UAAU,aAAa;oBAC7B,IAAI,IAAI,KAAA,IAAS,CAAC,SAAS;wBACzB,IAAI,KAAA,CAAM,UAAA,CAAW,YAAY,KAAK;oBACxC;oBAEA,OAAO;wBACL;wBACA,OAAO;wBACP;wBACA;wBACA,SAAS,QAAQ,OAAA,CAAQ;oBAC3B;gBACF;gBACA,MAAM,cAAa,GAAA,EAAoB,UAAA,EAAoB;oBAEzD,MAAM,CAAC,iBAAiB,UAAU,CAAA,GAAI,MAAM,SAC1C,KACA,QAAQ,YAAA,CAAa,WAAA,CAAY,YAAA,EACjC;wBAAC,UAAU;qBAAA,EACX;wBAAC,SAAS;qBAAA;oBAGZ,MAAM,gBAAgB,KAAK,GAAA,CAAI,IAAI;oBACnC,MAAM,sBAAsB,aAAa;oBAEzC,OAAO;wBACL,WAAW;wBACX,OAAO,eAAe,gCAAgC,gBAAgB;oBACxE;gBACF;gBACA,MAAM,aAAY,GAAA,EAAoB,UAAA,EAAoB;oBACxD,MAAM,UAAU;oBAChB,IAAI,IAAI,KAAA,EAAO;wBACb,IAAI,KAAA,CAAM,GAAA,CAAI,UAAU;oBAC1B;oBAEA,MAAM,SACJ,KACA,cACA;wBAAC,OAAO;qBAAA,EACR;wBAAC,IAAI;qBAAA;gBAET;YACF,CAAA;IACF;IAAA;;;;;;;;;;;;;;;;;;;;;;;GAAA,GAyBA,OAAO,kBAIL,MAAA,EAIA,MAAA,EAC0B;QAC1B,MAAM,iBAAiB,GAAG,MAAM;QAEhC,OAAO,IAAA,CAAO;gBACZ,MAAM,OAAM,GAAA,EAAoB,UAAA,EAAoB,IAAA,EAAe;oBACjE,IAAI,CAAC,IAAI,KAAA,EAAO;wBACd,MAAM,IAAI,MAAM,iCAAiC;oBACnD;oBACA,MAAM,SAAS,KAAK,KAAA,CAAM,KAAK,GAAA,CAAI,IAAI,cAAc;oBACrD,MAAM,MAAM;wBAAC;wBAAY,MAAM;qBAAA,CAAE,IAAA,CAAK,GAAG;oBACzC,MAAM,QAAA,CAAS,SAAS,CAAA,IAAK;oBAC7B,MAAM,cAAc,OAAO,KAAK,GAAA,CAAI,GAAG,IAAI,IAAI;oBAE/C,MAAM,MAAM,OAAO,IAAI,KAAA,CAAM,GAAA,CAAI,GAAG,MAAM;oBAC1C,IAAI,KAAK;wBACP,MAAM,0BAA0B,IAAI,KAAA,CAAM,IAAA,CAAK,GAAG;wBAClD,MAAM,UAAU,0BAA0B;wBAE1C,MAAM,UAAU,UACZ,SACA,KACA,QAAQ,YAAA,CAAa,iBAAA,CAAkB,KAAA,EACvC;4BAAC,GAAG;yBAAA,EACJ;4BAAC;4BAAgB,WAAW;yBAAA,IAE5B,QAAQ,OAAA,CAAQ;wBAEpB,OAAO;4BACL;4BACA,OAAO;4BACP,WAAW,SAAS;4BACpB;4BACA;wBACF;oBACF;oBAEA,MAAM,wBAAwB,MAAM,SAClC,KACA,QAAQ,YAAA,CAAa,iBAAA,CAAkB,KAAA,EACvC;wBAAC,GAAG;qBAAA,EACJ;wBAAC;wBAAgB,WAAW;qBAAA;oBAE9B,IAAI,KAAA,CAAM,GAAA,CAAI,KAAK,qBAAqB;oBACxC,MAAM,YAAY,SAAS;oBAE3B,OAAO;wBACL,SAAS,aAAa;wBACtB,OAAO;wBACP;wBACA;wBACA,SAAS,QAAQ,OAAA,CAAQ;oBAC3B;gBACF;gBACA,MAAM,cAAa,GAAA,EAAoB,UAAA,EAAoB;oBACzD,IAAI,CAAC,IAAI,KAAA,EAAO;wBACd,MAAM,IAAI,MAAM,iCAAiC;oBACnD;oBAEA,MAAM,SAAS,KAAK,KAAA,CAAM,KAAK,GAAA,CAAI,IAAI,cAAc;oBACrD,MAAM,MAAM;wBAAC;wBAAY,MAAM;qBAAA,CAAE,IAAA,CAAK,GAAG;oBAEzC,MAAM,MAAM,OAAO,IAAI,KAAA,CAAM,GAAA,CAAI,GAAG,MAAM;oBAC1C,IAAI,KAAK;wBACP,MAAM,mBAAmB,IAAI,KAAA,CAAM,GAAA,CAAI,GAAG,KAAK;wBAC/C,OAAO;4BACL,WAAW,KAAK,GAAA,CAAI,GAAG,SAAS,gBAAgB;4BAChD,OAAA,CAAQ,SAAS,CAAA,IAAK;wBACxB;oBACF;oBAEA,MAAM,aAAa,MAAM,SACvB,KACA,QAAQ,YAAA,CAAa,iBAAA,CAAkB,YAAA,EACvC;wBAAC,GAAG;qBAAA,EACJ;wBAAC,IAAI;qBAAA;oBAEP,OAAO;wBACL,WAAW,KAAK,GAAA,CAAI,GAAG,SAAS,UAAU;wBAC1C,OAAA,CAAQ,SAAS,CAAA,IAAK;oBACxB;gBACF;gBACA,MAAM,aAAY,GAAA,EAAoB,UAAA,EAAoB;oBAExD,IAAI,CAAC,IAAI,KAAA,EAAO;wBACd,MAAM,IAAI,MAAM,iCAAiC;oBACnD;oBAEA,MAAM,SAAS,KAAK,KAAA,CAAM,KAAK,GAAA,CAAI,IAAI,cAAc;oBACrD,MAAM,MAAM;wBAAC;wBAAY,MAAM;qBAAA,CAAE,IAAA,CAAK,GAAG;oBACzC,IAAI,KAAA,CAAM,GAAA,CAAI,GAAG;oBAEjB,MAAM,UAAU;wBAAC;wBAAY,GAAG;qBAAA,CAAE,IAAA,CAAK,GAAG;oBAE1C,MAAM,SACJ,KACA,cACA;wBAAC,OAAO;qBAAA,EACR;wBAAC,IAAI;qBAAA;gBAET;YACF,CAAA;IACF;AACF", "ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16]}}, {"offset": {"line": 8023, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/uncrypto/dist/crypto.web.mjs"], "sourcesContent": ["const webCrypto = globalThis.crypto;\nconst subtle = webCrypto.subtle;\nconst randomUUID = () => {\n  return webCrypto.randomUUID();\n};\nconst getRandomValues = (array) => {\n  return webCrypto.getRandomValues(array);\n};\nconst _crypto = {\n  randomUUID,\n  getRandomValues,\n  subtle\n};\n\nexport { _crypto as default, getRandomValues, randomUUID, subtle };\n"], "names": [], "mappings": ";;;;;;AAAA,MAAM,YAAY,WAAW,MAAM;AACnC,MAAM,SAAS,UAAU,MAAM;AAC/B,MAAM,aAAa;IACjB,OAAO,UAAU,UAAU;AAC7B;AACA,MAAM,kBAAkB,CAAC;IACvB,OAAO,UAAU,eAAe,CAAC;AACnC;AACA,MAAM,UAAU;IACd;IACA;IACA;AACF", "ignoreList": [0]}}]}