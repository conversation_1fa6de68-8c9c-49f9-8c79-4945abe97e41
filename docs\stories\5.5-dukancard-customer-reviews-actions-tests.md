---
Status: Draft
Story: |
  As a developer, I need to implement comprehensive unit tests for the `actions.ts` file within the `dukancard` project's customer reviews module. This file contains the server action `fetchCustomerReviews`, which is responsible for fetching review data from the `reviewsService`. The tests should ensure this action correctly interacts with the `reviewsService`, handles various data scenarios (including pagination and sorting), and gracefully manages service-level errors.

Acceptance Criteria:
- **`fetchCustomerReviews` Functionality:**
    - Given a valid `userId`, `page`, `limit`, and `sortBy`, when `fetchCustomerReviews` is called, then `reviewsService.fetchReviews` is invoked with the correct parameters.
    - Given `reviewsService.fetchReviews` returns data, then `fetchCustomerReviews` returns a success object with the data.
    - Given `reviewsService.fetchReviews` throws an error, then `fetchCustomerReviews` returns a failure object with the error message.

Tasks:
1. Create a new test file: `C:\web-app\dukancard\__tests__\app\(dashboard)\dashboard\customer\reviews\actions.test.ts`.
2. Set up a testing environment that can mock `reviewsService`.
3. Write unit tests for `fetchCustomerReviews` covering:
    - Successful data retrieval.
    - Error handling when `reviewsService.fetchReviews` throws an error.
    - Correct parameter passing (userId, page, limit, sortBy).

File: `C:\web-app\dukancard\app\(dashboard)\dashboard\customer\reviews\actions.ts`
Platform: dukancard
---