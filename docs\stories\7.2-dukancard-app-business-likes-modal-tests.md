---
Status: Draft
Story: |
  As a developer, I need to implement comprehensive unit and integration tests for the `BusinessLikesModal` component in the `dukancard-app` project. This modal is responsible for displaying a business's received and given likes, allowing users to switch between these views, search within the "Given" likes, and handle loading, empty, and error states. The tests should ensure correct UI rendering, state management for tab switching and search, and proper interaction with its child list components.

Acceptance Criteria:
- **Initial Rendering:**
    - Given `visible` is `true`, when the modal renders, then it displays the header with title "Likes", a close button, and two tabs: "Received" and "Given".
    - The "Received" tab is active by default.
    - `BusinessLikesList` is rendered with `businessId` and `searchTerm` (empty initially).
    - `BusinessLikesGivenList` is not rendered.
- **Modal Visibility:**
    - Given `visible` is `false`, the modal is not rendered.
    - When the close button is pressed, `onClose` is called.
- **Tab Switching (UI & State):**
    - When the "Given" tab is pressed, `activeTab` state changes to 'given'.
    - `BusinessLikesGivenList` is rendered with `searchTerm` (empty initially).
    - `BusinessLikesList` is not rendered.
    - When the "Received" tab is pressed, `activeTab` state changes to 'received'.
    - `BusinessLikesList` is rendered with `businessId` and `searchTerm` (empty initially).
    - `BusinessLikesGivenList` is not rendered.
- **Search Functionality:**
    - The search input is rendered.
    - When text is entered into the search input, `searchTerm` state updates.
    - When the search button is pressed or `onSubmitEditing` is triggered, `activeSearchTerm` updates to `searchTerm`.
    - The `BusinessLikesList` or `BusinessLikesGivenList` receives the `activeSearchTerm` as its `searchTerm` prop.
- **Child Component Interaction:**
    - Verify that `BusinessLikesList` receives the correct `businessId` and `searchTerm` props when the "Received" tab is active.
    - Verify that `BusinessLikesGivenList` receives the correct `searchTerm` prop when the "Given" tab is active.

Tasks:
1. Update the existing test file: `C:\web-app\dukancard-app\__tests__\src\components\modals\business\BusinessLikesModal.test.tsx`.
2. Remove mocks for `BusinessLikesList` and `BusinessLikesGivenList` to allow for integration testing of their rendering within the modal.
3. Write integration tests to verify:
    - Correct rendering of `BusinessLikesList` and `BusinessLikesGivenList` based on `activeTab`.
    - Proper passing of `businessId` and `searchTerm` props to the list components.
    - State updates for `searchTerm` and `activeSearchTerm` upon user input and search actions.
    - The `onClose` prop is correctly called when the close button is pressed.
4. Ensure the modal's visibility is controlled by the `visible` prop.

File: `C:\web-app\dukancard-app\src\components\modals\business\BusinessLikesModal.tsx`
Platform: dukancard-app
---