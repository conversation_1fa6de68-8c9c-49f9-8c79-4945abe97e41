---
Status: Draft
Story: |
  As a developer, I need to implement comprehensive unit and integration tests for the `BusinessSubscriptionsPageClient` component in the `dukancard` project. This component is responsible for rendering the UI for business subscriptions, handling tab switching between "Subscribers" and "Following", managing search functionality within "Following", and handling pagination for both tabs. The tests should ensure correct UI rendering, state management, and interaction with its child components and the Next.js router.

Acceptance Criteria:
- **Initial Rendering:**
    - Given `initialSubscribers`, `subscribersCount`, `initialFollowing`, `followingCount`, `searchTerm`, and `activeTab` props, when the component renders, then it correctly displays the header, tab buttons with accurate counts, and the content for the `activeTab`.
    - And the `SubscriptionSearch` component is rendered only when `activeTab` is 'following'.
    - And the `SubscriptionPagination` component is rendered only when `totalPages` > 1.
- **Tab Switching (UI & State):**
    - Given the "Subscribers" tab is active, when the "Following" tab button is clicked, then `activeTab` state updates to 'following', `isLoading` state becomes `true`, and the URL is updated via `router.push` with `tab=following` and `page=1`.
    - Given the "Following" tab is active, when the "Subscribers" tab button is clicked, then `activeTab` state updates to 'subscribers', `isLoading` state becomes `true`, and the URL is updated via `router.push` with `tab=subscribers` and `page=1`.
    - And the `isLoading` state resets to `false` once the `useEffect` hook detects changes in `initialSubscribers` or `initialFollowing`.
- **Search Functionality (UI & State):**
    - Given the "Following" tab is active, when `onSearch` is triggered on `SubscriptionSearch` with a new search term, then `isLoading` state becomes `true`, and the URL is updated via `router.push` with the new `search` term and `page=1`.
    - Given the "Following" tab is active and a search term is present, when the search term is cleared (empty string), then `isLoading` state becomes `true`, and the URL is updated via `router.push` with `search` parameter removed and `page=1`.
    - And the search results count display is shown correctly when `activeTab` is 'following', `searchTerm` is present, and `isLoading` is `false`.
- **Pagination (UI & State):**
    - Given `totalPages` > 1, when `onPageChange` is triggered on `SubscriptionPagination` with a new page number, then `isLoading` state becomes `true`, and the URL is updated via `router.push` with the new `page` parameter.
    - And the `isLoading` state resets to `false` once the `useEffect` hook detects changes in `initialSubscribers` or `initialFollowing`.
- **Loading States:**
    - Given `isLoading` is `true`, then `SubscriptionListSkeleton` is rendered.
    - Given `isLoading` is `false`, then `SubscriptionList` is rendered with `currentData`.
- **Child Component Interaction:**
    - Verify that `SubscriptionList` receives `initialSubscriptions` when `activeTab` is 'subscribers'.
    - Verify that `SubscriptionList` receives `initialFollowing` when `activeTab` is 'following'.
    - Verify `showUnsubscribe` prop is `true` for `SubscriptionList` when `activeTab` is 'following'.
    - Verify `showDiscoverButton` prop is `true` for `SubscriptionList` when `activeTab` is 'following'.

Tasks:
1. Create a new test file: `C:\web-app\dukancard\__tests__\app\(dashboard)\dashboard\business\subscriptions\components\BusinessSubscriptionsPageClient.test.tsx`.
2. Set up a testing environment that can mock `next/navigation` hooks (`useRouter`, `useSearchParams`).
3. Write unit tests for the `BusinessSubscriptionsPageClient` component covering all acceptance criteria.
4. Simulate user interactions (button clicks, search input changes, pagination clicks).
5. Assert on component state changes, `router.push` calls with correct URL parameters, and conditional rendering of child components.
6. Ensure `isLoading` state transitions are correctly handled.

File: `C:\web-app\dukancard\app\(dashboard)\dashboard\business\subscriptions\components\BusinessSubscriptionsPageClient.tsx`
Platform: dukancard
---