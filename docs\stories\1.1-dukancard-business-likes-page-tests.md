---
Status: Draft
Story: |
  As a developer, I need to implement comprehensive unit and integration tests for the `BusinessLikesPage` server component in the `dukancard` project. This page is responsible for fetching and displaying "Likes Received" and "My Likes" data for a business user, including handling authentication, pagination, search, and tab switching. The tests should ensure the page behaves correctly under various conditions, including successful data loads, empty states, error conditions, and proper handling of URL parameters.

Acceptance Criteria:
- **Authentication:**
    - Given an unauthenticated user, when accessing `/dashboard/business/likes`, then the user is redirected to `/login`.
    - Given an authenticated business user, when accessing `/dashboard/business/likes`, then the page loads successfully.
- **Initial Data Fetching & Rendering:**
    - Given a business user with existing "Likes Received" and "My Likes" data, when the page loads, then `fetchBusinessLikesReceived` and `fetchBusinessMyLikes` are called with correct initial parameters (user ID, page 1, limit 10, empty search term).
    - And the `BusinessLikesPageClient` component receives the correct `initialLikesReceived`, `likesReceivedCount`, `likesReceivedCurrentPage`, `initialMyLikes`, `myLikesCount`, `myLikesCurrentPage`, `searchTerm`, and `activeTab` props.
    - And the page correctly displays the "Likes Received" tab content by default.
    - And the counts for "Likes Received" and "My Likes" are accurately displayed on their respective tab buttons.
- **Tab Switching:**
    - Given the page is loaded on the "Likes Received" tab, when the "My Likes" tab is clicked, then the `BusinessLikesPageClient` receives updated props reflecting the "My Likes" tab, and the URL `tab` parameter is set to `my-likes`.
    - And the search term is cleared and pagination is reset to page 1 when switching tabs.
    - Given the page is loaded on the "My Likes" tab, when the "Likes Received" tab is clicked, then the `BusinessLikesPageClient` receives updated props reflecting the "Likes Received" tab, and the URL `tab` parameter is removed.
    - And the search term is cleared and pagination is reset to page 1 when switching tabs.
- **Pagination:**
    - Given the "Likes Received" tab is active and there are multiple pages of data, when a user navigates to page 2 (e.g., via URL parameter or pagination control), then `fetchBusinessLikesReceived` is called with `page: 2`.
    - And the `BusinessLikesPageClient` receives the correct `likesReceivedCurrentPage` prop.
    - Given the "My Likes" tab is active and there are multiple pages of data, when a user navigates to page 3, then `fetchBusinessMyLikes` is called with `page: 3`.
    - And the `BusinessLikesPageClient` receives the correct `myLikesCurrentPage` prop.
- **Search Functionality (My Likes Tab Only):**
    - Given the "My Likes" tab is active, when a user enters a search term, then `fetchBusinessMyLikes` is called with the correct `searchTerm`.
    - And the `BusinessLikesPageClient` receives the updated `searchTerm` prop.
    - And pagination is reset to page 1 when a search term is applied.
    - Given the "My Likes" tab is active and a search term is applied, when the search term is cleared, then `fetchBusinessMyLikes` is called with an empty `searchTerm`.
    *   And the `BusinessLikesPageClient` receives an empty `searchTerm` prop.
    *   And pagination is reset to page 1 when the search term is cleared.
- **Loading States:**
    - Given the page is loading data, then the `LikeListSkeleton` is displayed within the `Suspense` fallback.
    - Given the page has finished loading, then the `LikeListSkeleton` is no longer displayed.
- **Error Handling:**
    - Given `fetchBusinessLikesReceived` or `fetchBusinessMyLikes` throws an error, then an `Alert` component with `variant="destructive"` is displayed, showing an appropriate error message.
    - And the page does not crash.
- **Edge Cases:**
    - Given no likes data exists for either tab, then the respective lists are empty and appropriate empty states are displayed (handled by client components).
    - Given `searchParams` are missing or invalid, then default values are used (page 1, empty search term, 'likes-received' tab).

Tasks:
1. Create a new test file: `C:\web-app\dukancard\__tests__\app\(dashboard)\dashboard\business\likes\page.test.tsx`.
2. Set up a testing environment that can mock Supabase server-side functions (`createClient`, `supabase.auth.getUser`, `supabase.from`).
3. Write unit tests for the `BusinessLikesPage` component covering all acceptance criteria.
4. Ensure tests use `render` from `@testing-library/react` or similar for component rendering.
5. Mock the `fetchBusinessLikesReceived` and `fetchBusinessMyLikes` functions to control data responses (success, empty, error).
6. Simulate `searchParams` changes to test pagination, search, and tab switching.
7. Verify redirects for unauthenticated users.
8. Verify correct data is passed to `BusinessLikesPageClient`.
9. Verify error message display on data fetching failures.
10. Ensure the `Suspense` fallback (skeleton) is shown during loading.

File: `C:\web-app\dukancard\app\(dashboard)\dashboard\business\likes\page.tsx`
Platform: dukancard
---