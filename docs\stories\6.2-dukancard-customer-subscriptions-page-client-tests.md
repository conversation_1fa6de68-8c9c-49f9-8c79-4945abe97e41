---
Status: Draft
Story: |
  As a developer, I need to implement comprehensive unit and integration tests for the `SubscriptionsPageClient` component in the `dukancard` project. This component is responsible for rendering the UI for customer subscriptions, managing search functionality, and handling pagination. The tests should ensure correct UI rendering, state management, and interaction with its child components and the Next.js router.

Acceptance Criteria:
- **Initial Rendering:**
    - Given `initialSubscriptions`, `totalCount`, `currentPage`, and `searchTerm` props, when the component renders, then it correctly displays the header, the total count of subscribed businesses, and the content for the subscriptions list.
    - And the `SubscriptionSearch` component is rendered with the correct `initialSearchTerm` and `placeholder`.
    - And the `Pagination` component is rendered only when `totalPages` > 1.
- **Search Functionality (UI & State):**
    - When `onSearch` is triggered on `SubscriptionSearch` with a new search term, then `isLoading` state becomes `true`, and the URL is updated via `router.push` with the new `search` term and `page=1`.
    - When the search term is cleared (empty string), then `isLoading` state becomes `true`, and the URL is updated via `router.push` with `search` parameter removed and `page=1`.
    - And the search results count display is shown correctly when `searchTerm` is present and `isLoading` is `false`.
- **Pagination (UI & State):**
    - Given `totalPages` > 1, when `onPageChange` is triggered on `Pagination` with a new page number, then `isLoading` state becomes `true`, and the URL is updated via `router.push` with the new `page` parameter.
    - And the `isLoading` state resets to `false` once the `useEffect` hook detects changes in `initialSubscriptions`.
- **Loading States:**
    - Given `isLoading` is `true`, then `SubscriptionListSkeleton` is rendered.
    - Given `isLoading` is `false`, then `SubscriptionListClient` is rendered with `initialSubscriptions`.
- **Child Component Interaction:**
    - Verify that `SubscriptionListClient` receives the correct `initialSubscriptions`, `totalCount`, and `currentPage` props.

Tasks:
1. Create a new test file: `C:\web-app\dukancard\__tests__\app\(dashboard)\dashboard\customer\subscriptions\components\SubscriptionsPageClient.test.tsx`.
2. Set up a testing environment that can mock `next/navigation` hooks (`useRouter`, `useSearchParams`).
3. Write unit tests for the `SubscriptionsPageClient` component covering all acceptance criteria.
4. Simulate user interactions (search input changes, pagination clicks).
5. Assert on component state changes, `router.push` calls with correct URL parameters, and conditional rendering of child components.
6. Ensure `isLoading` state transitions are correctly handled.

File: `C:\web-app\dukancard\app\(dashboard)\dashboard\customer\subscriptions\components\SubscriptionsPageClient.tsx`
Platform: dukancard
---