---
Status: Draft
Story: |
  As a developer, I need to implement comprehensive unit tests for the `actions.ts` file within the `dukancard` project's business reviews module. This file contains server actions (`fetchBusinessReviewsReceived` and `fetchMyReviews`) responsible for fetching review data from the `reviewsService`. The tests should ensure these actions correctly interact with the `reviewsService`, handle various data scenarios (including pagination and sorting), and gracefully manage service-level errors.

Acceptance Criteria:
- **`fetchBusinessReviewsReceived` Functionality:**
    - Given a valid `businessId`, `page`, `limit`, and `sortBy`, when `fetchBusinessReviewsReceived` is called, then `reviewsService.fetchBusinessReviewsReceived` is invoked with the correct parameters.
    - Given `reviewsService.fetchBusinessReviewsReceived` returns data, then `fetchBusinessReviewsReceived` returns a success object with the data.
    - Given `reviewsService.fetchBusinessReviewsReceived` throws an error, then `fetchBusinessReviewsReceived` returns a failure object with the error message.
- **`fetchMyReviews` Functionality:**
    - Given a valid `userId`, `page`, `limit`, and `sortBy`, when `fetchMyReviews` is called, then `reviewsService.fetchReviews` is invoked with the correct parameters.
    - Given `reviewsService.fetchReviews` returns data, then `fetchMyReviews` returns a success object with the data.
    - Given `reviewsService.fetchReviews` throws an error, then `fetchMyReviews` returns a failure object with the error message.

Tasks:
1. Create a new test file: `C:\web-app\dukancard\__tests__\app\(dashboard)\dashboard\business\reviews\actions.test.ts`.
2. Set up a testing environment that can mock `reviewsService`.
3. Write unit tests for `fetchBusinessReviewsReceived` covering:
    - Successful data retrieval.
    - Error handling when `reviewsService.fetchBusinessReviewsReceived` throws an error.
    - Correct parameter passing (businessId, page, limit, sortBy).
4. Write unit tests for `fetchMyReviews` covering:
    - Successful data retrieval.
    - Error handling when `reviewsService.fetchReviews` throws an error.
    - Correct parameter passing (userId, page, limit, sortBy).

File: `C:\web-app\dukancard\app\(dashboard)\dashboard\business\reviews\actions.ts`
Platform: dukancard
---