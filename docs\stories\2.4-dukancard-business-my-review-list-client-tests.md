---
Status: Draft
Story: |
  As a developer, I need to implement comprehensive unit and integration tests for the `BusinessMyReviewListClient` component in the `dukancard` project. This component is responsible for fetching, displaying, sorting, and paginating reviews written by the business user. It should handle various data states (loading, error, empty) and correctly render `ReviewCard` components, including handling review deletion and refreshing the list.

Acceptance Criteria:
- **Initial Data Fetching & Rendering:**
    - When the component mounts, `fetchMyReviews` is called with a placeholder user ID, default page (1), default limit (10), and default sort (`newest`).
    - Given `fetchMyReviews` returns data, then `ReviewCard` components are rendered for each review, with `onDeleteSuccess` prop correctly set.
    - Given `fetchMyReviews` returns an empty list, then the empty state message "No reviews written yet" is displayed.
- **Loading States:**
    - Given the component is fetching data, then `ReviewCardSkeleton` components are displayed.
    - Given the component has finished fetching data, then `ReviewCardSkeleton` components are no longer displayed.
- **Error Handling:**
    - Given `fetchMyReviews` throws an error, then an `Alert` component with `variant="destructive"` is displayed, showing an appropriate error message.
    - And the component does not crash.
- **Sorting Functionality:**
    - Given a user selects a new sort option from `ReviewSortDropdown`, then `sortBy` state updates, `fetchMyReviews` is called with the new sort option and `currentPage` reset to 1.
    - And the displayed reviews are re-rendered according to the new sort order.
- **Pagination Functionality:**
    - Given `pagination.totalPages` > 1, when a user clicks on a pagination link (e.g., next, previous, specific page number), then `handlePageChange` is called with the correct page number.
    - And `fetchMyReviews` is called with the new page number.
    - And the displayed reviews are updated accordingly.
    - And pagination controls (Previous, Next, page numbers, ellipses) are rendered correctly based on `currentPage` and `totalPages`.
    - And Previous/Next buttons are disabled when on the first/last page respectively.
- **Review Deletion:**
    - Given `handleDeleteSuccess` is called with a `reviewId`, then the review with that `reviewId` is removed from the displayed list.
    - If the deleted review was the last one on the current page and `currentPage` > 1, then the component navigates to the previous page.
    - Otherwise, the current page is refreshed by calling `fetchMyReviews`.

Tasks:
1. Create a new test file: `C:\web-app\dukancard\__tests__\app\(dashboard)\dashboard\business\reviews\components\BusinessMyReviewListClient.test.tsx`.
2. Set up a testing environment that can render React components and mock `fetchMyReviews`.
3. Write unit tests for the `BusinessMyReviewListClient` component covering all acceptance criteria.
4. Mock `ReviewSortDropdown`, `Pagination`, and `ReviewCard` components to control their behavior and assert on their props.
5. Simulate user interactions (sort selection, pagination clicks, review deletion).
6. Test loading, error, and empty states.
7. Verify `fetchMyReviews` calls with correct parameters for sorting and pagination.
8. Verify review deletion logic, including page navigation and refresh.

File: `C:\web-app\dukancard\app\(dashboard)\dashboard\business\reviews\components\BusinessMyReviewListClient.tsx`
Platform: dukancard
---