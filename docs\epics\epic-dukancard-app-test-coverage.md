# Epic: Comprehensive Test Coverage for Dukancard App Modals

## User Story
As a developer, I want to have comprehensive unit and integration tests for the likes, followers, and reviews modals in the `dukancard-app`, for both customer and business profile views, so that I can ensure the quality and stability of these features.

## Acceptance Criteria
- Unit and integration tests are created for data fetching in all three modals (likes, followers, reviews).
- Unit and integration tests are created for pagination and search functionality in all three modals.
- Unit and integration tests are created for sorting functionality in the reviews modal.
- Tests cover both customer and business profile views.
- All new tests are passing and integrated into the CI/CD pipeline.
- Code coverage for the new tests is above 80%.

## Testing Strategy
- **Mocking:** We will only mock external services like Supabase.
- **Component Behavior:** All local components will be used directly in tests without mocking to ensure we are testing the exact behavior of each component.

## File Dependencies

### Business Profile Modals
- **Profile Page:** `C:\web-app\dukancard-app\app\(dashboard)\business\profile.tsx`
- **Modals:**
    - `C:\web-app\dukancard-app\src\components\modals\business\BusinessLikesModal.tsx`
    - `C:\web-app\dukancard-app\src\components\modals\business\BusinessFollowersModal.tsx`
    - `C:\web-app\dukancard-app\src\components\modals\business\BusinessReviewsModal.tsx`
- **List Components:**
    - `C:\web-app\dukancard-app\src\components\modals\business\components\BusinessLikesList.tsx`
    - `C:\web-app\dukancard-app\src\components\modals\business\components\BusinessLikesGivenList.tsx`
    - `C:\web-app\dukancard-app\src\components\modals\business\components\BusinessFollowersList.tsx`
    - `C:\web-app\dukancard-app\src\components\modals\business\components\BusinessFollowingList.tsx`
    - `C:\web-app\dukancard-app\src\components\modals\business\components\BusinessReviewsList.tsx`
    - `C:\web-app\dukancard-app\src\components\modals\business\components\BusinessReviewsGivenList.tsx`
- **Services:**
    - `C:\web-app\dukancard-app\backend\supabase\services\business\businessSocialService.ts`
    - `C:\web-app\dukancard-app\backend\supabase\services\posts\socialService.ts`

### Customer Profile Modals
- **Profile Page:** `C:\web-app\dukancard-app\app\(dashboard)\customer\profile.tsx`
- **Modals:**
    - `C:\web-app\dukancard-app\src\components\modals\customer\LikesModal.tsx`
    - `C:\web-app\dukancard-app\src\components\modals\customer\FollowingModal.tsx`
    - `C:\web-app\dukancard-app\src\components\modals\customer\ReviewsModal.tsx`
- **List Components:**
    - `C:\web-app\dukancard-app\src\components\modals\customer\components\LikesList.tsx`
    - `C:\web-app\dukancard-app\src\components\modals\customer\components\FollowingList.tsx`
    - `C:\web-app\dukancard-app\src\components\modals\customer\components\ReviewsList.tsx`
- **Service:** `C:\web-app\dukancard-app\backend\supabase\services\posts\socialService.ts`
