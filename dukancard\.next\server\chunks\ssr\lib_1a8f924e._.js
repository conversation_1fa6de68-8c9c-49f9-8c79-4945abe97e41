module.exports = {

"[project]/lib/utils/client-image-compression.ts [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/lib_utils_client-image-compression_ts_554b15a8._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/lib/utils/client-image-compression.ts [app-ssr] (ecmascript)");
    });
});
}}),
"[project]/lib/actions/shared/upload-business-post-media.ts [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/node_modules_next_headers_f21dd19a.js",
  "server/chunks/ssr/_dba43e5c._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/lib/actions/shared/upload-business-post-media.ts [app-ssr] (ecmascript)");
    });
});
}}),
"[project]/lib/actions/posts.ts [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/lib_actions_d2d1baed._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/lib/actions/posts.ts [app-ssr] (ecmascript)");
    });
});
}}),

};