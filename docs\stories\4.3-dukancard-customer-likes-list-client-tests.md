---
Status: Draft
Story: |
  As a developer, I need to implement comprehensive unit and integration tests for the `LikeListClient` component in the `dukancard` project. This component is responsible for transforming raw like data into a format suitable for the shared `LikeList` component and rendering it with specific props for the customer's "My Likes" view. The tests should ensure correct data mapping and proper prop passing to the `LikeList` component.

Acceptance Criteria:
- **Data Transformation:**
    - Given `initialLikes` containing `LikeWithProfile` objects, when the component renders, then `transformedLikes` correctly maps each `like` object to a `LikeData` object with `type: 'business'`.
    - And `profile` is `null` for any `like` object where `business_profiles` is missing.
    - And `null` profiles are filtered out from `transformedLikes`.
- **Rendering with Data:**
    - Given `initialLikes` is not empty, when the component renders, then the `LikeList` component is rendered with the following props:
        - `initialLikes` set to the `transformedLikes` array.
        - `showUnlike={true}`.
        - `emptyMessage="You haven't liked any businesses yet."`.
        - `emptyDescription="Like businesses to see them here and get updates."`.
        - `showDiscoverButton={true}`.
        - `showVisitButton={true}`.
        - `showAddress={true}`.
        - `showRedirectIcon={false}`.
- **Empty State (delegated to LikeList):**
    - The empty state rendering is handled by the `LikeList` component, and this component should correctly pass the `emptyMessage` and `emptyDescription` props.

Tasks:
1. Create a new test file: `C:\web-app\dukancard\__tests__\app\(dashboard)\dashboard\customer\likes\LikeListClient.test.tsx`.
2. Set up a testing environment that can render React components.
3. Write unit tests for the `LikeListClient` component covering all acceptance criteria.
4. Mock the `LikeList` component to verify its props without testing its internal rendering.
5. Test rendering with various `initialLikes` data scenarios (empty, with data).
6. Verify the correct props are passed to `LikeList`.

File: `C:\web-app\dukancard\app\(dashboard)\dashboard\customer\likes\LikeListClient.tsx`
Platform: dukancard
---