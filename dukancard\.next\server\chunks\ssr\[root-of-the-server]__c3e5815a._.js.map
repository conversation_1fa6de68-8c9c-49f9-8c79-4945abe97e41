{"version": 3, "sources": [], "sections": [{"offset": {"line": 87, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/utils/supabase/server.ts"], "sourcesContent": ["import { createServerClient } from '@supabase/ssr';\r\nimport { SupabaseClient } from '@supabase/supabase-js';\r\nimport { Database } from '@/types/supabase';\r\n\r\nexport async function createClient(): Promise<SupabaseClient<Database>> {\r\n  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;\r\n  const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;\r\n\r\n  if (!supabaseUrl || !supabaseAnonKey) {\r\n    throw new Error('Supabase environment variables are not set.');\r\n  }\r\n\r\n  // Check if we're in a test environment\r\n  let headersList: Headers | null = null;\r\n  let cookieStore: any = null;\r\n\r\n  try {\r\n    // Dynamically import next/headers to avoid issues in edge runtime\r\n    const { headers, cookies } = await import('next/headers');\r\n    headersList = await headers();\r\n    cookieStore = await cookies();\r\n  } catch (error) {\r\n    // If next/headers is not available (e.g., in edge runtime), continue without it\r\n    console.warn('next/headers not available in this context, using fallback');\r\n  }\r\n\r\n  const isTestEnvironment =\r\n    process.env.NODE_ENV === 'test' ||\r\n    process.env.PLAYWRIGHT_TESTING === 'true' ||\r\n    (headersList && headersList.get('x-playwright-testing') === 'true');\r\n\r\n  if (isTestEnvironment && headersList) {\r\n    // Return a mocked Supabase client for testing\r\n    return createMockSupabaseClient(headersList) as unknown as SupabaseClient<Database>;\r\n  }\r\n\r\n  // If cookies are not available, create a basic server client\r\n  if (!cookieStore) {\r\n    return createServerClient(\r\n      supabaseUrl,\r\n      supabaseAnonKey,\r\n      {\r\n        cookies: {\r\n          getAll() {\r\n            return [];\r\n          },\r\n          setAll() {\r\n            // No-op when cookies are not available\r\n          },\r\n        },\r\n      }\r\n    ) as unknown as SupabaseClient<Database>;\r\n  }\r\n\r\n  return createServerClient(\r\n    supabaseUrl,\r\n    supabaseAnonKey,\r\n    {\r\n      cookies: {\r\n        async getAll() {\r\n          return await cookieStore.getAll();\r\n        },\r\n        async setAll(cookiesToSet: any[]) {\r\n          try {\r\n            for (const { name, value, options } of cookiesToSet) {\r\n              await cookieStore.set(name, value, options);\r\n            }\r\n          } catch {\r\n            // The `setAll` method was called from a Server Component.\r\n            // This can be ignored if you have middleware refreshing\r\n            // user sessions.\r\n          }\r\n        },\r\n      },\r\n    }\r\n  ) as unknown as SupabaseClient<Database>;\r\n}\r\n\r\ntype MockQueryBuilder = {\r\n  select: (columns?: string) => MockQueryBuilder;\r\n  eq: (column: string, value: any) => MockQueryBuilder;\r\n  neq: (column: string, value: any) => MockQueryBuilder;\r\n  gt: (column: string, value: any) => MockQueryBuilder;\r\n  gte: (column: string, value: any) => MockQueryBuilder;\r\n  lt: (column: string, value: any) => MockQueryBuilder;\r\n  lte: (column: string, value: any) => MockQueryBuilder;\r\n  like: (column: string, pattern: string) => MockQueryBuilder;\r\n  ilike: (column: string, pattern: string) => MockQueryBuilder;\r\n  is: (column: string, value: any) => MockQueryBuilder;\r\n  in: (column: string, values: any[]) => MockQueryBuilder;\r\n  contains: (column: string, value: any) => MockQueryBuilder;\r\n  containedBy: (column: string, value: any) => MockQueryBuilder;\r\n  rangeGt: (column: string, value: any) => MockQueryBuilder;\r\n  rangeGte: (column: string, value: any) => MockQueryBuilder;\r\n  rangeLt: (column: string, value: any) => MockQueryBuilder;\r\n  rangeLte: (column: string, value: any) => MockQueryBuilder;\r\n  rangeAdjacent: (column: string, value: any) => MockQueryBuilder;\r\n  overlaps: (column: string, value: any) => MockQueryBuilder;\r\n  textSearch: (column: string, query: string) => MockQueryBuilder;\r\n  match: (query: Record<string, any>) => MockQueryBuilder;\r\n  not: (column: string, operator: string, value: any) => MockQueryBuilder;\r\n  or: (filters: string) => MockQueryBuilder;\r\n  filter: (column: string, operator: string, value: any) => MockQueryBuilder;\r\n  order: (column: string, options?: { ascending?: boolean; nullsFirst?: boolean }) => MockQueryBuilder;\r\n  limit: (count: number, options?: { foreignTable?: string }) => MockQueryBuilder;\r\n  range: (from: number, to: number, options?: { foreignTable?: string }) => MockQueryBuilder;\r\n  abortSignal: (signal: AbortSignal) => MockQueryBuilder;\r\n  single: () => Promise<any>;\r\n  maybeSingle: () => Promise<any>;\r\n  then: (callback?: any) => Promise<any>;\r\n  data: any;\r\n  error: any;\r\n  count: number;\r\n  status: number;\r\n  statusText: string;\r\n};\r\n\r\nfunction createMockSupabaseClient(headersList: Headers) {\r\n  const testAuthState = headersList.get('x-test-auth-state');\r\n  const testUserType = headersList.get('x-test-user-type');\r\n  const testHasProfile = testUserType === 'customer' || testUserType === 'business';\r\n  const testBusinessSlug = headersList.get('x-test-business-slug');\r\n  const testPlanId = headersList.get('x-test-plan-id') || 'free';\r\n\r\n  return {\r\n    auth: {\r\n      getUser: async () => {\r\n        if (testAuthState === 'authenticated') {\r\n          return { data: { user: { id: 'test-user-id', email: '<EMAIL>' } }, error: null };\r\n        }\r\n        return { data: { user: null }, error: { message: 'Unauthorized', name: 'AuthApiError', status: 401 } };\r\n      },\r\n      getSession: async () => {\r\n        if (testAuthState === 'authenticated') {\r\n          return { data: { session: { user: { id: 'test-user-id', email: '<EMAIL>' } } }, error: null };\r\n        }\r\n        return { data: { session: null }, error: { message: 'Unauthorized', name: 'AuthApiError', status: 401 } };\r\n      },\r\n      signInWithOtp: async () => ({ data: { user: null, session: null }, error: null }),\r\n      signOut: async () => ({ error: null }),\r\n    },\r\n    from: (table: string) => createMockQueryBuilder(table, testUserType, testHasProfile, testBusinessSlug, testPlanId),\r\n  };\r\n}\r\n\r\nfunction createMockQueryBuilder(\r\n  table: string,\r\n  testUserType: string | null,\r\n  testHasProfile: boolean,\r\n  testBusinessSlug: string | null,\r\n  testPlanId: string\r\n): any {\r\n  const getMockData = () => getMockTableData(table, testUserType, testHasProfile, testBusinessSlug, testPlanId);\r\n\r\n  const createChainableMock = (data?: any): MockQueryBuilder => ({\r\n    select: (_columns?: string) => createChainableMock(data),\r\n    eq: (_column: string, _value: any) => createChainableMock(data),\r\n    neq: (_column: string, _value: any) => createChainableMock(data),\r\n    gt: (_column: string, _value: any) => createChainableMock(data),\r\n    gte: (_column: string, _value: any) => createChainableMock(data),\r\n    lt: (_column: string, _value: any) => createChainableMock(data),\r\n    lte: (_column: string, _value: any) => createChainableMock(data),\r\n    like: (_column: string, _pattern: string) => createChainableMock(data),\r\n    ilike: (_column: string, _pattern: string) => createChainableMock(data),\r\n    is: (_column: string, _value: any) => createChainableMock(data),\r\n    in: (_column: string, _values: any[]) => createChainableMock(data),\r\n    contains: (_column: string, _value: any) => createChainableMock(data),\r\n    containedBy: (_column: string, _value: any) => createChainableMock(data),\r\n    rangeGt: (_column: string, _value: any) => createChainableMock(data),\r\n    rangeGte: (_column: string, _value: any) => createChainableMock(data),\r\n    rangeLt: (_column: string, _value: any) => createChainableMock(data),\r\n    rangeLte: (_column: string, _value: any) => createChainableMock(data),\r\n    rangeAdjacent: (_column: string, _value: any) => createChainableMock(data),\r\n    overlaps: (_column: string, _value: any) => createChainableMock(data),\r\n    textSearch: (_column: string, _query: string) => createChainableMock(data),\r\n    match: (_query: Record<string, any>) => createChainableMock(data),\r\n    not: (_column: string, _operator: string, _value: any) => createChainableMock(data),\r\n    or: (_filters: string) => createChainableMock(data),\r\n    filter: (_column: string, _operator: string, _value: any) => createChainableMock(data),\r\n    order: (_column: string, _options?: { ascending?: boolean; nullsFirst?: boolean }) => createChainableMock(data),\r\n    limit: (_count: number, _options?: { foreignTable?: string }) => createChainableMock(data),\r\n    range: (_from: number, _to: number, _options?: { foreignTable?: string }) => createChainableMock(data),\r\n    abortSignal: (_signal: AbortSignal) => createChainableMock(data),\r\n    single: async () => getMockData(),\r\n    maybeSingle: async () => getMockData(),\r\n    then: async (callback?: any) => {\r\n      const result = getMockData();\r\n      return callback ? callback(result) : result;\r\n    },\r\n    data: data || [],\r\n    error: null,\r\n    count: data ? data.length : 0,\r\n    status: 200,\r\n    statusText: 'OK',\r\n  });\r\n\r\n  return {\r\n    select: (_columns?: string) => createChainableMock(),\r\n    insert: (data: any | any[]) => ({\r\n      select: (_columns?: string) => ({\r\n        single: async () => ({\r\n          data: Array.isArray(data) ? data[0] : data,\r\n          error: null,\r\n        }),\r\n        maybeSingle: async () => ({\r\n          data: Array.isArray(data) ? data[0] : data,\r\n          error: null,\r\n        }),\r\n        then: async (_callback?: any) => {\r\n          const result = { data: Array.isArray(data) ? data : [data], error: null };\r\n          return _callback ? _callback(result) : result;\r\n        },\r\n      }),\r\n      then: async (_callback?: any) => {\r\n        const result = { data: Array.isArray(data) ? data : [data], error: null };\r\n        return _callback ? _callback(result) : result;\r\n      },\r\n    }),\r\n    update: (data: any) => createChainableMock(data),\r\n    upsert: (data: any | any[]) => createChainableMock(data),\r\n    delete: () => createChainableMock(),\r\n    rpc: (_functionName: string, _params?: any) => createChainableMock(),\r\n  };\r\n}\r\n\r\n/**\r\n * Helper function to get mock table data based on test state\r\n */\r\nfunction getMockTableData(\r\n  table: string,\r\n  testUserType: string | null,\r\n  testHasProfile: boolean,\r\n  testBusinessSlug: string | null,\r\n  testPlanId: string\r\n) {\r\n  if (table === 'customer_profiles') {\r\n    const hasCustomerProfile = testHasProfile && testUserType === 'customer';\r\n    return {\r\n      data: hasCustomerProfile ? {\r\n        id: 'test-user-id',\r\n        name: 'Test Customer',\r\n        avatar_url: null,\r\n        phone: '+1234567890',\r\n        email: '<EMAIL>',\r\n        address: 'Test Address',\r\n        city: 'Test City',\r\n        state: 'Test State',\r\n        pincode: '123456'\r\n      } : null,\r\n      error: null\r\n    };\r\n  }\r\n\r\n  if (table === 'business_profiles') {\r\n    const hasBusinessProfile = testHasProfile && testUserType === 'business';\r\n    return {\r\n      data: hasBusinessProfile ? {\r\n        id: 'test-user-id',\r\n        business_slug: testBusinessSlug || null,\r\n        trial_end_date: null,\r\n        has_active_subscription: true,\r\n        business_name: 'Test Business',\r\n        city_slug: 'test-city',\r\n        state_slug: 'test-state',\r\n        locality_slug: 'test-locality',\r\n        pincode: '123456',\r\n        business_description: 'Test business description',\r\n        business_category: 'retail',\r\n        phone: '+1234567890',\r\n        email: '<EMAIL>',\r\n        website: 'https://testbusiness.com'\r\n      } : null,\r\n      error: null\r\n    };\r\n  }\r\n\r\n  if (table === 'payment_subscriptions') {\r\n    return {\r\n      data: testUserType === 'business' ? {\r\n        id: 'test-subscription-id',\r\n        plan_id: testPlanId,\r\n        business_profile_id: 'test-user-id',\r\n        status: 'active',\r\n        created_at: '2024-01-01T00:00:00Z'\r\n      } : null,\r\n      error: null\r\n    };\r\n  }\r\n\r\n  if (table === 'products') {\r\n    return {\r\n      data: testUserType === 'business' ? [\r\n        {\r\n          id: 'test-product-1',\r\n          name: 'Test Product 1',\r\n          price: 100,\r\n          business_profile_id: 'test-user-id',\r\n          available: true\r\n        },\r\n        {\r\n          id: 'test-product-2',\r\n          name: 'Test Product 2',\r\n          price: 200,\r\n          business_profile_id: 'test-user-id',\r\n          available: false\r\n        }\r\n      ] : [],\r\n      error: null\r\n    };\r\n  }\r\n\r\n  // Default return for unknown tables\r\n  return { data: null, error: null };\r\n}\r\n\r\n"], "names": [], "mappings": ";;;AAAA;AAAA;;AAIO,eAAe;IACpB,MAAM;IACN,MAAM;IAEN,uCAAsC;;IAEtC;IAEA,uCAAuC;IACvC,IAAI,cAA8B;IAClC,IAAI,cAAmB;IAEvB,IAAI;QACF,kEAAkE;QAClE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG;QAC7B,cAAc,MAAM;QACpB,cAAc,MAAM;IACtB,EAAE,OAAO,OAAO;QACd,gFAAgF;QAChF,QAAQ,IAAI,CAAC;IACf;IAEA,MAAM,oBACJ,oDAAyB,UACzB,QAAQ,GAAG,CAAC,kBAAkB,KAAK,UAClC,eAAe,YAAY,GAAG,CAAC,4BAA4B;IAE9D,IAAI,qBAAqB,aAAa;QACpC,8CAA8C;QAC9C,OAAO,yBAAyB;IAClC;IAEA,6DAA6D;IAC7D,IAAI,CAAC,aAAa;QAChB,OAAO,CAAA,GAAA,yKAAA,CAAA,qBAAkB,AAAD,EACtB,aACA,iBACA;YACE,SAAS;gBACP;oBACE,OAAO,EAAE;gBACX;gBACA;gBACE,uCAAuC;gBACzC;YACF;QACF;IAEJ;IAEA,OAAO,CAAA,GAAA,yKAAA,CAAA,qBAAkB,AAAD,EACtB,aACA,iBACA;QACE,SAAS;YACP,MAAM;gBACJ,OAAO,MAAM,YAAY,MAAM;YACjC;YACA,MAAM,QAAO,YAAmB;gBAC9B,IAAI;oBACF,KAAK,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,IAAI,aAAc;wBACnD,MAAM,YAAY,GAAG,CAAC,MAAM,OAAO;oBACrC;gBACF,EAAE,OAAM;gBACN,0DAA0D;gBAC1D,wDAAwD;gBACxD,iBAAiB;gBACnB;YACF;QACF;IACF;AAEJ;AAyCA,SAAS,yBAAyB,WAAoB;IACpD,MAAM,gBAAgB,YAAY,GAAG,CAAC;IACtC,MAAM,eAAe,YAAY,GAAG,CAAC;IACrC,MAAM,iBAAiB,iBAAiB,cAAc,iBAAiB;IACvE,MAAM,mBAAmB,YAAY,GAAG,CAAC;IACzC,MAAM,aAAa,YAAY,GAAG,CAAC,qBAAqB;IAExD,OAAO;QACL,MAAM;YACJ,SAAS;gBACP,IAAI,kBAAkB,iBAAiB;oBACrC,OAAO;wBAAE,MAAM;4BAAE,MAAM;gCAAE,IAAI;gCAAgB,OAAO;4BAAmB;wBAAE;wBAAG,OAAO;oBAAK;gBAC1F;gBACA,OAAO;oBAAE,MAAM;wBAAE,MAAM;oBAAK;oBAAG,OAAO;wBAAE,SAAS;wBAAgB,MAAM;wBAAgB,QAAQ;oBAAI;gBAAE;YACvG;YACA,YAAY;gBACV,IAAI,kBAAkB,iBAAiB;oBACrC,OAAO;wBAAE,MAAM;4BAAE,SAAS;gCAAE,MAAM;oCAAE,IAAI;oCAAgB,OAAO;gCAAmB;4BAAE;wBAAE;wBAAG,OAAO;oBAAK;gBACvG;gBACA,OAAO;oBAAE,MAAM;wBAAE,SAAS;oBAAK;oBAAG,OAAO;wBAAE,SAAS;wBAAgB,MAAM;wBAAgB,QAAQ;oBAAI;gBAAE;YAC1G;YACA,eAAe,UAAY,CAAC;oBAAE,MAAM;wBAAE,MAAM;wBAAM,SAAS;oBAAK;oBAAG,OAAO;gBAAK,CAAC;YAChF,SAAS,UAAY,CAAC;oBAAE,OAAO;gBAAK,CAAC;QACvC;QACA,MAAM,CAAC,QAAkB,uBAAuB,OAAO,cAAc,gBAAgB,kBAAkB;IACzG;AACF;AAEA,SAAS,uBACP,KAAa,EACb,YAA2B,EAC3B,cAAuB,EACvB,gBAA+B,EAC/B,UAAkB;IAElB,MAAM,cAAc,IAAM,iBAAiB,OAAO,cAAc,gBAAgB,kBAAkB;IAElG,MAAM,sBAAsB,CAAC,OAAiC,CAAC;YAC7D,QAAQ,CAAC,WAAsB,oBAAoB;YACnD,IAAI,CAAC,SAAiB,SAAgB,oBAAoB;YAC1D,KAAK,CAAC,SAAiB,SAAgB,oBAAoB;YAC3D,IAAI,CAAC,SAAiB,SAAgB,oBAAoB;YAC1D,KAAK,CAAC,SAAiB,SAAgB,oBAAoB;YAC3D,IAAI,CAAC,SAAiB,SAAgB,oBAAoB;YAC1D,KAAK,CAAC,SAAiB,SAAgB,oBAAoB;YAC3D,MAAM,CAAC,SAAiB,WAAqB,oBAAoB;YACjE,OAAO,CAAC,SAAiB,WAAqB,oBAAoB;YAClE,IAAI,CAAC,SAAiB,SAAgB,oBAAoB;YAC1D,IAAI,CAAC,SAAiB,UAAmB,oBAAoB;YAC7D,UAAU,CAAC,SAAiB,SAAgB,oBAAoB;YAChE,aAAa,CAAC,SAAiB,SAAgB,oBAAoB;YACnE,SAAS,CAAC,SAAiB,SAAgB,oBAAoB;YAC/D,UAAU,CAAC,SAAiB,SAAgB,oBAAoB;YAChE,SAAS,CAAC,SAAiB,SAAgB,oBAAoB;YAC/D,UAAU,CAAC,SAAiB,SAAgB,oBAAoB;YAChE,eAAe,CAAC,SAAiB,SAAgB,oBAAoB;YACrE,UAAU,CAAC,SAAiB,SAAgB,oBAAoB;YAChE,YAAY,CAAC,SAAiB,SAAmB,oBAAoB;YACrE,OAAO,CAAC,SAAgC,oBAAoB;YAC5D,KAAK,CAAC,SAAiB,WAAmB,SAAgB,oBAAoB;YAC9E,IAAI,CAAC,WAAqB,oBAAoB;YAC9C,QAAQ,CAAC,SAAiB,WAAmB,SAAgB,oBAAoB;YACjF,OAAO,CAAC,SAAiB,WAA6D,oBAAoB;YAC1G,OAAO,CAAC,QAAgB,WAAyC,oBAAoB;YACrF,OAAO,CAAC,OAAe,KAAa,WAAyC,oBAAoB;YACjG,aAAa,CAAC,UAAyB,oBAAoB;YAC3D,QAAQ,UAAY;YACpB,aAAa,UAAY;YACzB,MAAM,OAAO;gBACX,MAAM,SAAS;gBACf,OAAO,WAAW,SAAS,UAAU;YACvC;YACA,MAAM,QAAQ,EAAE;YAChB,OAAO;YACP,OAAO,OAAO,KAAK,MAAM,GAAG;YAC5B,QAAQ;YACR,YAAY;QACd,CAAC;IAED,OAAO;QACL,QAAQ,CAAC,WAAsB;QAC/B,QAAQ,CAAC,OAAsB,CAAC;gBAC9B,QAAQ,CAAC,WAAsB,CAAC;wBAC9B,QAAQ,UAAY,CAAC;gCACnB,MAAM,MAAM,OAAO,CAAC,QAAQ,IAAI,CAAC,EAAE,GAAG;gCACtC,OAAO;4BACT,CAAC;wBACD,aAAa,UAAY,CAAC;gCACxB,MAAM,MAAM,OAAO,CAAC,QAAQ,IAAI,CAAC,EAAE,GAAG;gCACtC,OAAO;4BACT,CAAC;wBACD,MAAM,OAAO;4BACX,MAAM,SAAS;gCAAE,MAAM,MAAM,OAAO,CAAC,QAAQ,OAAO;oCAAC;iCAAK;gCAAE,OAAO;4BAAK;4BACxE,OAAO,YAAY,UAAU,UAAU;wBACzC;oBACF,CAAC;gBACD,MAAM,OAAO;oBACX,MAAM,SAAS;wBAAE,MAAM,MAAM,OAAO,CAAC,QAAQ,OAAO;4BAAC;yBAAK;wBAAE,OAAO;oBAAK;oBACxE,OAAO,YAAY,UAAU,UAAU;gBACzC;YACF,CAAC;QACD,QAAQ,CAAC,OAAc,oBAAoB;QAC3C,QAAQ,CAAC,OAAsB,oBAAoB;QACnD,QAAQ,IAAM;QACd,KAAK,CAAC,eAAuB,UAAkB;IACjD;AACF;AAEA;;CAEC,GACD,SAAS,iBACP,KAAa,EACb,YAA2B,EAC3B,cAAuB,EACvB,gBAA+B,EAC/B,UAAkB;IAElB,IAAI,UAAU,qBAAqB;QACjC,MAAM,qBAAqB,kBAAkB,iBAAiB;QAC9D,OAAO;YACL,MAAM,qBAAqB;gBACzB,IAAI;gBACJ,MAAM;gBACN,YAAY;gBACZ,OAAO;gBACP,OAAO;gBACP,SAAS;gBACT,MAAM;gBACN,OAAO;gBACP,SAAS;YACX,IAAI;YACJ,OAAO;QACT;IACF;IAEA,IAAI,UAAU,qBAAqB;QACjC,MAAM,qBAAqB,kBAAkB,iBAAiB;QAC9D,OAAO;YACL,MAAM,qBAAqB;gBACzB,IAAI;gBACJ,eAAe,oBAAoB;gBACnC,gBAAgB;gBAChB,yBAAyB;gBACzB,eAAe;gBACf,WAAW;gBACX,YAAY;gBACZ,eAAe;gBACf,SAAS;gBACT,sBAAsB;gBACtB,mBAAmB;gBACnB,OAAO;gBACP,OAAO;gBACP,SAAS;YACX,IAAI;YACJ,OAAO;QACT;IACF;IAEA,IAAI,UAAU,yBAAyB;QACrC,OAAO;YACL,MAAM,iBAAiB,aAAa;gBAClC,IAAI;gBACJ,SAAS;gBACT,qBAAqB;gBACrB,QAAQ;gBACR,YAAY;YACd,IAAI;YACJ,OAAO;QACT;IACF;IAEA,IAAI,UAAU,YAAY;QACxB,OAAO;YACL,MAAM,iBAAiB,aAAa;gBAClC;oBACE,IAAI;oBACJ,MAAM;oBACN,OAAO;oBACP,qBAAqB;oBACrB,WAAW;gBACb;gBACA;oBACE,IAAI;oBACJ,MAAM;oBACN,OAAO;oBACP,qBAAqB;oBACrB,WAAW;gBACb;aACD,GAAG,EAAE;YACN,OAAO;QACT;IACF;IAEA,oCAAoC;IACpC,OAAO;QAAE,MAAM;QAAM,OAAO;IAAK;AACnC", "debugId": null}}, {"offset": {"line": 386, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/business/components/BusinessDashboardClientLayout.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/app/(dashboard)/dashboard/business/components/BusinessDashboardClientLayout.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/app/(dashboard)/dashboard/business/components/BusinessDashboardClientLayout.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAmV,GAChX,iHACA", "debugId": null}}, {"offset": {"line": 400, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/business/components/BusinessDashboardClientLayout.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/app/(dashboard)/dashboard/business/components/BusinessDashboardClientLayout.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/app/(dashboard)/dashboard/business/components/BusinessDashboardClientLayout.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA+T,GAC5V,6FACA", "debugId": null}}, {"offset": {"line": 414, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 424, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/utils/business-validation.ts"], "sourcesContent": ["/**\r\n * Utility functions for validating business profile completeness\r\n */\r\nimport { type Tables } from \"@/types/supabase\";\r\n\r\ntype BusinessProfiles = Tables<'business_profiles'>;\r\n\r\n// Required fields for accessing business dashboard\r\nexport const REQUIRED_BUSINESS_FIELDS = [\r\n  \"member_name\",\r\n  \"title\",\r\n  \"business_name\",\r\n  \"business_category\",\r\n  \"contact_email\",\r\n  \"phone\",\r\n  \"address_line\",\r\n  \"pincode\",\r\n  \"city\",\r\n  \"state\",\r\n  \"locality\",\r\n] as const;\r\n\r\nexport type RequiredBusinessField = (typeof REQUIRED_BUSINESS_FIELDS)[number];\r\n\r\n/**\r\n * Check if all required business fields are complete\r\n * @param profile - Business profile object\r\n * @returns Object with validation result and missing fields\r\n */\r\nexport function validateRequiredBusinessFields(\r\n  profile: Partial<BusinessProfiles> | null\r\n) {\r\n  if (!profile) {\r\n    return {\r\n      isComplete: false,\r\n      missingFields: [...REQUIRED_BUSINESS_FIELDS],\r\n      missingFieldLabels: [\r\n        \"Your name\",\r\n        \"Your title\",\r\n        \"Business name\",\r\n        \"Business category\",\r\n        \"Contact email\",\r\n        \"Primary phone\",\r\n        \"Address line\",\r\n        \"Pincode\",\r\n        \"City\",\r\n        \"State\",\r\n        \"Locality/area\",\r\n      ],\r\n    };\r\n  }\r\n\r\n  const missingFields: RequiredBusinessField[] = [];\r\n  const missingFieldLabels: string[] = [];\r\n\r\n  const fieldLabelMap: Record<RequiredBusinessField, string> = {\r\n    member_name: \"Your name\",\r\n    title: \"Your title\",\r\n    business_name: \"Business name\",\r\n    business_category: \"Business category\",\r\n    contact_email: \"Contact email\",\r\n    phone: \"Primary phone\",\r\n    address_line: \"Address line\",\r\n    pincode: \"Pincode\",\r\n    city: \"City\",\r\n    state: \"State\",\r\n    locality: \"Locality/area\",\r\n  };\r\n\r\n  REQUIRED_BUSINESS_FIELDS.forEach((field) => {\r\n    const value = profile[field as keyof BusinessProfiles];\r\n    if (!value || String(value).trim() === \"\") {\r\n      missingFields.push(field);\r\n      missingFieldLabels.push(fieldLabelMap[field]);\r\n    }\r\n  });\r\n\r\n  return {\r\n    isComplete: missingFields.length === 0,\r\n    missingFields,\r\n    missingFieldLabels,\r\n  };\r\n}\r\n\r\n/**\r\n * Generate a user-friendly message for missing fields\r\n * @param missingFieldLabels - Array of missing field labels\r\n * @returns Formatted message string\r\n */\r\nexport function generateMissingFieldsMessage(\r\n  missingFieldLabels: string[]\r\n): string {\r\n  if (missingFieldLabels.length === 0) return \"\";\r\n\r\n  if (missingFieldLabels.length === 1) {\r\n    return `Please complete your ${missingFieldLabels[0].toLowerCase()} to access the dashboard.`;\r\n  }\r\n\r\n  if (missingFieldLabels.length === 2) {\r\n    return `Please complete your ${missingFieldLabels[0].toLowerCase()} and ${missingFieldLabels[1].toLowerCase()} to access the dashboard.`;\r\n  }\r\n\r\n  const lastField = missingFieldLabels[missingFieldLabels.length - 1];\r\n  const otherFields = missingFieldLabels.slice(0, -1);\r\n\r\n  return `Please complete your ${otherFields\r\n    .map((f) => f.toLowerCase())\r\n    .join(\", \")}, and ${lastField.toLowerCase()} to access the dashboard.`;\r\n}\r\n"], "names": [], "mappings": "AAAA;;CAEC;;;;;AAMM,MAAM,2BAA2B;IACtC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AASM,SAAS,+BACd,OAAyC;IAEzC,IAAI,CAAC,SAAS;QACZ,OAAO;YACL,YAAY;YACZ,eAAe;mBAAI;aAAyB;YAC5C,oBAAoB;gBAClB;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;QACH;IACF;IAEA,MAAM,gBAAyC,EAAE;IACjD,MAAM,qBAA+B,EAAE;IAEvC,MAAM,gBAAuD;QAC3D,aAAa;QACb,OAAO;QACP,eAAe;QACf,mBAAmB;QACnB,eAAe;QACf,OAAO;QACP,cAAc;QACd,SAAS;QACT,MAAM;QACN,OAAO;QACP,UAAU;IACZ;IAEA,yBAAyB,OAAO,CAAC,CAAC;QAChC,MAAM,QAAQ,OAAO,CAAC,MAAgC;QACtD,IAAI,CAAC,SAAS,OAAO,OAAO,IAAI,OAAO,IAAI;YACzC,cAAc,IAAI,CAAC;YACnB,mBAAmB,IAAI,CAAC,aAAa,CAAC,MAAM;QAC9C;IACF;IAEA,OAAO;QACL,YAAY,cAAc,MAAM,KAAK;QACrC;QACA;IACF;AACF;AAOO,SAAS,6BACd,kBAA4B;IAE5B,IAAI,mBAAmB,MAAM,KAAK,GAAG,OAAO;IAE5C,IAAI,mBAAmB,MAAM,KAAK,GAAG;QACnC,OAAO,CAAC,qBAAqB,EAAE,kBAAkB,CAAC,EAAE,CAAC,WAAW,GAAG,yBAAyB,CAAC;IAC/F;IAEA,IAAI,mBAAmB,MAAM,KAAK,GAAG;QACnC,OAAO,CAAC,qBAAqB,EAAE,kBAAkB,CAAC,EAAE,CAAC,WAAW,GAAG,KAAK,EAAE,kBAAkB,CAAC,EAAE,CAAC,WAAW,GAAG,yBAAyB,CAAC;IAC1I;IAEA,MAAM,YAAY,kBAAkB,CAAC,mBAAmB,MAAM,GAAG,EAAE;IACnE,MAAM,cAAc,mBAAmB,KAAK,CAAC,GAAG,CAAC;IAEjD,OAAO,CAAC,qBAAqB,EAAE,YAC5B,GAAG,CAAC,CAAC,IAAM,EAAE,WAAW,IACxB,IAAI,CAAC,MAAM,MAAM,EAAE,UAAU,WAAW,GAAG,yBAAyB,CAAC;AAC1E", "debugId": null}}, {"offset": {"line": 512, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/business/layout.tsx"], "sourcesContent": ["import React from \"react\";\r\n// import { cookies } from \"next/headers\"; // Removed unused import\r\nimport { createClient } from \"@/utils/supabase/server\"; // Correct import name\r\nimport BusinessDashboardClientLayout from \"./components/BusinessDashboardClientLayout\";\r\nimport { validateRequiredBusinessFields, generateMissingFieldsMessage } from \"@/utils/business-validation\";\r\nimport { redirect } from \"next/navigation\";\r\n\r\n// This is now a Server Component\r\nexport default async function BusinessDashboardLayout({\r\n  // Make the function async\r\n  children,\r\n}: {\r\n  children: React.ReactNode;\r\n}) {\r\n  // const cookieStore = cookies(); // createClient handles this\r\n  const supabase = await createClient(); // Correct function call and add await as it's async\r\n\r\n  let businessName: string | null = null;\r\n  let logoUrl: string | null = null;\r\n  let memberName: string | null = null;\r\n  let userPlan: string | null = null;\r\n  let _businessProfile: Record<string, unknown> | null = null;\r\n\r\n  const {\r\n    data: { user },\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (user) {\r\n    // Get business profile data with all required fields for validation\r\n    const { data: profile, error } = await supabase\r\n      .from(\"business_profiles\")\r\n      .select(`\r\n        business_name,\r\n        logo_url,\r\n        member_name,\r\n        title,\r\n        business_category,\r\n        contact_email,\r\n        phone,\r\n        address_line,\r\n        pincode,\r\n        city,\r\n        state,\r\n        locality\r\n      `)\r\n      .eq(\"id\", user.id)\r\n      .single();\r\n\r\n    // Get subscription data to determine plan\r\n    const { data: subscription } = await supabase\r\n      .from(\"payment_subscriptions\")\r\n      .select(\"plan_id\")\r\n      .eq(\"business_profile_id\", user.id)\r\n      .order(\"created_at\", { ascending: false })\r\n      .limit(1)\r\n      .maybeSingle();\r\n\r\n    if (error) {\r\n      console.error(\r\n        \"Error fetching business profile in layout:\",\r\n        error.message\r\n      );\r\n      // Handle error appropriately, maybe redirect or show an error state\r\n      // For now, we'll proceed with null values\r\n    } else if (profile) {\r\n      businessName = profile.business_name;\r\n      logoUrl = profile.logo_url;\r\n      memberName = profile.member_name;\r\n      userPlan = subscription?.plan_id || \"free\";\r\n      _businessProfile = profile;\r\n\r\n      // Validate required business fields\r\n      const validation = validateRequiredBusinessFields(profile);\r\n      if (!validation.isComplete) {\r\n        // Generate a message for missing fields\r\n        const message = generateMissingFieldsMessage(validation.missingFieldLabels);\r\n\r\n        // Redirect to onboarding with message\r\n        redirect(`/onboarding?message=${encodeURIComponent(message)}`);\r\n      }\r\n    }\r\n  } else {\r\n    // This case should ideally be handled by middleware, but good to have a fallback\r\n    console.warn(\"No user found in business dashboard layout.\");\r\n  }\r\n\r\n  // Render the Client Layout component, passing fetched data as props\r\n  return (\r\n    <BusinessDashboardClientLayout\r\n      businessName={businessName}\r\n      logoUrl={logoUrl}\r\n      memberName={memberName}\r\n      userPlan={userPlan}\r\n    >\r\n      {children}\r\n    </BusinessDashboardClientLayout>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AACA,mEAAmE;AACnE,wNAAwD,sBAAsB;AAC9E;AACA;AACA;AAAA;;;;;;AAGe,eAAe,wBAAwB,EACpD,0BAA0B;AAC1B,QAAQ,EAGT;IACC,8DAA8D;IAC9D,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD,KAAK,oDAAoD;IAE3F,IAAI,eAA8B;IAClC,IAAI,UAAyB;IAC7B,IAAI,aAA4B;IAChC,IAAI,WAA0B;IAC9B,IAAI,mBAAmD;IAEvD,MAAM,EACJ,MAAM,EAAE,IAAI,EAAE,EACf,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;IAE/B,IAAI,MAAM;QACR,oEAAoE;QACpE,MAAM,EAAE,MAAM,OAAO,EAAE,KAAK,EAAE,GAAG,MAAM,SACpC,IAAI,CAAC,qBACL,MAAM,CAAC,CAAC;;;;;;;;;;;;;MAaT,CAAC,EACA,EAAE,CAAC,MAAM,KAAK,EAAE,EAChB,MAAM;QAET,0CAA0C;QAC1C,MAAM,EAAE,MAAM,YAAY,EAAE,GAAG,MAAM,SAClC,IAAI,CAAC,yBACL,MAAM,CAAC,WACP,EAAE,CAAC,uBAAuB,KAAK,EAAE,EACjC,KAAK,CAAC,cAAc;YAAE,WAAW;QAAM,GACvC,KAAK,CAAC,GACN,WAAW;QAEd,IAAI,OAAO;YACT,QAAQ,KAAK,CACX,8CACA,MAAM,OAAO;QAEf,oEAAoE;QACpE,0CAA0C;QAC5C,OAAO,IAAI,SAAS;YAClB,eAAe,QAAQ,aAAa;YACpC,UAAU,QAAQ,QAAQ;YAC1B,aAAa,QAAQ,WAAW;YAChC,WAAW,cAAc,WAAW;YACpC,mBAAmB;YAEnB,oCAAoC;YACpC,MAAM,aAAa,CAAA,GAAA,+HAAA,CAAA,iCAA8B,AAAD,EAAE;YAClD,IAAI,CAAC,WAAW,UAAU,EAAE;gBAC1B,wCAAwC;gBACxC,MAAM,UAAU,CAAA,GAAA,+HAAA,CAAA,+BAA4B,AAAD,EAAE,WAAW,kBAAkB;gBAE1E,sCAAsC;gBACtC,CAAA,GAAA,qLAAA,CAAA,WAAQ,AAAD,EAAE,CAAC,oBAAoB,EAAE,mBAAmB,UAAU;YAC/D;QACF;IACF,OAAO;QACL,iFAAiF;QACjF,QAAQ,IAAI,CAAC;IACf;IAEA,oEAAoE;IACpE,qBACE,8OAAC,6LAAA,CAAA,UAA6B;QAC5B,cAAc;QACd,SAAS;QACT,YAAY;QACZ,UAAU;kBAET;;;;;;AAGP", "debugId": null}}]}