{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/components/qr/qr-scanner.css"], "sourcesContent": ["/* Custom styles for html5-qrcode scanner to match Dukancard theme */\n\n/* Scanner container */\n#qr-scanner-region {\n  border-radius: 12px;\n  overflow: hidden;\n  background: #000;\n}\n\n/* Scanner video element */\n#qr-scanner-region video {\n  border-radius: 12px;\n  width: 100% !important;\n  height: auto !important;\n  object-fit: cover;\n}\n\n/* Scanner overlay */\n#qr-scanner-region canvas {\n  border-radius: 12px;\n}\n\n/* Control buttons styling */\n#qr-scanner-region button {\n  background: rgba(212, 175, 55, 0.9) !important; /* Gold theme */\n  border: none !important;\n  border-radius: 8px !important;\n  color: white !important;\n  font-weight: 500 !important;\n  padding: 8px 16px !important;\n  margin: 4px !important;\n  transition: all 0.2s ease !important;\n}\n\n#qr-scanner-region button:hover {\n  background: rgba(212, 175, 55, 1) !important;\n  transform: translateY(-1px);\n}\n\n/* Torch/flashlight button */\n#qr-scanner-region button[title*=\"torch\"],\n#qr-scanner-region button[title*=\"Torch\"],\n#qr-scanner-region button[title*=\"flash\"],\n#qr-scanner-region button[title*=\"Flash\"] {\n  background: rgba(255, 255, 255, 0.1) !important;\n  backdrop-filter: blur(10px);\n  border: 1px solid rgba(255, 255, 255, 0.2) !important;\n}\n\n/* Camera selection dropdown */\n#qr-scanner-region select {\n  background: rgba(0, 0, 0, 0.8) !important;\n  color: white !important;\n  border: 1px solid rgba(212, 175, 55, 0.5) !important;\n  border-radius: 6px !important;\n  padding: 6px 12px !important;\n  font-size: 14px !important;\n}\n\n/* Scanner region text */\n#qr-scanner-region span,\n#qr-scanner-region div {\n  color: white !important;\n  font-family: inherit !important;\n}\n\n/* Hide default file input styling */\n#qr-scanner-region input[type=\"file\"] {\n  background: rgba(212, 175, 55, 0.9) !important;\n  color: white !important;\n  border: none !important;\n  border-radius: 8px !important;\n  padding: 8px 16px !important;\n  cursor: pointer !important;\n}\n\n/* Scanner status text */\n#qr-scanner-region .qr-scanner-status {\n  background: rgba(0, 0, 0, 0.7) !important;\n  backdrop-filter: blur(10px);\n  border-radius: 8px !important;\n  padding: 8px 12px !important;\n  margin: 8px !important;\n}\n\n/* Permission request styling */\n#qr-scanner-region .qr-scanner-permission {\n  background: rgba(212, 175, 55, 0.1) !important;\n  border: 1px solid rgba(212, 175, 55, 0.3) !important;\n  border-radius: 12px !important;\n  padding: 20px !important;\n  text-align: center !important;\n}\n\n/* Error messages */\n#qr-scanner-region .qr-scanner-error {\n  background: rgba(239, 68, 68, 0.1) !important;\n  border: 1px solid rgba(239, 68, 68, 0.3) !important;\n  border-radius: 8px !important;\n  padding: 12px !important;\n  color: #ef4444 !important;\n}\n\n/* Success messages */\n#qr-scanner-region .qr-scanner-success {\n  background: rgba(34, 197, 94, 0.1) !important;\n  border: 1px solid rgba(34, 197, 94, 0.3) !important;\n  border-radius: 8px !important;\n  padding: 12px !important;\n  color: #22c55e !important;\n}\n\n/* Loading spinner */\n#qr-scanner-region .qr-scanner-loading {\n  display: flex !important;\n  align-items: center !important;\n  justify-content: center !important;\n  padding: 20px !important;\n}\n\n/* Responsive adjustments */\n@media (max-width: 640px) {\n  #qr-scanner-region {\n    border-radius: 8px;\n  }\n  \n  #qr-scanner-region button {\n    padding: 6px 12px !important;\n    font-size: 14px !important;\n  }\n  \n  #qr-scanner-region select {\n    font-size: 12px !important;\n    padding: 4px 8px !important;\n  }\n}\n\n/* Dark mode adjustments */\n@media (prefers-color-scheme: dark) {\n  #qr-scanner-region {\n    background: #0a0a0a;\n  }\n  \n  #qr-scanner-region span,\n  #qr-scanner-region div {\n    color: #f5f5f5 !important;\n  }\n}\n\n/* Animation for scanner frame */\n@keyframes qr-scanner-pulse {\n  0% {\n    box-shadow: 0 0 0 0 rgba(212, 175, 55, 0.7);\n  }\n  70% {\n    box-shadow: 0 0 0 10px rgba(212, 175, 55, 0);\n  }\n  100% {\n    box-shadow: 0 0 0 0 rgba(212, 175, 55, 0);\n  }\n}\n\n#qr-scanner-region .qr-scanner-active {\n  animation: qr-scanner-pulse 2s infinite;\n}\n"], "names": [], "mappings": "AAGA;;;;;;AAOA;;;;;;;AAQA;;;;AAKA;;;;;;;;;;;AAWA;;;;;AAMA;;;;;;AAUA;;;;;;;;;AAUA;;;;;AAOA;;;;;;;;;AAUA;;;;;;;;AASA;;;;;;;;AASA;;;;;;;;AASA;;;;;;;;AASA;;;;;;;AAQA;EACE;;;;EAIA;;;;;EAKA;;;;;;AAOF;EACE;;;;EAIA;;;;;AAOF;;;;;;;;;;;;;;AAYA", "debugId": null}}]}