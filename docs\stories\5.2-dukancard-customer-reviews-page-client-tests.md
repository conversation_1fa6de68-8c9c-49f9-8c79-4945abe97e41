---
Status: Draft
Story: |
  As a developer, I need to implement comprehensive unit and integration tests for the `ReviewsPageClient` component in the `dukancard` project. This component is responsible for rendering the UI for customer reviews, displaying the total count of reviews, and rendering the `EnhancedReviewListClient` component. The tests should ensure correct UI rendering and proper prop passing to child components.

Acceptance Criteria:
- **Initial Rendering:**
    - Given `reviewsCount` prop, when the component renders, then it correctly displays the header with the title "Your Reviews" and the formatted count of reviews (e.g., "5 reviews you've written for businesses").
    - And the `EnhancedReviewListClient` component is rendered.
- **Prop Passing to Child Components:**
    - Verify that `EnhancedReviewListClient` is rendered without any specific props (as it handles its own data fetching).

Tasks:
1. Create a new test file: `C:\web-app\dukancard\__tests__\app\(dashboard)\dashboard\customer\reviews\components\ReviewsPageClient.test.tsx`.
2. Set up a testing environment that can render React components.
3. Write unit tests for the `ReviewsPageClient` component covering all acceptance criteria.
4. Mock `EnhancedReviewListClient` to control its behavior and assert on its rendering.
5. Verify the correct display of the `reviewsCount` in the header.

File: `C:\web-app\dukancard\app\(dashboard)\dashboard\customer\reviews\components\ReviewsPageClient.tsx`
Platform: dukancard
---