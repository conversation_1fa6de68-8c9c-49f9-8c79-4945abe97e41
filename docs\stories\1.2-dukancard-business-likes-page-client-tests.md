---
Status: Draft
Story: |
  As a developer, I need to implement comprehensive unit and integration tests for the `BusinessLikesPageClient` component in the `dukancard` project. This component is responsible for rendering the UI for business likes, handling tab switching between "Likes Received" and "My Likes", managing search functionality within "My Likes", and handling pagination for both tabs. The tests should ensure correct UI rendering, state management, and interaction with its child components and the Next.js router.

Acceptance Criteria:
- **Initial Rendering:**
    - Given `initialLikesReceived`, `likesReceivedCount`, `initialMyLikes`, `myLikesCount`, `searchTerm`, and `activeTab` props, when the component renders, then it correctly displays the header, tab buttons with accurate counts, and the content for the `activeTab`.
    - And the `LikeSearch` component is rendered only when `activeTab` is 'my-likes'.
    - And the `LikePagination` component is rendered only when `totalPages` > 1.
- **Tab Switching (UI & State):**
    - Given the "Likes Received" tab is active, when the "My Likes" tab button is clicked, then `activeTab` state updates to 'my-likes', `isLoading` state becomes `true`, and the URL is updated via `router.push` with `tab=my-likes`, `page` and `search` parameters removed.
    - Given the "My Likes" tab is active, when the "Likes Received" tab button is clicked, then `activeTab` state updates to 'likes-received', `isLoading` state becomes `true`, and the URL is updated via `router.push` with `tab`, `page`, and `search` parameters removed.
    - And the `isLoading` state resets to `false` once the `useEffect` hook detects changes in `initialLikesReceived` or `initialMyLikes`.
- **Search Functionality (UI & State):**
    - Given the "My Likes" tab is active, when `onSearch` is triggered on `LikeSearch` with a new search term, then `searchTerm` state updates, `isLoading` state becomes `true`, and the URL is updated via `router.push` with the new `search` term and `page` parameter removed.
    - Given the "My Likes" tab is active and a search term is present, when the search term is cleared (empty string), then `searchTerm` state updates to empty, `isLoading` state becomes `true`, and the URL is updated via `router.push` with `search` and `page` parameters removed.
    - And the search results count display is shown correctly when `searchTerm` is present and `isLoading` is `false`.
- **Pagination (UI & State):**
    - Given `totalPages` > 1, when `onPageChange` is triggered on `LikePagination` with a new page number, then `isLoading` state becomes `true`, and the URL is updated via `router.push` with the new `page` parameter.
    - And the `isLoading` state resets to `false` once the `useEffect` hook detects changes in `initialLikesReceived` or `initialMyLikes`.
- **Loading States:**
    - Given `isLoading` is `true`, then `LikeListSkeleton` is rendered.
    - Given `isLoading` is `false`, then either `BusinessLikesReceivedList` or `BusinessMyLikesList` is rendered based on `activeTab`.
- **Child Component Interaction:**
    - Verify that `BusinessLikesReceivedList` receives `initialLikesReceived` when `activeTab` is 'likes-received'.
    - Verify that `BusinessMyLikesList` receives `initialMyLikes` when `activeTab` is 'my-likes'.

Tasks:
1. Create a new test file: `C:\web-app\dukancard\__tests__\app\(dashboard)\dashboard\business\likes\components\BusinessLikesPageClient.test.tsx`.
2. Set up a testing environment that can mock `next/navigation` hooks (`useRouter`, `useSearchParams`).
3. Write unit tests for the `BusinessLikesPageClient` component covering all acceptance criteria.
4. Simulate user interactions (button clicks, search input changes, pagination clicks).
5. Assert on component state changes, `router.push` calls with correct URL parameters, and conditional rendering of child components.
6. Ensure `isLoading` state transitions are correctly handled.

File: `C:\web-app\dukancard\app\(dashboard)\dashboard\business\likes\components\BusinessLikesPageClient.tsx`
Platform: dukancard
---