---
Status: Draft
Story: |
  As a developer, I need to implement comprehensive unit and integration tests for the `CustomerSubscriptionsPage` server component in the `dukancard` project. This page is responsible for fetching and displaying "My Subscriptions" data for a customer user, including handling authentication, profile completion checks, pagination, and search. The tests should ensure the page behaves correctly under various conditions, including successful data loads, empty states, error conditions, and proper redirection.

Acceptance Criteria:
- **Authentication & Profile Completion:**
    - Given an unauthenticated user, when accessing `/dashboard/customer/subscriptions`, then the user is redirected to `/login`.
    - Given an authenticated user with an incomplete profile, when accessing `/dashboard/customer/subscriptions`, then `requireCompleteProfile` is called.
    - Given an authenticated user with a complete profile, when accessing `/dashboard/customer/subscriptions`, then the page loads successfully.
- **Initial Data Fetching & Rendering:**
    - Given a customer user with existing subscriptions, when the page loads, then `fetchSubscriptions` is called with correct initial parameters (user ID, page 1, limit 12, empty search term).
    - And the `SubscriptionsPageClient` component receives the correct `initialSubscriptions`, `totalCount`, `currentPage`, and `searchTerm` props.
    - And the page correctly displays the `SubscriptionsPageClient`.
- **Pagination:**
    - Given there are multiple pages of data, when a user navigates to page 2 (e.g., via URL parameter), then `fetchSubscriptions` is called with `page: 2`.
- **Search Functionality:**
    - Given a user enters a search term, then `fetchSubscriptions` is called with the correct `searchTerm`.
    - And pagination is reset to page 1 when a search term is applied.
    - Given a search term is applied, when the search term is cleared, then `fetchSubscriptions` is called with an empty `searchTerm`.
    *   And pagination is reset to page 1 when the search term is cleared.
- **Loading States:**
    - Given the page is loading data, then the `SubscriptionListSkeleton` is displayed within the `Suspense` fallback.
    - Given the page has finished loading, then the `SubscriptionListSkeleton` is no longer displayed.
- **Error Handling:**
    - Given `fetchSubscriptions` throws an error, then an `Alert` component with `variant="destructive"` is displayed, showing an appropriate error message.
    - And the page does not crash.
- **Edge Cases:**
    - Given no subscription data exists, then the list is empty and appropriate empty states are displayed (handled by client components).
    - Given `searchParams` are missing or invalid, then default values are used (page 1, empty search term).

Tasks:
1. Create a new test file: `C:\web-app\dukancard\__tests__\app\(dashboard)\dashboard\customer\subscriptions\page.test.tsx`.
2. Set up a testing environment that can mock Supabase server-side functions (`createClient`, `supabase.auth.getUser`), `next/navigation` (`redirect`), and `@/lib/actions/customerProfiles/addressValidation` (`requireCompleteProfile`).
3. Write unit tests for the `CustomerSubscriptionsPage` component covering all acceptance criteria.
4. Ensure tests use `render` from `@testing-library/react` or similar for component rendering.
5. Mock the `fetchSubscriptions` function to control data responses (success, empty, error).
6. Simulate `searchParams` changes to test pagination and search.
7. Verify redirects for unauthenticated users.
8. Verify `requireCompleteProfile` is called.
9. Verify correct data is passed to `SubscriptionsPageClient`.
10. Verify error message display on data fetching failures.
11. Ensure the `Suspense` fallback (skeleton) is shown during loading.

File: `C:\web-app\dukancard\app\(dashboard)\dashboard\customer\subscriptions\page.tsx`
Platform: dukancard
---