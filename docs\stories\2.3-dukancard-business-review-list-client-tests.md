---
Status: Draft
Story: |
  As a developer, I need to implement comprehensive unit and integration tests for the `BusinessReviewListClient` component in the `dukancard` project. This component is responsible for fetching, displaying, sorting, and paginating reviews received by a business. It should handle various data states (loading, error, empty) and correctly render `ReviewCard` components with appropriate props.

Acceptance Criteria:
- **Initial Data Fetching & Rendering:**
    - Given a `businessProfileId`, when the component mounts, then `fetchBusinessReviewsReceived` is called with `businessProfileId`, default page (1), default limit (10), and default sort (`newest`).
    - Given `fetchBusinessReviewsReceived` returns data, then `ReviewCard` components are rendered for each review, with `onDeleteSuccess={null}` and `isReviewsReceivedTab={true}`.
    - Given `fetchBusinessReviewsReceived` returns an empty list, then the empty state message "No reviews received yet" is displayed.
- **Loading States:**
    - Given the component is fetching data, then `ReviewCardSkeleton` components are displayed.
    - Given the component has finished fetching data, then `ReviewCardSkeleton` components are no longer displayed.
- **Error Handling:**
    - Given `fetchBusinessReviewsReceived` throws an error, then an `Alert` component with `variant="destructive"` is displayed, showing an appropriate error message.
    - And the component does not crash.
- **Sorting Functionality:**
    - Given a user selects a new sort option from `ReviewSortDropdown`, then `sortBy` state updates, `fetchBusinessReviewsReceived` is called with the new sort option and `currentPage` reset to 1.
    - And the displayed reviews are re-rendered according to the new sort order.
- **Pagination Functionality:**
    - Given `pagination.totalPages` > 1, when a user clicks on a pagination link (e.g., next, previous, specific page number), then `handlePageChange` is called with the correct page number.
    - And `fetchBusinessReviewsReceived` is called with the new page number.
    - And the displayed reviews are updated accordingly.
    - And `window.scrollTo({ top: 0, behavior: 'smooth' })` is called on page change.
    - And pagination controls (Previous, Next, page numbers, ellipses) are rendered correctly based on `currentPage` and `totalPages`.
    - And Previous/Next buttons are disabled when on the first/last page respectively.
- **ReviewCard Props:**
    - Ensure `ReviewCard` receives a `review` object where `business_profiles` is correctly constructed with `reviewer_name`, `reviewer_avatar`, and `reviewer_slug` based on `reviewer_type`.

Tasks:
1. Create a new test file: `C:\web-app\dukancard\__tests__\app\(dashboard)\dashboard\business\reviews\components\BusinessReviewListClient.test.tsx`.
2. Set up a testing environment that can render React components and mock `fetchBusinessReviewsReceived`.
3. Write unit tests for the `BusinessReviewListClient` component covering all acceptance criteria.
4. Mock `ReviewSortDropdown`, `Pagination`, and `ReviewCard` components to control their behavior and assert on their props.
5. Simulate user interactions (sort selection, pagination clicks).
6. Test loading, error, and empty states.
7. Verify `fetchBusinessReviewsReceived` calls with correct parameters for sorting and pagination.
8. Verify `window.scrollTo` is called on page change.

File: `C:\web-app\dukancard\app\(dashboard)\dashboard\business\reviews\components\BusinessReviewListClient.tsx`
Platform: dukancard
---