---
Status: Draft
Story: |
  As a developer, I need to implement comprehensive unit and integration tests for the `BusinessReviewsPageClient` component in the `dukancard` project. This component is responsible for rendering the UI for business reviews, handling tab switching between "Reviews Received" and "My Reviews", and conditionally rendering the appropriate list component (`BusinessReviewListClient` or `BusinessMyReviewListClient`). The tests should ensure correct UI rendering, state management for tab switching, and proper passing of props to child components.

Acceptance Criteria:
- **Initial Rendering:**
    - Given `businessProfileId`, `reviewsReceivedCount`, and `myReviewsCount` props, when the component renders, then it correctly displays the header, tab buttons with accurate counts, and the "Reviews Received" tab content by default.
    - And `BusinessReviewListClient` is rendered with `businessProfileId`.
    - And `BusinessMyReviewListClient` is not rendered.
- **Tab Switching (UI & State):**
    - Given the "Reviews Received" tab is active, when the "My Reviews" tab button is clicked, then `activeTab` state updates to 'given'.
    - And `BusinessMyReviewListClient` is rendered.
    - And `BusinessReviewListClient` is not rendered.
    - Given the "My Reviews" tab is active, when the "Reviews Received" tab button is clicked, then `activeTab` state updates to 'received'.
    - And `BusinessReviewListClient` is rendered.
    - And `BusinessMyReviewListClient` is not rendered.
- **Prop Passing to Child Components:**
    - Verify that `BusinessReviewListClient` receives the correct `businessProfileId` prop when the "Reviews Received" tab is active.
    - Verify that `BusinessMyReviewListClient` is rendered without any specific props (as it fetches its own data) when the "My Reviews" tab is active.

Tasks:
1. Create a new test file: `C:\web-app\dukancard\__tests__\app\(dashboard)\dashboard\business\reviews\components\BusinessReviewsPageClient.test.tsx`.
2. Set up a testing environment that can render React components.
3. Write unit tests for the `BusinessReviewsPageClient` component covering all acceptance criteria.
4. Simulate user interactions (tab button clicks).
5. Assert on component state changes and conditional rendering of child components.
6. Mock `BusinessReviewListClient` and `BusinessMyReviewListClient` to verify their rendering without testing their internal logic.

File: `C:\web-app\dukancard\app\(dashboard)\dashboard\business\reviews\components\BusinessReviewsPageClient.tsx`
Platform: dukancard
---