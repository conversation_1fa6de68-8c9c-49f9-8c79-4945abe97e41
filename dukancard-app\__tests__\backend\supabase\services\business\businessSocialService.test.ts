import { describe, it, expect, jest, beforeEach, afterEach } from '@jest/globals';
import { supabase } from '@/lib/supabase';
import {
  fetchBusinessLikesReceived,
  fetchBusinessMyLikes,
  fetchBusinessFollowers,
  fetchBusinessFollowing,
  fetchBusinessReviewsReceived,
  businessSocialService
} from '@/backend/supabase/services/business/businessSocialService';

// Mock the Supabase client
jest.mock('@/lib/supabase', () => ({
  supabase: {
    from: jest.fn().mockReturnThis(),
    select: jest.fn().mockReturnThis(),
    eq: jest.fn().mockReturnThis(),
    ilike: jest.fn().mockReturnThis(),
    order: jest.fn().mockReturnThis(),
    range: jest.fn().mockReturnThis(),
    delete: jest.fn().mockReturnThis(),
    update: jest.fn().mockReturnThis(),
    in: jest.fn().mockReturnThis(),
  },
}));

// Mock constants
jest.mock('@/src/config/supabase/constants', () => ({
  TABLES: {
    SUBSCRIPTIONS: 'subscriptions',
    BUSINESS_PROFILES: 'business_profiles',
    LIKES: 'likes',
    RATINGS_REVIEWS: 'ratings_reviews',
    CUSTOMER_PROFILES: 'customer_profiles',
  },
  COLUMNS: {
    ID: 'id',
    USER_ID: 'user_id',
    BUSINESS_PROFILE_ID: 'business_profile_id',
    BUSINESS_NAME: 'business_name',
    CREATED_AT: 'created_at',
    RATING: 'rating',
    REVIEW_TEXT: 'review_text',
    NAME: 'name',
    EMAIL: 'email',
    AVATAR_URL: 'avatar_url',
    BUSINESS_SLUG: 'business_slug',
    LOGO_URL: 'logo_url',
    CITY: 'city',
    STATE: 'state',
    PINCODE: 'pincode',
    ADDRESS_LINE: 'address_line',
    UPDATED_AT: 'updated_at',
  },
}));

describe('businessSocialService', () => {
  let mockSupabase: any;

  beforeEach(() => {
    mockSupabase = supabase;
    jest.clearAllMocks();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('fetchBusinessLikesReceived', () => {
    it('should fetch likes received by business with pagination', async () => {
      const mockLikes = [
        { id: '1', user_id: 'user1' },
        { id: '2', user_id: 'user2' },
      ];

      const mockCustomerProfiles = [
        { id: 'user1', name: 'John Doe', email: '<EMAIL>', avatar_url: null },
      ];

      const mockBusinessProfiles = [
        { id: 'user2', business_name: 'Test Business', business_slug: 'test-business' },
      ];

      // Mock count query
      mockSupabase.select.mockResolvedValueOnce({
        count: 10,
        error: null,
      });

      // Mock likes query
      mockSupabase.select.mockResolvedValueOnce({
        data: mockLikes,
        error: null,
      });

      // Mock customer profiles query
      mockSupabase.select.mockResolvedValueOnce({
        data: mockCustomerProfiles,
        error: null,
      });

      // Mock business profiles query
      mockSupabase.select.mockResolvedValueOnce({
        data: mockBusinessProfiles,
        error: null,
      });

      const result = await fetchBusinessLikesReceived(
        'business1',
        1,
        10
      );

      expect(result.totalCount).toBe(10);
      expect(result.hasMore).toBe(false);
      expect(result.items).toHaveLength(2);
      expect(result.items[0]).toMatchObject({
        id: '1',
        user_id: 'user1',
        profile_type: 'customer',
        customer_profiles: mockCustomerProfiles[0],
      });
    });

    it('should handle errors in fetchBusinessLikesReceived', async () => {
      mockSupabase.select.mockResolvedValueOnce({
        count: null,
        error: { message: 'Database error' },
      });

      await expect(
        fetchBusinessLikesReceived('business1', 1, 10)
      ).rejects.toThrow('Database error');
    });
  });

  describe('fetchBusinessMyLikes', () => {
    it('should fetch likes given by business with pagination', async () => {
      const mockData = [
        {
          id: '1',
          business_profiles: {
            id: 'business1',
            business_name: 'Test Business',
          },
        },
      ];

      mockSupabase.select.mockResolvedValueOnce({
        data: mockData,
        count: 1,
        error: null,
      });

      const result = await businessSocialService.fetchBusinessMyLikes(
        'business1',
        1,
        10,
        'newest'
      );

      expect(result.items).toEqual(mockData);
      expect(result.totalCount).toBe(1);
      expect(result.hasMore).toBe(false);
      expect(result.currentPage).toBe(1);
    });
  });

  describe('fetchBusinessFollowers', () => {
    it('should fetch business followers with pagination', async () => {
      const mockSubscriptions = [
        { id: '1', user_id: 'user1', created_at: '2024-01-01T00:00:00Z' },
        { id: '2', user_id: 'user2', created_at: '2024-01-02T00:00:00Z' },
      ];

      const mockCustomerProfiles = [
        { id: 'user1', name: 'John Doe', avatar_url: null },
      ];

      const mockBusinessProfiles = [
        { id: 'user2', business_name: 'Test Business', business_slug: 'test-business' },
      ];

      // Mock count query
      mockSupabase.select.mockResolvedValueOnce({
        count: 5,
        error: null,
      });

      // Mock subscriptions query
      mockSupabase.select.mockResolvedValueOnce({
        data: mockSubscriptions,
        error: null,
      });

      // Mock customer profiles query
      mockSupabase.select.mockResolvedValueOnce({
        data: mockCustomerProfiles,
        error: null,
      });

      // Mock business profiles query
      mockSupabase.select.mockResolvedValueOnce({
        data: mockBusinessProfiles,
        error: null,
      });

      const result = await businessSocialService.fetchBusinessFollowers(
        'business1',
        1,
        10
      );

      expect(result.totalCount).toBe(5);
      expect(result.hasMore).toBe(false);
      expect(result.items).toHaveLength(2);
      expect(result.items[0]).toMatchObject({
        id: '1',
        user_id: 'user1',
        profile_type: 'customer',
        customer_profiles: mockCustomerProfiles[0],
      });
    });
  });

  describe('fetchBusinessFollowing', () => {
    it('should fetch businesses that this business is following', async () => {
      const mockData = [
        {
          id: '1',
          business_profiles: {
            id: 'business1',
            business_name: 'Test Business',
          },
        },
      ];

      mockSupabase.select.mockResolvedValueOnce({
        data: mockData,
        count: 1,
        error: null,
      });

      const result = await businessSocialService.fetchBusinessFollowing(
        'business1',
        1,
        10,
        'newest'
      );

      expect(result.items).toEqual(mockData);
      expect(result.totalCount).toBe(1);
      expect(result.hasMore).toBe(false);
      expect(result.currentPage).toBe(1);
    });
  });

  describe('fetchBusinessReviewsReceived', () => {
    it('should fetch reviews received by business with pagination', async () => {
      const mockReviews = [
        {
          id: '1',
          rating: 5,
          review_text: 'Great service!',
          created_at: '2024-01-01T00:00:00Z',
          user_id: 'user1',
        },
      ];

      const mockCustomerProfiles = [
        { id: 'user1', name: 'John Doe', email: '<EMAIL>', avatar_url: null },
      ];

      // Mock count query
      mockSupabase.select.mockResolvedValueOnce({
        count: 3,
        error: null,
      });

      // Mock reviews query
      mockSupabase.select.mockResolvedValueOnce({
        data: mockReviews,
        error: null,
      });

      // Mock customer profiles query
      mockSupabase.select.mockResolvedValueOnce({
        data: mockCustomerProfiles,
        error: null,
      });

      // Mock business profiles query (empty)
      mockSupabase.select.mockResolvedValueOnce({
        data: [],
        error: null,
      });

      const result = await businessSocialService.fetchBusinessReviewsReceived(
        'business1',
        1,
        10,
        'newest'
      );

      expect(result.totalCount).toBe(3);
      expect(result.hasMore).toBe(false);
      expect(result.items).toHaveLength(1);
      expect(result.items[0]).toMatchObject({
        id: '1',
        rating: 5,
        review_text: 'Great service!',
        profile_type: 'customer',
        customer_profiles: mockCustomerProfiles[0],
      });
    });

    it('should handle different sort options', async () => {
      mockSupabase.select.mockResolvedValueOnce({
        count: 0,
        error: null,
      });

      mockSupabase.select.mockResolvedValueOnce({
        data: [],
        error: null,
      });

      await businessSocialService.fetchBusinessReviewsReceived(
        'business1',
        1,
        10,
        'rating_high'
      );

      expect(mockSupabase.order).toHaveBeenCalledWith('rating', { ascending: false });
    });
  });
});
