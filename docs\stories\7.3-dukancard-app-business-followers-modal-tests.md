---
Status: Draft
Story: |
  As a developer, I need to implement comprehensive unit and integration tests for the `BusinessFollowersModal` component in the `dukancard-app` project. This modal is responsible for displaying a business's followers and the businesses it is following, allowing users to switch between these views, search within the "Following" list, and handle loading, empty, and error states. The tests should ensure correct UI rendering, state management for tab switching and search, and proper interaction with its child list components.

Acceptance Criteria:
- **Initial Rendering:**
    - Given `visible` is `true`, when the modal renders, then it displays the header with title "Followers", a close button, and two tabs: "Followers" and "Following".
    - The "Followers" tab is active by default.
    - `BusinessFollowersList` is rendered with `businessId` and `searchTerm` (empty initially).
    - `BusinessFollowingList` is not rendered.
- **Modal Visibility:**
    - Given `visible` is `false`, the modal is not rendered.
    - When the close button is pressed, `onClose` is called.
- **Tab Switching (UI & State):**
    - When the "Following" tab is pressed, `activeTab` state changes to 'following'.
    - `BusinessFollowingList` is rendered with `searchTerm` (empty initially).
    - `BusinessFollowersList` is not rendered.
    - When the "Followers" tab is pressed, `activeTab` state changes to 'followers'.
    - `BusinessFollowersList` is rendered with `businessId` and `searchTerm` (empty initially).
    - `BusinessFollowingList` is not rendered.
- **Search Functionality:**
    - The search input is rendered.
    - When text is entered into the search input, `searchTerm` state updates.
    - When the search button is pressed or `onSubmitEditing` is triggered, `activeSearchTerm` updates to `searchTerm`.
    - The `BusinessFollowersList` or `BusinessFollowingList` receives the `activeSearchTerm` as its `searchTerm` prop.
- **Child Component Interaction:**
    - Verify that `BusinessFollowersList` receives the correct `businessId` and `searchTerm` props when the "Followers" tab is active.
    - Verify that `BusinessFollowingList` receives the correct `searchTerm` prop when the "Following" tab is active.

Tasks:
1. Update the existing test file: `C:\web-app\dukancard-app\__tests__\src\components\modals\business\BusinessFollowersModal.test.tsx`.
2. Remove mocks for `BusinessFollowersList` and `BusinessFollowingList` to allow for integration testing of their rendering within the modal.
3. Write integration tests to verify:
    - Correct rendering of `BusinessFollowersList` and `BusinessFollowingList` based on `activeTab`.
    - Proper passing of `businessId` and `searchTerm` props to the list components.
    - State updates for `searchTerm` and `activeSearchTerm` upon user input and search actions.
    - The `onClose` prop is correctly called when the close button is pressed.
4. Ensure the modal's visibility is controlled by the `visible` prop.

File: `C:\web-app\dukancard-app\src\components\modals\business\BusinessFollowersModal.tsx`
Platform: dukancard-app
---