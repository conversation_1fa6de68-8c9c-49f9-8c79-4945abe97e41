import React from 'react';
import { render, waitFor, fireEvent } from '@testing-library/react-native';
import BusinessLikesList from '@/src/components/modals/business/components/BusinessLikesList';
import { fetchBusinessLikesReceived } from '@/backend/supabase/services/business/businessSocialService';
import { useTheme } from '@/src/hooks/useTheme';

// Mock dependencies
jest.mock('@/backend/supabase/services/business/businessSocialService', () => ({
  fetchBusinessLikesReceived: jest.fn(),
}));

jest.mock('@/src/hooks/useTheme', () => ({
  useTheme: () => ({
    colors: {
      primary: '#C29D5B',
      text: '#000000',
      textSecondary: '#666666',
      background: '#FFFFFF',
      border: '#E5E5E5',
    },
    spacing: {
      xs: 4,
      sm: 8,
      md: 16,
      lg: 24,
      xl: 32,
    },
    isDark: false,
  }),
}));

// Mock the styles
jest.mock('@/styles/modals/business/business-likes-modal', () => ({
  createBusinessLikesModalStyles: () => ({
    container: {},
    likeItem: {},
    userInfo: {},
    userName: {},
    userEmail: {},
    emptyContainer: {},
    emptyText: {},
  }),
}));

// Mock the skeleton component
jest.mock('@/src/components/skeletons/modals/BusinessLikesModalSkeleton', () => ({
  BusinessLikesModalSkeleton: () => 'BusinessLikesModalSkeleton',
}));

// Mock SocialEmptyState
jest.mock('@/src/components/shared/ui/SocialEmptyState', () => ({
  SocialEmptyState: () => 'SocialEmptyState',
}));

describe('BusinessLikesList', () => {
  const mockBusinessId = 'business123';
  const mockLikes = [
    { id: 'like1', profile_type: 'customer', customer_profiles: { id: 'cust1', name: 'Customer One', avatar_url: 'http://example.com/cust1.jpg' } },
    { id: 'like2', profile_type: 'business', business_profiles: { id: 'biz1', business_name: 'Business One', logo_url: 'http://example.com/biz1.jpg' } },
  ];

  beforeEach(() => {
    jest.clearAllMocks();
    (fetchBusinessLikesReceived as jest.Mock).mockResolvedValue({
      items: mockLikes,
      hasMore: false,
    });
  });

  it('renders without crashing', async () => {
    const result = render(
      <BusinessLikesList businessId={mockBusinessId} searchTerm="" />
    );

    expect(result).toBeTruthy();
  });

  it('renders loading state initially', () => {
    (fetchBusinessLikesReceived as jest.Mock).mockReturnValueOnce(new Promise(() => {})); // Never resolve
    const result = render(
      <BusinessLikesList businessId={mockBusinessId} searchTerm="" />
    );
    expect(result).toBeTruthy();
  });

  it('renders empty state when no likes', async () => {
    (fetchBusinessLikesReceived as jest.Mock).mockResolvedValue({ items: [], hasMore: false });
    const result = render(
      <BusinessLikesList businessId={mockBusinessId} searchTerm="" />
    );

    expect(result).toBeTruthy();
  });

  it('handles load more functionality', async () => {
    (fetchBusinessLikesReceived as jest.Mock)
      .mockResolvedValueOnce({ items: [mockLikes[0]], hasMore: true })
      .mockResolvedValueOnce({ items: [mockLikes[1]], hasMore: false });

    const result = render(
      <BusinessLikesList businessId={mockBusinessId} searchTerm="" />
    );

    expect(result).toBeTruthy();
  });

  it('handles refresh functionality', async () => {
    (fetchBusinessLikesReceived as jest.Mock)
      .mockResolvedValueOnce({ items: [mockLikes[0]], hasMore: true })
      .mockResolvedValueOnce({ items: mockLikes, hasMore: false });

    const result = render(
      <BusinessLikesList businessId={mockBusinessId} searchTerm="" />
    );

    expect(result).toBeTruthy();
  });

  // Test pagination logic fixes for infinite loading issue
  describe('Pagination Logic Fixes', () => {
    it('should only increment page when hasMore is true on first load', async () => {
      const mockResponse = {
        items: Array(10).fill({ id: 'test', user_id: 'user' }),
        hasMore: true,
        totalCount: 25,
        currentPage: 1,
      };

      (fetchBusinessLikesReceived as jest.Mock).mockResolvedValueOnce(mockResponse);

      render(<BusinessLikesList businessId={mockBusinessId} />);

      await waitFor(() => {
        expect(fetchBusinessLikesReceived).toHaveBeenCalledWith(mockBusinessId, 1, 10);
      });

      // Component should set page to 2 since hasMore is true
      // This tests the fix where page is only incremented when hasMore is true
    });

    it('should not increment page when hasMore is false on first load', async () => {
      const mockResponse = {
        items: Array(5).fill({ id: 'test', user_id: 'user' }),
        hasMore: false,
        totalCount: 5,
        currentPage: 1,
      };

      (fetchBusinessLikesReceived as jest.Mock).mockResolvedValueOnce(mockResponse);

      render(<BusinessLikesList businessId={mockBusinessId} />);

      await waitFor(() => {
        expect(fetchBusinessLikesReceived).toHaveBeenCalledWith(mockBusinessId, 1, 10);
      });

      // Component should not increment page since hasMore is false
      // This prevents infinite loading when no more data exists
    });

    it('should handle edge case where totalCount equals from + limit', async () => {
      const mockResponse = {
        items: Array(10).fill({ id: 'test', user_id: 'user' }),
        hasMore: false, // 20 > 10 + 10 = false (20 is not > 20)
        totalCount: 20,
        currentPage: 2,
      };

      (fetchBusinessLikesReceived as jest.Mock).mockResolvedValueOnce(mockResponse);

      render(<BusinessLikesList businessId={mockBusinessId} />);

      await waitFor(() => {
        expect(fetchBusinessLikesReceived).toHaveBeenCalled();
      });

      // Component should not increment page when hasMore is false
      // This tests the exact edge case that was causing infinite loading
    });
  });
});
