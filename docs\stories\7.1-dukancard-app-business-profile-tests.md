---
Status: Draft
Story: |
  As a developer, I need to enhance the unit and integration tests for the `BusinessProfileScreen` component in the `dukancard-app` project. This screen serves as the main profile page for business users, displaying their profile information, statistics, and providing navigation to various management and social features, including the likes, followers, and reviews modals. The tests should ensure correct data fetching, rendering of profile details and menu items, proper modal visibility toggling, and robust error handling.

Acceptance Criteria:
- **Initial Data Fetching & Rendering:**
    - Given an authenticated business user, when the screen loads, then `getBusinessProfile` is called with the user's ID.
    - Given `getBusinessProfile` returns data, then the `ProfileHeader` is rendered with the correct `avatarUrl`, `profileName`, `likesCount`, `followersCount`, `reviewsOrRatingsCount`, and `isBusinessProfile` props.
    - And all main menu items ("Show My Digital Card", "Manage Card", "Manage Products/Services", "Gallery", "Analytics", "Manage Plan", "App Theme", "Logout") are rendered.
- **Loading States:**
    - Given the screen is loading profile data, then `ProfileSkeleton` is displayed.
    - Given the screen has finished loading, then `ProfileSkeleton` is no longer displayed.
- **Error Handling:**
    - Given `getBusinessProfile` throws an error, then `ErrorState` is displayed with an appropriate error message.
    - And the screen does not crash.
- **Redirection:**
    - Given an unauthenticated user, when accessing the screen, then the user is redirected to the login page.
    - Given an authenticated user who is a customer (not a business), when accessing the screen, then the user is redirected to the customer dashboard.
- **Modal Interactions (Likes, Followers, Reviews):**
    - When the "Likes" stat in `ProfileHeader` is pressed, then `BusinessLikesModal` becomes visible.
    - When the "Followers" stat in `ProfileHeader` is pressed, then `BusinessFollowersModal` becomes visible.
    - When the "Reviews" stat in `ProfileHeader` is pressed, then `BusinessReviewsModal` becomes visible.
    - When any of these social modals are visible and their `onClose` prop is called, then the respective modal becomes invisible.
- **Other Modal Interactions:**
    - When "Show My Digital Card" is pressed, then `ShareBusinessCardModal` becomes visible.
    - When "Manage Card" is pressed, then `ManageCardModal` becomes visible.
    - When "Manage Products/Services" is pressed, then `ManageProductsModal` becomes visible.
    - When "Gallery", "Analytics", or "Manage Plan" are pressed, then `ComingSoonModal` becomes visible with the correct `featureName` and `description`.
- **Logout Functionality:**
    - When the "Logout" button is pressed, then `signOut` is called.
    - And the `ActivityIndicator` is shown while logging out.
- **Refresh Control:**
    - When the `ScrollView` is pulled to refresh, then `handleRefresh` is called, which re-fetches the business profile.

Tasks:
1. Update the existing test file: `C:\web-app\dukancard-app\__tests__\app\(dashboard)\business\profile.test.tsx`.
2. Remove mocks for `BusinessLikesModal`, `BusinessFollowersModal`, `BusinessReviewsModal`, `ShareBusinessCardModal`, `ManageCardModal`, `ManageProductsModal`, and `ComingSoonModal`.
3. Write integration tests to verify that these modals become visible and invisible upon interaction with the `ProfileHeader` stats and menu items.
4. Ensure that the correct `businessId` is passed to the social modals.
5. Expand tests for `ProfileHeader` to ensure correct props are passed based on `businessProfile` data.
6. Add tests for `RefreshControl` to ensure data re-fetching on pull-to-refresh.
7. Verify the `isLoggingOut` state correctly controls the `ActivityIndicator` and button disabled state.

File: `C:\web-app\dukancard-app\app\(dashboard)\business\profile.tsx`
Platform: dukancard-app
---