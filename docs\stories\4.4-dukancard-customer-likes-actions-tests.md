---
Status: Draft
Story: |
  As a developer, I need to implement comprehensive unit tests for the `actions.ts` file within the `dukancard` project's customer likes module. This file contains the server action `fetchCustomerLikes`, which is responsible for fetching liked businesses data from the `likesService`. The tests should ensure this action correctly interacts with the `likesService`, handles various data scenarios (including pagination and search), and gracefully manages service-level errors.

Acceptance Criteria:
- **`fetchCustomerLikes` Functionality:**
    - Given a valid `userId`, `page`, `limit`, and `searchTerm`, when `fetchCustomerLikes` is called, then `likesService.fetchLikes` is invoked with the correct parameters.
    - Given `likesService.fetchLikes` returns data, then `fetchCustomerLikes` returns the data correctly, including `items`, `totalCount`, `hasMore`, and `currentPage`.
    - Given `likesService.fetchLikes` returns an empty array, then `fetchCustomerLikes` returns an empty `items` array, `totalCount` of 0, `hasMore` as `false`, and `currentPage` as the requested page.
    - Given `likesService.fetchLikes` throws an error, then `fetchCustomer<PERSON>ikes` catches the error and re-throws it.
    - Given `searchTerm` is provided, then `likesService.fetchLikes` is called with the correct `searchTerm`.
    - Given `page` and `limit` are provided, then `likesService.fetchLikes` is called with the correct pagination parameters.

Tasks:
1. Create a new test file: `C:\web-app\dukancard\__tests__\app\(dashboard)\dashboard\customer\likes\actions.test.ts`.
2. Set up a testing environment that can mock `likesService`.
3. Write unit tests for `fetchCustomerLikes` covering:
    - Successful data retrieval.
    - Error handling when `likesService.fetchLikes` throws an error.
    - Correct parameter passing (userId, page, limit, searchTerm).
    - Empty data responses from `likesService`.

File: `C:\web-app\dukancard\app\(dashboard)\dashboard\customer\likes\actions.ts`
Platform: dukancard
---