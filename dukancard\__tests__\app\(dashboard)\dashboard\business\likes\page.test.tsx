import { describe, it, expect, jest, beforeEach, afterEach } from '@jest/globals';
import { render, screen } from '@testing-library/react';
import { redirect } from 'next/navigation';
import BusinessLikesPage from '@/app/(dashboard)/dashboard/business/likes/page';
import { fetchBusinessLikesReceived, fetchBusinessMyLikes } from '@/app/(dashboard)/dashboard/business/likes/actions';

// Add jest-dom matchers
import '@testing-library/jest-dom';

// Mock Next.js navigation
jest.mock('next/navigation', () => ({
  redirect: jest.fn(),
}));

// Mock the actions
const mockFetchBusinessLikesReceived = jest.fn();
const mockFetchBusinessMyLikes = jest.fn();
jest.mock('@/app/(dashboard)/dashboard/business/likes/actions', () => ({
  fetchBusinessLikesReceived: mockFetchBusinessLikesReceived,
  fetchBusinessMyLikes: mockFetchBusinessMyLikes,
}));

// Mock environment variables
process.env.NEXT_PUBLIC_SUPABASE_URL = 'https://test.supabase.co';
process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY = 'test-anon-key';

// Mock Supabase server client
const mockCreateClient = jest.fn();
jest.mock('@/utils/supabase/server', () => ({
  createClient: mockCreateClient,
}));

// Mock Supabase SSR
jest.mock('@supabase/ssr', () => ({
  createServerClient: jest.fn(),
}));

// Mock the client component
jest.mock('@/app/(dashboard)/dashboard/business/likes/components/BusinessLikesPageClient', () => {
  return function MockBusinessLikesPageClient(props: any) {
    return (
      <div data-testid="business-likes-page-client">
        <div data-testid="initial-likes-received">{JSON.stringify(props.initialLikesReceived)}</div>
        <div data-testid="likes-received-count">{props.likesReceivedCount}</div>
        <div data-testid="likes-received-current-page">{props.likesReceivedCurrentPage}</div>
        <div data-testid="initial-my-likes">{JSON.stringify(props.initialMyLikes)}</div>
        <div data-testid="my-likes-count">{props.myLikesCount}</div>
        <div data-testid="my-likes-current-page">{props.myLikesCurrentPage}</div>
        <div data-testid="search-term">{props.searchTerm}</div>
        <div data-testid="active-tab">{props.activeTab}</div>
      </div>
    );
  };
});

// Mock the LikeListSkeleton component
jest.mock('@/app/components/shared/likes', () => ({
  LikeListSkeleton: function MockLikeListSkeleton() {
    return <div data-testid="like-list-skeleton">Loading likes...</div>;
  },
}));

// Mock UI components
jest.mock('@/components/ui/alert', () => ({
  Alert: function MockAlert({ children, variant, ...props }: any) {
    return (
      <div data-testid="alert" data-variant={variant} {...props}>
        {children}
      </div>
    );
  },
  AlertTitle: function MockAlertTitle({ children }: any) {
    return <div data-testid="alert-title">{children}</div>;
  },
  AlertDescription: function MockAlertDescription({ children }: any) {
    return <div data-testid="alert-description">{children}</div>;
  },
}));

jest.mock('@/components/ui/skeleton', () => ({
  Skeleton: function MockSkeleton({ className }: any) {
    return <div data-testid="skeleton" className={className}></div>;
  },
}));

// Mock Lucide icons
jest.mock('lucide-react', () => ({
  AlertTriangle: function MockAlertTriangle(props: any) {
    return <div data-testid="alert-triangle-icon" {...props}></div>;
  },
  Heart: function MockHeart(props: any) {
    return <div data-testid="heart-icon" {...props}></div>;
  },
}));

describe('BusinessLikesPage', () => {
  const mockUser = {
    id: 'test-user-id',
    email: '<EMAIL>',
    created_at: '2023-01-01T00:00:00.000Z',
    updated_at: '2023-01-01T00:00:00.000Z',
  };

  const mockSupabaseClient = {
    auth: {
      getUser: jest.fn(),
    },
  };

  const mockLikesReceivedData = {
    items: [
      {
        id: 'like-1',
        user_id: 'customer-1',
        profile_type: 'customer' as const,
        customer_profiles: {
          id: 'customer-1',
          name: 'John Doe',
          email: '<EMAIL>',
          avatar_url: null,
        },
        business_profiles: null,
      },
    ],
    totalCount: 15,
    hasMore: true,
    currentPage: 1,
  };

  const mockMyLikesData = {
    items: [
      {
        id: 'my-like-1',
        business_profiles: {
          id: 'business-1',
          business_name: 'Test Business',
          business_slug: 'test-business',
          logo_url: null,
          city: 'Test City',
          state: 'Test State',
          pincode: '12345',
          address_line: '123 Test St',
          locality: 'Test Locality',
        },
      },
    ],
    totalCount: 8,
    hasMore: false,
    currentPage: 1,
  };

  beforeEach(() => {
    jest.clearAllMocks();
    mockCreateClient.mockResolvedValue(mockSupabaseClient);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('Authentication', () => {
    it('should redirect to login when user is not authenticated (no user)', async () => {
      mockSupabaseClient.auth.getUser.mockResolvedValue({
        data: { user: null },
        error: null,
      });

      const searchParams = Promise.resolve({});
      await BusinessLikesPage({ searchParams });

      expect(redirect).toHaveBeenCalledWith('/login?message=Please log in to view your likes.');
    });

    it('should redirect to login when authentication error occurs', async () => {
      mockSupabaseClient.auth.getUser.mockResolvedValue({
        data: { user: null },
        error: { message: 'Authentication failed' },
      });

      const searchParams = Promise.resolve({});
      await BusinessLikesPage({ searchParams });

      expect(redirect).toHaveBeenCalledWith('/login?message=Please log in to view your likes.');
    });

    it('should proceed when user is authenticated', async () => {
      mockSupabaseClient.auth.getUser.mockResolvedValue({
        data: { user: mockUser },
        error: null,
      });

      mockFetchBusinessLikesReceived.mockResolvedValue(mockLikesReceivedData);
      mockFetchBusinessMyLikes.mockResolvedValue(mockMyLikesData);

      const searchParams = Promise.resolve({});
      const result = await BusinessLikesPage({ searchParams });

      expect(redirect).not.toHaveBeenCalled();
      expect(result).toBeDefined();
    });
  });

  describe('Initial Data Fetching & Rendering', () => {
    beforeEach(() => {
      mockSupabaseClient.auth.getUser.mockResolvedValue({
        data: { user: mockUser },
        error: null,
      });
    });

    it('should fetch data with correct initial parameters for likes-received tab', async () => {
      mockFetchBusinessLikesReceived.mockResolvedValue(mockLikesReceivedData);
      mockFetchBusinessMyLikes.mockResolvedValue(mockMyLikesData);

      const searchParams = Promise.resolve({});
      await BusinessLikesPage({ searchParams });

      expect(mockFetchBusinessLikesReceived).toHaveBeenCalledWith(mockUser.id, 1, 10);
      expect(mockFetchBusinessMyLikes).toHaveBeenCalledWith(mockUser.id, 1, 10, "");

      // Verify count fetching calls
      expect(mockFetchBusinessLikesReceived).toHaveBeenCalledWith(mockUser.id, 1, 1);
      expect(mockFetchBusinessMyLikes).toHaveBeenCalledWith(mockUser.id, 1, 1, "");
    });

    it('should render BusinessLikesPageClient with correct props for likes-received tab', async () => {
      (fetchBusinessLikesReceived as jest.MockedFunction<typeof fetchBusinessLikesReceived>)
        .mockResolvedValueOnce(mockLikesReceivedData)
        .mockResolvedValueOnce({ ...mockLikesReceivedData, totalCount: 15 });
      (fetchBusinessMyLikes as jest.MockedFunction<typeof fetchBusinessMyLikes>)
        .mockResolvedValueOnce(mockMyLikesData)
        .mockResolvedValueOnce({ ...mockMyLikesData, totalCount: 8 });

      const searchParams = Promise.resolve({});
      const result = await BusinessLikesPage({ searchParams });

      render(result as React.ReactElement);

      expect(screen.getByTestId('business-likes-page-client')).toBeInTheDocument();
      expect(screen.getByTestId('initial-likes-received')).toHaveTextContent(JSON.stringify(mockLikesReceivedData.items));
      expect(screen.getByTestId('likes-received-count')).toHaveTextContent('15');
      expect(screen.getByTestId('likes-received-current-page')).toHaveTextContent('1');
      expect(screen.getByTestId('initial-my-likes')).toHaveTextContent(JSON.stringify(mockMyLikesData.items));
      expect(screen.getByTestId('my-likes-count')).toHaveTextContent('8');
      expect(screen.getByTestId('my-likes-current-page')).toHaveTextContent('1');
      expect(screen.getByTestId('search-term')).toHaveTextContent('');
      expect(screen.getByTestId('active-tab')).toHaveTextContent('likes-received');
    });

    it('should handle empty data correctly', async () => {
      const emptyLikesReceived = { items: [], totalCount: 0, hasMore: false, currentPage: 1 };
      const emptyMyLikes = { items: [], totalCount: 0, hasMore: false, currentPage: 1 };

      (fetchBusinessLikesReceived as jest.MockedFunction<typeof fetchBusinessLikesReceived>)
        .mockResolvedValue(emptyLikesReceived);
      (fetchBusinessMyLikes as jest.MockedFunction<typeof fetchBusinessMyLikes>)
        .mockResolvedValue(emptyMyLikes);

      const searchParams = Promise.resolve({});
      const result = await BusinessLikesPage({ searchParams });

      render(result as React.ReactElement);

      expect(screen.getByTestId('initial-likes-received')).toHaveTextContent('[]');
      expect(screen.getByTestId('likes-received-count')).toHaveTextContent('0');
      expect(screen.getByTestId('initial-my-likes')).toHaveTextContent('[]');
      expect(screen.getByTestId('my-likes-count')).toHaveTextContent('0');
    });
  });

  describe('Tab Switching', () => {
    beforeEach(() => {
      mockSupabaseClient.auth.getUser.mockResolvedValue({
        data: { user: mockUser },
        error: null,
      });
    });

    it('should handle my-likes tab correctly', async () => {
      (fetchBusinessLikesReceived as jest.MockedFunction<typeof fetchBusinessLikesReceived>)
        .mockResolvedValue(mockLikesReceivedData);
      (fetchBusinessMyLikes as jest.MockedFunction<typeof fetchBusinessMyLikes>)
        .mockResolvedValue(mockMyLikesData);

      const searchParams = Promise.resolve({ tab: 'my-likes' });
      const result = await BusinessLikesPage({ searchParams });

      render(result as React.ReactElement);

      expect(screen.getByTestId('active-tab')).toHaveTextContent('my-likes');
      // Should fetch page 1 for likes-received (inactive tab) and current page for my-likes (active tab)
      expect(fetchBusinessLikesReceived).toHaveBeenCalledWith(mockUser.id, 1, 10);
      expect(fetchBusinessMyLikes).toHaveBeenCalledWith(mockUser.id, 1, 10, "");
    });

    it('should default to likes-received tab for invalid tab parameter', async () => {
      (fetchBusinessLikesReceived as jest.MockedFunction<typeof fetchBusinessLikesReceived>)
        .mockResolvedValue(mockLikesReceivedData);
      (fetchBusinessMyLikes as jest.MockedFunction<typeof fetchBusinessMyLikes>)
        .mockResolvedValue(mockMyLikesData);

      const searchParams = Promise.resolve({ tab: 'invalid-tab' });
      const result = await BusinessLikesPage({ searchParams });

      render(result as React.ReactElement);

      expect(screen.getByTestId('active-tab')).toHaveTextContent('likes-received');
    });

    it('should default to likes-received tab when no tab parameter', async () => {
      (fetchBusinessLikesReceived as jest.MockedFunction<typeof fetchBusinessLikesReceived>)
        .mockResolvedValue(mockLikesReceivedData);
      (fetchBusinessMyLikes as jest.MockedFunction<typeof fetchBusinessMyLikes>)
        .mockResolvedValue(mockMyLikesData);

      const searchParams = Promise.resolve({});
      const result = await BusinessLikesPage({ searchParams });

      render(result as React.ReactElement);

      expect(screen.getByTestId('active-tab')).toHaveTextContent('likes-received');
    });
  });

  describe('Pagination', () => {
    beforeEach(() => {
      mockSupabaseClient.auth.getUser.mockResolvedValue({
        data: { user: mockUser },
        error: null,
      });
    });

    it('should handle pagination for likes-received tab', async () => {
      const page2LikesReceived = { ...mockLikesReceivedData, currentPage: 2 };

      (fetchBusinessLikesReceived as jest.MockedFunction<typeof fetchBusinessLikesReceived>)
        .mockResolvedValue(page2LikesReceived);
      (fetchBusinessMyLikes as jest.MockedFunction<typeof fetchBusinessMyLikes>)
        .mockResolvedValue(mockMyLikesData);

      const searchParams = Promise.resolve({ page: '2' });
      const result = await BusinessLikesPage({ searchParams });

      render(result as React.ReactElement);

      expect(fetchBusinessLikesReceived).toHaveBeenCalledWith(mockUser.id, 2, 10);
      expect(fetchBusinessMyLikes).toHaveBeenCalledWith(mockUser.id, 1, 10, "");
      expect(screen.getByTestId('likes-received-current-page')).toHaveTextContent('2');
    });

    it('should handle pagination for my-likes tab', async () => {
      const page3MyLikes = { ...mockMyLikesData, currentPage: 3 };

      (fetchBusinessLikesReceived as jest.MockedFunction<typeof fetchBusinessLikesReceived>)
        .mockResolvedValue(mockLikesReceivedData);
      (fetchBusinessMyLikes as jest.MockedFunction<typeof fetchBusinessMyLikes>)
        .mockResolvedValue(page3MyLikes);

      const searchParams = Promise.resolve({ page: '3', tab: 'my-likes' });
      const result = await BusinessLikesPage({ searchParams });

      render(result as React.ReactElement);

      expect(fetchBusinessLikesReceived).toHaveBeenCalledWith(mockUser.id, 1, 10);
      expect(fetchBusinessMyLikes).toHaveBeenCalledWith(mockUser.id, 3, 10, "");
      expect(screen.getByTestId('my-likes-current-page')).toHaveTextContent('3');
    });

    it('should default to page 1 for invalid page parameter', async () => {
      (fetchBusinessLikesReceived as jest.MockedFunction<typeof fetchBusinessLikesReceived>)
        .mockResolvedValue(mockLikesReceivedData);
      (fetchBusinessMyLikes as jest.MockedFunction<typeof fetchBusinessMyLikes>)
        .mockResolvedValue(mockMyLikesData);

      const searchParams = Promise.resolve({ page: 'invalid' });
      const result = await BusinessLikesPage({ searchParams });

      expect(fetchBusinessLikesReceived).toHaveBeenCalledWith(mockUser.id, 1, 10);
      expect(fetchBusinessMyLikes).toHaveBeenCalledWith(mockUser.id, 1, 10, "");
    });

    it('should handle page parameter as string number correctly', async () => {
      (fetchBusinessLikesReceived as jest.MockedFunction<typeof fetchBusinessLikesReceived>)
        .mockResolvedValue({ ...mockLikesReceivedData, currentPage: 5 });
      (fetchBusinessMyLikes as jest.MockedFunction<typeof fetchBusinessMyLikes>)
        .mockResolvedValue(mockMyLikesData);

      const searchParams = Promise.resolve({ page: '5' });
      const result = await BusinessLikesPage({ searchParams });

      expect(fetchBusinessLikesReceived).toHaveBeenCalledWith(mockUser.id, 5, 10);
    });
  });

  describe('Search Functionality', () => {
    beforeEach(() => {
      mockSupabaseClient.auth.getUser.mockResolvedValue({
        data: { user: mockUser },
        error: null,
      });
    });

    it('should handle search term for my-likes tab', async () => {
      (fetchBusinessLikesReceived as jest.MockedFunction<typeof fetchBusinessLikesReceived>)
        .mockResolvedValue(mockLikesReceivedData);
      (fetchBusinessMyLikes as jest.MockedFunction<typeof fetchBusinessMyLikes>)
        .mockResolvedValue(mockMyLikesData);

      const searchParams = Promise.resolve({ search: 'test business', tab: 'my-likes' });
      const result = await BusinessLikesPage({ searchParams });

      render(result as React.ReactElement);

      expect(fetchBusinessMyLikes).toHaveBeenCalledWith(mockUser.id, 1, 10, "test business");
      expect(screen.getByTestId('search-term')).toHaveTextContent('test business');
    });

    it('should not apply search term to likes-received tab', async () => {
      (fetchBusinessLikesReceived as jest.MockedFunction<typeof fetchBusinessLikesReceived>)
        .mockResolvedValue(mockLikesReceivedData);
      (fetchBusinessMyLikes as jest.MockedFunction<typeof fetchBusinessMyLikes>)
        .mockResolvedValue(mockMyLikesData);

      const searchParams = Promise.resolve({ search: 'test business', tab: 'likes-received' });
      const result = await BusinessLikesPage({ searchParams });

      expect(fetchBusinessLikesReceived).toHaveBeenCalledWith(mockUser.id, 1, 10);
      expect(fetchBusinessMyLikes).toHaveBeenCalledWith(mockUser.id, 1, 10, "");
    });

    it('should handle empty search term', async () => {
      (fetchBusinessLikesReceived as jest.MockedFunction<typeof fetchBusinessLikesReceived>)
        .mockResolvedValue(mockLikesReceivedData);
      (fetchBusinessMyLikes as jest.MockedFunction<typeof fetchBusinessMyLikes>)
        .mockResolvedValue(mockMyLikesData);

      const searchParams = Promise.resolve({ search: '', tab: 'my-likes' });
      const result = await BusinessLikesPage({ searchParams });

      expect(fetchBusinessMyLikes).toHaveBeenCalledWith(mockUser.id, 1, 10, "");
    });

    it('should reset pagination to page 1 when search is applied', async () => {
      (fetchBusinessLikesReceived as jest.MockedFunction<typeof fetchBusinessLikesReceived>)
        .mockResolvedValue(mockLikesReceivedData);
      (fetchBusinessMyLikes as jest.MockedFunction<typeof fetchBusinessMyLikes>)
        .mockResolvedValue(mockMyLikesData);

      const searchParams = Promise.resolve({ search: 'test', page: '3', tab: 'my-likes' });
      const result = await BusinessLikesPage({ searchParams });

      // Should use page 1 for my-likes when search is applied, regardless of page param
      expect(fetchBusinessMyLikes).toHaveBeenCalledWith(mockUser.id, 1, 10, "test");
    });
  });

  describe('Error Handling', () => {
    beforeEach(() => {
      mockSupabaseClient.auth.getUser.mockResolvedValue({
        data: { user: mockUser },
        error: null,
      });
    });

    it('should display error alert when fetchBusinessLikesReceived throws error', async () => {
      (fetchBusinessLikesReceived as jest.MockedFunction<typeof fetchBusinessLikesReceived>)
        .mockRejectedValue(new Error('Failed to fetch likes received'));
      (fetchBusinessMyLikes as jest.MockedFunction<typeof fetchBusinessMyLikes>)
        .mockResolvedValue(mockMyLikesData);

      const searchParams = Promise.resolve({});
      const result = await BusinessLikesPage({ searchParams });

      render(result as React.ReactElement);

      expect(screen.getByTestId('alert')).toBeInTheDocument();
      expect(screen.getByTestId('alert')).toHaveAttribute('data-variant', 'destructive');
      expect(screen.getByTestId('alert-title')).toHaveTextContent('Error');
      expect(screen.getByTestId('alert-description')).toHaveTextContent('Could not load likes data. Please try again later.');
    });

    it('should display error alert when fetchBusinessMyLikes throws error', async () => {
      (fetchBusinessLikesReceived as jest.MockedFunction<typeof fetchBusinessLikesReceived>)
        .mockResolvedValue(mockLikesReceivedData);
      (fetchBusinessMyLikes as jest.MockedFunction<typeof fetchBusinessMyLikes>)
        .mockRejectedValue(new Error('Failed to fetch my likes'));

      const searchParams = Promise.resolve({});
      const result = await BusinessLikesPage({ searchParams });

      render(result as React.ReactElement);

      expect(screen.getByTestId('alert')).toBeInTheDocument();
      expect(screen.getByTestId('alert')).toHaveAttribute('data-variant', 'destructive');
      expect(screen.getByTestId('alert-title')).toHaveTextContent('Error');
      expect(screen.getByTestId('alert-description')).toHaveTextContent('Could not load likes data. Please try again later.');
    });

    it('should display error alert when both fetch functions throw errors', async () => {
      (fetchBusinessLikesReceived as jest.MockedFunction<typeof fetchBusinessLikesReceived>)
        .mockRejectedValue(new Error('Failed to fetch likes received'));
      (fetchBusinessMyLikes as jest.MockedFunction<typeof fetchBusinessMyLikes>)
        .mockRejectedValue(new Error('Failed to fetch my likes'));

      const searchParams = Promise.resolve({});
      const result = await BusinessLikesPage({ searchParams });

      render(result as React.ReactElement);

      expect(screen.getByTestId('alert')).toBeInTheDocument();
      expect(screen.getByTestId('alert')).toHaveAttribute('data-variant', 'destructive');
    });
  });

  describe('Edge Cases', () => {
    beforeEach(() => {
      mockSupabaseClient.auth.getUser.mockResolvedValue({
        data: { user: mockUser },
        error: null,
      });
    });

    it('should handle missing searchParams gracefully', async () => {
      (fetchBusinessLikesReceived as jest.MockedFunction<typeof fetchBusinessLikesReceived>)
        .mockResolvedValue(mockLikesReceivedData);
      (fetchBusinessMyLikes as jest.MockedFunction<typeof fetchBusinessMyLikes>)
        .mockResolvedValue(mockMyLikesData);

      const searchParams = Promise.resolve({});
      const result = await BusinessLikesPage({ searchParams });

      expect(fetchBusinessLikesReceived).toHaveBeenCalledWith(mockUser.id, 1, 10);
      expect(fetchBusinessMyLikes).toHaveBeenCalledWith(mockUser.id, 1, 10, "");
    });

    it('should handle undefined searchParams values', async () => {
      (fetchBusinessLikesReceived as jest.MockedFunction<typeof fetchBusinessLikesReceived>)
        .mockResolvedValue(mockLikesReceivedData);
      (fetchBusinessMyLikes as jest.MockedFunction<typeof fetchBusinessMyLikes>)
        .mockResolvedValue(mockMyLikesData);

      const searchParams = Promise.resolve({
        search: undefined,
        page: undefined,
        tab: undefined
      });
      const result = await BusinessLikesPage({ searchParams });

      render(result as React.ReactElement);

      expect(screen.getByTestId('search-term')).toHaveTextContent('');
      expect(screen.getByTestId('active-tab')).toHaveTextContent('likes-received');
      expect(fetchBusinessLikesReceived).toHaveBeenCalledWith(mockUser.id, 1, 10);
      expect(fetchBusinessMyLikes).toHaveBeenCalledWith(mockUser.id, 1, 10, "");
    });
  });
});
