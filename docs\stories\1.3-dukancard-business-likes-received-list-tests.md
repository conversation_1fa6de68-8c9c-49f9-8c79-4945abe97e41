---
Status: Draft
Story: |
  As a developer, I need to implement comprehensive unit and integration tests for the `BusinessLikesReceivedList` component in the `dukancard` project. This component is responsible for displaying a list of likes received by a business, handling the transformation of raw data into a format suitable for the `LikeCard` component, and rendering an appropriate empty state when no likes are present. The tests should ensure correct data mapping, proper rendering of `LikeCard` components, and accurate display of the empty state.

Acceptance Criteria:
- **Data Transformation:**
    - Given `initialLikes` containing a mix of customer and business profiles, when the component renders, then `transformedLikes` correctly maps each `like` object to a `LikeData` object, distinguishing between 'customer' and 'business' types.
    - And `slug` is `null` for customer profiles.
    - And `profile` is `null` for any `like` object where `customer_profiles` or `business_profiles` are missing.
    - And `null` profiles are filtered out from `transformedLikes`.
- **Rendering with Data:**
    - Given `initialLikes` is not empty, when the component renders, then a `div` with class `grid` is displayed.
    - And for each valid `like` in `transformedLikes`, a `LikeCard` component is rendered with the correct `likeId`, `profile`, `showUnlike={false}`, `variant="default"`, `showVisitButton={false}`, `showAddress={false}`, and `showRedirectIcon={true}` props.
    - And each `LikeCard` is wrapped in a `div` with the specified `transform transition-all duration-200 hover:scale-[1.02]` classes.
- **Empty State Rendering:**
    - Given `initialLikes` is empty, when the component renders, then the empty state message "No likes received yet" and its associated description and button are displayed.
    - And the `LikeCard` components are not rendered.
    - And the "Discover Businesses" button links to `/businesses` with `target="_blank"` and `rel="noopener noreferrer"`.
- **UI Consistency:**
    - Ensure the styling of the empty state matches the provided design (e.g., icons, text sizes, colors).

Tasks:
1. Create a new test file: `C:\web-app\dukancard\__tests__\app\(dashboard)\dashboard\business\likes\components\BusinessLikesReceivedList.test.tsx`.
2. Set up a testing environment that can render React components.
3. Write unit tests for the `BusinessLikesReceivedList` component covering all acceptance criteria.
4. Mock the `LikeCard` component to verify its props without testing its internal rendering.
5. Test rendering with various `initialLikes` data scenarios (empty, customer-only, business-only, mixed).
6. Verify the correct props are passed to `LikeCard` based on `profile_type`.
7. Assert on the presence and content of the empty state when `initialLikes` is empty.
8. Verify the `Link` component's `href`, `target`, and `rel` attributes in the empty state.

File: `C:\web-app\dukancard\app\(dashboard)\dashboard\business\likes\components\BusinessLikesReceivedList.tsx`
Platform: dukancard
---