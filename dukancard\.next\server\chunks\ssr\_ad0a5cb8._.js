module.exports = {

"[project]/lib/actions/data:b2371d [app-ssr] (ecmascript) <text/javascript>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ [{"40a7020d8c9a90386aa2f0bbb96a95966205d638a2":"unsubscribeFromBusiness"},"lib/actions/interactions.ts",""] */ __turbopack_context__.s({
    "unsubscribeFromBusiness": (()=>unsubscribeFromBusiness)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-client-wrapper.js [app-ssr] (ecmascript)");
"use turbopack no side effects";
;
var unsubscribeFromBusiness = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createServerReference"])("40a7020d8c9a90386aa2f0bbb96a95966205d638a2", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["callServer"], void 0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["findSourceMapURL"], "unsubscribeFromBusiness"); //# sourceMappingURL=data:application/json;base64,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
}}),
"[project]/app/components/shared/subscriptions/SubscriptionCard.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>SubscriptionCard)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/client/app-dir/link.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/ui/button.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$avatar$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/ui/avatar.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$user$2d$minus$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__UserMinus$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/user-minus.js [app-ssr] (ecmascript) <export default as UserMinus>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2d$circle$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Loader2$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/loader-circle.js [app-ssr] (ecmascript) <export default as Loader2>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$external$2d$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ExternalLink$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/external-link.js [app-ssr] (ecmascript) <export default as ExternalLink>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$user$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__User$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/user.js [app-ssr] (ecmascript) <export default as User>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$building$2d$2$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Building2$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/building-2.js [app-ssr] (ecmascript) <export default as Building2>");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$data$3a$b2371d__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__ = __turbopack_context__.i("[project]/lib/actions/data:b2371d [app-ssr] (ecmascript) <text/javascript>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/sonner/dist/index.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/utils.ts [app-ssr] (ecmascript)");
'use client';
;
;
;
;
;
;
;
;
;
;
function SubscriptionCard({ subscriptionId, profile, onUnsubscribeSuccess, showUnsubscribe = true, variant = 'default' }) {
    const [isLoading, setIsLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [isHovered, setIsHovered] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const handleUnsubscribe = async ()=>{
        if (!onUnsubscribeSuccess) return;
        setIsLoading(true);
        try {
            if (!profile.id) {
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].error("Cannot unsubscribe: Missing profile ID");
                return;
            }
            const result = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$data$3a$b2371d__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__["unsubscribeFromBusiness"])(profile.id);
            if (result.success) {
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].success(`Unsubscribed from ${profile.name || 'profile'}.`);
                onUnsubscribeSuccess(subscriptionId);
            } else {
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].error(`Failed to unsubscribe: ${result.error || 'Unknown error'}`);
            }
        } catch (_error) {
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].error("An unexpected error occurred");
        } finally{
            setIsLoading(false);
        }
    };
    // Format the full address
    const location = [
        profile.locality,
        profile.city,
        profile.state
    ].filter(Boolean).join(', ');
    const profileUrl = profile.type === 'business' ? `/${profile.slug}` : `/profile/${profile.slug}`;
    const imageUrl = profile.logo_url || profile.avatar_url;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["motion"].div, {
        initial: {
            opacity: 0,
            y: 10
        },
        animate: {
            opacity: 1,
            y: 0
        },
        transition: {
            duration: 0.3
        },
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])("rounded-lg border p-0 overflow-hidden transition-all duration-300", "bg-white dark:bg-black border-neutral-200 dark:border-neutral-800", isHovered ? "shadow-md transform -translate-y-1" : "shadow-sm", variant === 'compact' && "max-w-sm"),
        onMouseEnter: ()=>setIsHovered(true),
        onMouseLeave: ()=>setIsHovered(false),
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "relative",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])("w-full bg-gradient-to-r from-blue-500/20 to-[var(--brand-gold)]/20 dark:from-blue-900/30 dark:to-[var(--brand-gold)]/30", variant === 'compact' ? "h-16" : "h-20"),
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "absolute inset-0 opacity-10 dark:opacity-20 bg-repeat",
                            style: {
                                backgroundImage: `url("/decorative/card-texture.svg")`,
                                backgroundSize: "200px"
                            }
                        }, void 0, false, {
                            fileName: "[project]/app/components/shared/subscriptions/SubscriptionCard.tsx",
                            lineNumber: 108,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/app/components/shared/subscriptions/SubscriptionCard.tsx",
                        lineNumber: 101,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])("absolute left-4", variant === 'compact' ? "-bottom-4" : "-bottom-6"),
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "p-1 bg-white dark:bg-black rounded-full border-2 border-white dark:border-neutral-800",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$avatar$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Avatar"], {
                                className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])("border border-neutral-200 dark:border-neutral-700 shadow-sm", variant === 'compact' ? "h-12 w-12" : "h-16 w-16"),
                                children: [
                                    imageUrl ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$avatar$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["AvatarImage"], {
                                        src: imageUrl,
                                        alt: profile.name ?? 'Profile'
                                    }, void 0, false, {
                                        fileName: "[project]/app/components/shared/subscriptions/SubscriptionCard.tsx",
                                        lineNumber: 128,
                                        columnNumber: 17
                                    }, this) : null,
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$avatar$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["AvatarFallback"], {
                                        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])("bg-gradient-to-br from-blue-100 to-blue-200 dark:from-blue-900 dark:to-blue-800 text-blue-600 dark:text-blue-300 font-semibold", variant === 'compact' ? "text-lg" : "text-xl"),
                                        children: profile.type === 'customer' ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$user$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__User$3e$__["User"], {
                                            className: variant === 'compact' ? "h-4 w-4" : "h-6 w-6"
                                        }, void 0, false, {
                                            fileName: "[project]/app/components/shared/subscriptions/SubscriptionCard.tsx",
                                            lineNumber: 135,
                                            columnNumber: 19
                                        }, this) : profile.name?.charAt(0).toUpperCase() ?? 'P'
                                    }, void 0, false, {
                                        fileName: "[project]/app/components/shared/subscriptions/SubscriptionCard.tsx",
                                        lineNumber: 130,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/app/components/shared/subscriptions/SubscriptionCard.tsx",
                                lineNumber: 123,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/app/components/shared/subscriptions/SubscriptionCard.tsx",
                            lineNumber: 122,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/app/components/shared/subscriptions/SubscriptionCard.tsx",
                        lineNumber: 118,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/app/components/shared/subscriptions/SubscriptionCard.tsx",
                lineNumber: 99,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])("px-4 pb-4", variant === 'compact' ? "pt-6" : "pt-8"),
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "flex flex-col",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "mb-3",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                    className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])("font-semibold text-neutral-800 dark:text-neutral-200 group flex items-center gap-1", variant === 'compact' ? "text-base" : "text-lg"),
                                    children: profile.slug ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                        href: profileUrl,
                                        className: "hover:text-[var(--brand-gold)] transition-colors inline-flex items-center gap-1",
                                        target: "_blank",
                                        children: [
                                            profile.name,
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$external$2d$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ExternalLink$3e$__["ExternalLink"], {
                                                className: "h-3.5 w-3.5 opacity-70"
                                            }, void 0, false, {
                                                fileName: "[project]/app/components/shared/subscriptions/SubscriptionCard.tsx",
                                                lineNumber: 163,
                                                columnNumber: 19
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/app/components/shared/subscriptions/SubscriptionCard.tsx",
                                        lineNumber: 157,
                                        columnNumber: 17
                                    }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        children: profile.name
                                    }, void 0, false, {
                                        fileName: "[project]/app/components/shared/subscriptions/SubscriptionCard.tsx",
                                        lineNumber: 166,
                                        columnNumber: 17
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/app/components/shared/subscriptions/SubscriptionCard.tsx",
                                    lineNumber: 152,
                                    columnNumber: 13
                                }, this),
                                location && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                    className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])("text-neutral-500 dark:text-neutral-400 mt-1 flex items-center", variant === 'compact' ? "text-xs" : "text-sm"),
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                            className: "inline-block h-1 w-1 rounded-full bg-neutral-300 dark:bg-neutral-700 mr-2"
                                        }, void 0, false, {
                                            fileName: "[project]/app/components/shared/subscriptions/SubscriptionCard.tsx",
                                            lineNumber: 174,
                                            columnNumber: 17
                                        }, this),
                                        location
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/app/components/shared/subscriptions/SubscriptionCard.tsx",
                                    lineNumber: 170,
                                    columnNumber: 15
                                }, this),
                                profile.type === 'customer' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                    className: "text-xs text-blue-600 dark:text-blue-400 mt-1 flex items-center",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$user$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__User$3e$__["User"], {
                                            className: "h-3 w-3 mr-1"
                                        }, void 0, false, {
                                            fileName: "[project]/app/components/shared/subscriptions/SubscriptionCard.tsx",
                                            lineNumber: 180,
                                            columnNumber: 17
                                        }, this),
                                        "Customer"
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/app/components/shared/subscriptions/SubscriptionCard.tsx",
                                    lineNumber: 179,
                                    columnNumber: 15
                                }, this),
                                profile.type === 'business' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                    className: "text-xs text-green-600 dark:text-green-400 mt-1 flex items-center",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$building$2d$2$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Building2$3e$__["Building2"], {
                                            className: "h-3 w-3 mr-1"
                                        }, void 0, false, {
                                            fileName: "[project]/app/components/shared/subscriptions/SubscriptionCard.tsx",
                                            lineNumber: 186,
                                            columnNumber: 17
                                        }, this),
                                        "Business"
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/app/components/shared/subscriptions/SubscriptionCard.tsx",
                                    lineNumber: 185,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/app/components/shared/subscriptions/SubscriptionCard.tsx",
                            lineNumber: 151,
                            columnNumber: 11
                        }, this),
                        showUnsubscribe && onUnsubscribeSuccess && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Button"], {
                            variant: "outline",
                            size: variant === 'compact' ? "sm" : "sm",
                            onClick: handleUnsubscribe,
                            disabled: isLoading,
                            className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])("mt-2 w-full border-neutral-200 dark:border-neutral-700 transition-all duration-200", "hover:bg-red-50 hover:text-red-600 hover:border-red-200", "dark:hover:bg-red-950/30 dark:hover:text-red-400 dark:hover:border-red-900/50"),
                            children: [
                                isLoading ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2d$circle$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Loader2$3e$__["Loader2"], {
                                    className: "mr-2 h-4 w-4 animate-spin"
                                }, void 0, false, {
                                    fileName: "[project]/app/components/shared/subscriptions/SubscriptionCard.tsx",
                                    lineNumber: 206,
                                    columnNumber: 17
                                }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$user$2d$minus$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__UserMinus$3e$__["UserMinus"], {
                                    className: "mr-2 h-4 w-4"
                                }, void 0, false, {
                                    fileName: "[project]/app/components/shared/subscriptions/SubscriptionCard.tsx",
                                    lineNumber: 208,
                                    columnNumber: 17
                                }, this),
                                "Unsubscribe"
                            ]
                        }, void 0, true, {
                            fileName: "[project]/app/components/shared/subscriptions/SubscriptionCard.tsx",
                            lineNumber: 194,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/app/components/shared/subscriptions/SubscriptionCard.tsx",
                    lineNumber: 150,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/app/components/shared/subscriptions/SubscriptionCard.tsx",
                lineNumber: 146,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/app/components/shared/subscriptions/SubscriptionCard.tsx",
        lineNumber: 85,
        columnNumber: 5
    }, this);
}
}}),
"[project]/app/components/shared/subscriptions/SubscriptionCardSkeleton.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "SubscriptionListSkeleton": (()=>SubscriptionListSkeleton),
    "default": (()=>SubscriptionCardSkeleton)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$skeleton$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/ui/skeleton.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/utils.ts [app-ssr] (ecmascript)");
"use client";
;
;
;
;
function SubscriptionCardSkeleton({ index = 0, variant = 'default' }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["motion"].div, {
        initial: {
            opacity: 0,
            y: 20
        },
        animate: {
            opacity: 1,
            y: 0
        },
        transition: {
            duration: 0.4,
            delay: index * 0.05
        },
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])("rounded-lg border p-0 overflow-hidden transition-all duration-300", "bg-white dark:bg-black border-neutral-200 dark:border-neutral-800", "shadow-sm", variant === 'compact' && "max-w-sm"),
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "relative",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$skeleton$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Skeleton"], {
                        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])("w-full", variant === 'compact' ? "h-16" : "h-20")
                    }, void 0, false, {
                        fileName: "[project]/app/components/shared/subscriptions/SubscriptionCardSkeleton.tsx",
                        lineNumber: 31,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])("absolute left-4", variant === 'compact' ? "-bottom-4" : "-bottom-6"),
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "p-1 bg-white dark:bg-black rounded-full border-2 border-white dark:border-neutral-800",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$skeleton$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Skeleton"], {
                                className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])("rounded-full", variant === 'compact' ? "h-12 w-12" : "h-16 w-16")
                            }, void 0, false, {
                                fileName: "[project]/app/components/shared/subscriptions/SubscriptionCardSkeleton.tsx",
                                lineNumber: 42,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/app/components/shared/subscriptions/SubscriptionCardSkeleton.tsx",
                            lineNumber: 41,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/app/components/shared/subscriptions/SubscriptionCardSkeleton.tsx",
                        lineNumber: 37,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/app/components/shared/subscriptions/SubscriptionCardSkeleton.tsx",
                lineNumber: 29,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])("px-4 pb-4", variant === 'compact' ? "pt-6" : "pt-8"),
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "flex flex-col",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "mb-3",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$skeleton$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Skeleton"], {
                                    className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])("mb-2", variant === 'compact' ? "h-5 w-32" : "h-6 w-40")
                                }, void 0, false, {
                                    fileName: "[project]/app/components/shared/subscriptions/SubscriptionCardSkeleton.tsx",
                                    lineNumber: 58,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$skeleton$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Skeleton"], {
                                    className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])("mt-1", variant === 'compact' ? "h-3 w-24" : "h-4 w-32")
                                }, void 0, false, {
                                    fileName: "[project]/app/components/shared/subscriptions/SubscriptionCardSkeleton.tsx",
                                    lineNumber: 63,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/app/components/shared/subscriptions/SubscriptionCardSkeleton.tsx",
                            lineNumber: 56,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$skeleton$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Skeleton"], {
                            className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])("w-full mt-2 rounded-md", variant === 'compact' ? "h-8" : "h-9")
                        }, void 0, false, {
                            fileName: "[project]/app/components/shared/subscriptions/SubscriptionCardSkeleton.tsx",
                            lineNumber: 70,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/app/components/shared/subscriptions/SubscriptionCardSkeleton.tsx",
                    lineNumber: 55,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/app/components/shared/subscriptions/SubscriptionCardSkeleton.tsx",
                lineNumber: 51,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/app/components/shared/subscriptions/SubscriptionCardSkeleton.tsx",
        lineNumber: 17,
        columnNumber: 5
    }, this);
}
function SubscriptionListSkeleton({ variant = 'default', count = 6 }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6",
        children: Array.from({
            length: count
        }).map((_, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(SubscriptionCardSkeleton, {
                index: index,
                variant: variant
            }, index, false, {
                fileName: "[project]/app/components/shared/subscriptions/SubscriptionCardSkeleton.tsx",
                lineNumber: 90,
                columnNumber: 9
            }, this))
    }, void 0, false, {
        fileName: "[project]/app/components/shared/subscriptions/SubscriptionCardSkeleton.tsx",
        lineNumber: 88,
        columnNumber: 5
    }, this);
}
}}),
"[project]/app/components/shared/subscriptions/SubscriptionSearch.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>SubscriptionSearch)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$search$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Search$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/search.js [app-ssr] (ecmascript) <export default as Search>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$x$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__X$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/x.js [app-ssr] (ecmascript) <export default as X>");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$input$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/ui/input.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/ui/button.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/utils.ts [app-ssr] (ecmascript)");
"use client";
;
;
;
;
;
;
function SubscriptionSearch({ onSearch, initialSearchTerm = "", className, placeholder = "Search..." }) {
    const [searchTerm, setSearchTerm] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(initialSearchTerm);
    // Update search term when initialSearchTerm changes (e.g., when navigating back)
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        setSearchTerm(initialSearchTerm);
    }, [
        initialSearchTerm
    ]);
    // Handle input change
    const handleSearchChange = (e)=>{
        setSearchTerm(e.target.value);
    };
    // Clear search
    const handleClearSearch = ()=>{
        setSearchTerm("");
        if (typeof onSearch === 'function') {
            onSearch("");
        }
    };
    // Handle form submission
    const handleSubmit = (e)=>{
        e.preventDefault();
        if (typeof onSearch === 'function') {
            onSearch(searchTerm);
        }
    };
    // Handle Enter key press for immediate search
    const handleKeyDown = (e)=>{
        if (e.key === 'Enter') {
            e.preventDefault();
            if (typeof onSearch === 'function') {
                onSearch(searchTerm);
            }
        }
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("form", {
        onSubmit: handleSubmit,
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])("relative w-full", className),
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "relative",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$search$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Search$3e$__["Search"], {
                    className: "absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-neutral-500 dark:text-neutral-400"
                }, void 0, false, {
                    fileName: "[project]/app/components/shared/subscriptions/SubscriptionSearch.tsx",
                    lineNumber: 63,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$input$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Input"], {
                    type: "text",
                    placeholder: placeholder,
                    value: searchTerm,
                    onChange: handleSearchChange,
                    onKeyDown: handleKeyDown,
                    className: "pl-10 pr-10 h-10 bg-white dark:bg-black border-neutral-200 dark:border-neutral-800 focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-600"
                }, void 0, false, {
                    fileName: "[project]/app/components/shared/subscriptions/SubscriptionSearch.tsx",
                    lineNumber: 64,
                    columnNumber: 9
                }, this),
                searchTerm && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Button"], {
                    type: "button",
                    variant: "ghost",
                    size: "icon",
                    onClick: handleClearSearch,
                    className: "absolute right-2 top-1/2 -translate-y-1/2 h-6 w-6 p-0 text-neutral-500 hover:text-neutral-700 dark:text-neutral-400 dark:hover:text-neutral-300",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$x$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__X$3e$__["X"], {
                            className: "h-4 w-4"
                        }, void 0, false, {
                            fileName: "[project]/app/components/shared/subscriptions/SubscriptionSearch.tsx",
                            lineNumber: 80,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                            className: "sr-only",
                            children: "Clear search"
                        }, void 0, false, {
                            fileName: "[project]/app/components/shared/subscriptions/SubscriptionSearch.tsx",
                            lineNumber: 81,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/app/components/shared/subscriptions/SubscriptionSearch.tsx",
                    lineNumber: 73,
                    columnNumber: 11
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/app/components/shared/subscriptions/SubscriptionSearch.tsx",
            lineNumber: 62,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/app/components/shared/subscriptions/SubscriptionSearch.tsx",
        lineNumber: 61,
        columnNumber: 5
    }, this);
}
}}),
"[project]/app/components/shared/subscriptions/SubscriptionPagination.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>SubscriptionPagination)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/ui/button.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$chevron$2d$left$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ChevronLeft$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/chevron-left.js [app-ssr] (ecmascript) <export default as ChevronLeft>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$chevron$2d$right$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ChevronRight$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/chevron-right.js [app-ssr] (ecmascript) <export default as ChevronRight>");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/utils.ts [app-ssr] (ecmascript)");
"use client";
;
;
;
;
function SubscriptionPagination({ currentPage, totalPages, onPageChange, className }) {
    // Don't render pagination if there's only one page
    if (totalPages <= 1) return null;
    // Generate page numbers to display
    const getPageNumbers = ()=>{
        const pages = [];
        const maxPagesToShow = 5;
        if (totalPages <= maxPagesToShow) {
            // Show all pages if total is less than max
            for(let i = 1; i <= totalPages; i++){
                pages.push(i);
            }
        } else {
            // Always include first page
            pages.push(1);
            // Calculate start and end of middle section
            let startPage = Math.max(2, currentPage - 1);
            let endPage = Math.min(totalPages - 1, currentPage + 1);
            // Adjust if we're near the beginning
            if (currentPage <= 3) {
                endPage = Math.min(totalPages - 1, 4);
            }
            // Adjust if we're near the end
            if (currentPage >= totalPages - 2) {
                startPage = Math.max(2, totalPages - 3);
            }
            // Add ellipsis if needed
            if (startPage > 2) {
                pages.push(-1); // -1 represents ellipsis
            }
            // Add middle pages
            for(let i = startPage; i <= endPage; i++){
                pages.push(i);
            }
            // Add ellipsis if needed
            if (endPage < totalPages - 1) {
                pages.push(-2); // -2 represents ellipsis
            }
            // Always include last page
            pages.push(totalPages);
        }
        return pages;
    };
    const pageNumbers = getPageNumbers();
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])("flex items-center justify-center space-x-1", className),
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Button"], {
                variant: "outline",
                size: "icon",
                onClick: ()=>onPageChange(currentPage - 1),
                disabled: currentPage === 1,
                className: "h-8 w-8 p-0 border-neutral-200 dark:border-neutral-800",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$chevron$2d$left$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ChevronLeft$3e$__["ChevronLeft"], {
                        className: "h-4 w-4"
                    }, void 0, false, {
                        fileName: "[project]/app/components/shared/subscriptions/SubscriptionPagination.tsx",
                        lineNumber: 85,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                        className: "sr-only",
                        children: "Previous page"
                    }, void 0, false, {
                        fileName: "[project]/app/components/shared/subscriptions/SubscriptionPagination.tsx",
                        lineNumber: 86,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/app/components/shared/subscriptions/SubscriptionPagination.tsx",
                lineNumber: 78,
                columnNumber: 7
            }, this),
            pageNumbers.map((page, _index)=>{
                if (page < 0) {
                    // Render ellipsis
                    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                        className: "px-2 text-neutral-500 dark:text-neutral-400",
                        children: "..."
                    }, `ellipsis-${_index}`, false, {
                        fileName: "[project]/app/components/shared/subscriptions/SubscriptionPagination.tsx",
                        lineNumber: 94,
                        columnNumber: 13
                    }, this);
                }
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Button"], {
                    variant: currentPage === page ? "default" : "outline",
                    size: "sm",
                    onClick: ()=>onPageChange(page),
                    className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])("h-8 w-8 p-0", currentPage === page ? "bg-blue-500 hover:bg-blue-600 text-white border-transparent" : "border-neutral-200 dark:border-neutral-800"),
                    children: page
                }, page, false, {
                    fileName: "[project]/app/components/shared/subscriptions/SubscriptionPagination.tsx",
                    lineNumber: 101,
                    columnNumber: 11
                }, this);
            }),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Button"], {
                variant: "outline",
                size: "icon",
                onClick: ()=>onPageChange(currentPage + 1),
                disabled: currentPage === totalPages,
                className: "h-8 w-8 p-0 border-neutral-200 dark:border-neutral-800",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$chevron$2d$right$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ChevronRight$3e$__["ChevronRight"], {
                        className: "h-4 w-4"
                    }, void 0, false, {
                        fileName: "[project]/app/components/shared/subscriptions/SubscriptionPagination.tsx",
                        lineNumber: 126,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                        className: "sr-only",
                        children: "Next page"
                    }, void 0, false, {
                        fileName: "[project]/app/components/shared/subscriptions/SubscriptionPagination.tsx",
                        lineNumber: 127,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/app/components/shared/subscriptions/SubscriptionPagination.tsx",
                lineNumber: 119,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/app/components/shared/subscriptions/SubscriptionPagination.tsx",
        lineNumber: 76,
        columnNumber: 5
    }, this);
}
}}),
"[project]/app/components/shared/subscriptions/SubscriptionList.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>SubscriptionList)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/client/app-dir/link.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/ui/button.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$compass$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Compass$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/compass.js [app-ssr] (ecmascript) <export default as Compass>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$bell$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Bell$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/bell.js [app-ssr] (ecmascript) <export default as Bell>");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$components$2f$shared$2f$subscriptions$2f$SubscriptionCard$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/components/shared/subscriptions/SubscriptionCard.tsx [app-ssr] (ecmascript)");
'use client';
;
;
;
;
;
;
function SubscriptionList({ initialSubscriptions, onUnsubscribeSuccess, showUnsubscribe = true, variant = 'default', emptyMessage = "No subscriptions found.", emptyDescription = "Subscribe to profiles to see them here.", showDiscoverButton = false }) {
    const [subscriptions, setSubscriptions] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(initialSubscriptions);
    // Update subscriptions when initialSubscriptions changes
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        setSubscriptions(initialSubscriptions);
    }, [
        initialSubscriptions
    ]);
    const handleUnsubscribeSuccess = (subscriptionIdToRemove)=>{
        setSubscriptions((currentSubscriptions)=>currentSubscriptions.filter((sub)=>sub.id !== subscriptionIdToRemove));
        // Call parent callback if provided
        if (onUnsubscribeSuccess) {
            onUnsubscribeSuccess(subscriptionIdToRemove);
        }
    };
    if (subscriptions.length === 0) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "flex flex-col items-center justify-center py-20 text-center",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "relative mb-8",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "absolute -inset-8 rounded-full bg-gradient-to-r from-primary/10 to-primary/5 animate-pulse blur-xl"
                        }, void 0, false, {
                            fileName: "[project]/app/components/shared/subscriptions/SubscriptionList.tsx",
                            lineNumber: 50,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "relative z-10 flex items-center justify-center w-20 h-20 rounded-2xl bg-gradient-to-br from-primary/10 to-primary/5 border border-primary/20 shadow-lg",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$bell$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Bell$3e$__["Bell"], {
                                className: "w-10 h-10 text-primary"
                            }, void 0, false, {
                                fileName: "[project]/app/components/shared/subscriptions/SubscriptionList.tsx",
                                lineNumber: 52,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/app/components/shared/subscriptions/SubscriptionList.tsx",
                            lineNumber: 51,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/app/components/shared/subscriptions/SubscriptionList.tsx",
                    lineNumber: 49,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                    className: "text-2xl font-bold text-neutral-900 dark:text-neutral-100 mb-3",
                    children: emptyMessage
                }, void 0, false, {
                    fileName: "[project]/app/components/shared/subscriptions/SubscriptionList.tsx",
                    lineNumber: 55,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                    className: "text-lg text-neutral-600 dark:text-neutral-400 max-w-lg leading-relaxed mb-2",
                    children: emptyDescription
                }, void 0, false, {
                    fileName: "[project]/app/components/shared/subscriptions/SubscriptionList.tsx",
                    lineNumber: 58,
                    columnNumber: 9
                }, this),
                showDiscoverButton && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "mt-6",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Button"], {
                        asChild: true,
                        variant: "outline",
                        className: "gap-2 px-6 py-2.5 h-auto font-medium shadow-sm hover:shadow-md transition-all duration-200",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                            href: "/discover",
                            target: "_blank",
                            rel: "noopener noreferrer",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$compass$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Compass$3e$__["Compass"], {
                                    className: "w-4 h-4"
                                }, void 0, false, {
                                    fileName: "[project]/app/components/shared/subscriptions/SubscriptionList.tsx",
                                    lineNumber: 65,
                                    columnNumber: 17
                                }, this),
                                "Discover Businesses"
                            ]
                        }, void 0, true, {
                            fileName: "[project]/app/components/shared/subscriptions/SubscriptionList.tsx",
                            lineNumber: 64,
                            columnNumber: 15
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/app/components/shared/subscriptions/SubscriptionList.tsx",
                        lineNumber: 63,
                        columnNumber: 13
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/app/components/shared/subscriptions/SubscriptionList.tsx",
                    lineNumber: 62,
                    columnNumber: 11
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/app/components/shared/subscriptions/SubscriptionList.tsx",
            lineNumber: 48,
            columnNumber: 7
        }, this);
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6",
        children: subscriptions.map((sub, _index)=>{
            const profile = sub.profile;
            if (!profile) {
                return null; // Skip items with missing profiles
            }
            return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "transform transition-all duration-200 hover:scale-[1.02]",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$components$2f$shared$2f$subscriptions$2f$SubscriptionCard$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                    subscriptionId: sub.id,
                    profile: profile,
                    onUnsubscribeSuccess: showUnsubscribe ? handleUnsubscribeSuccess : undefined,
                    showUnsubscribe: showUnsubscribe,
                    variant: variant
                }, void 0, false, {
                    fileName: "[project]/app/components/shared/subscriptions/SubscriptionList.tsx",
                    lineNumber: 86,
                    columnNumber: 13
                }, this)
            }, sub.id, false, {
                fileName: "[project]/app/components/shared/subscriptions/SubscriptionList.tsx",
                lineNumber: 85,
                columnNumber: 11
            }, this);
        })
    }, void 0, false, {
        fileName: "[project]/app/components/shared/subscriptions/SubscriptionList.tsx",
        lineNumber: 76,
        columnNumber: 5
    }, this);
}
}}),
"[project]/app/components/shared/subscriptions/index.ts [app-ssr] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// Export all shared subscription components
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$components$2f$shared$2f$subscriptions$2f$SubscriptionCard$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/components/shared/subscriptions/SubscriptionCard.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$components$2f$shared$2f$subscriptions$2f$SubscriptionCardSkeleton$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/components/shared/subscriptions/SubscriptionCardSkeleton.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$components$2f$shared$2f$subscriptions$2f$SubscriptionSearch$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/components/shared/subscriptions/SubscriptionSearch.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$components$2f$shared$2f$subscriptions$2f$SubscriptionPagination$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/components/shared/subscriptions/SubscriptionPagination.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$components$2f$shared$2f$subscriptions$2f$SubscriptionList$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/components/shared/subscriptions/SubscriptionList.tsx [app-ssr] (ecmascript)");
;
;
;
;
;
}}),
"[project]/app/components/shared/subscriptions/index.ts [app-ssr] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$components$2f$shared$2f$subscriptions$2f$SubscriptionCard$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/components/shared/subscriptions/SubscriptionCard.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$components$2f$shared$2f$subscriptions$2f$SubscriptionCardSkeleton$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/components/shared/subscriptions/SubscriptionCardSkeleton.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$components$2f$shared$2f$subscriptions$2f$SubscriptionSearch$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/components/shared/subscriptions/SubscriptionSearch.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$components$2f$shared$2f$subscriptions$2f$SubscriptionPagination$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/components/shared/subscriptions/SubscriptionPagination.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$components$2f$shared$2f$subscriptions$2f$SubscriptionList$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/components/shared/subscriptions/SubscriptionList.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$components$2f$shared$2f$subscriptions$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/app/components/shared/subscriptions/index.ts [app-ssr] (ecmascript) <locals>");
}}),
"[project]/app/components/shared/subscriptions/SubscriptionList.tsx [app-ssr] (ecmascript) <export default as SubscriptionList>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "SubscriptionList": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$components$2f$shared$2f$subscriptions$2f$SubscriptionList$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$components$2f$shared$2f$subscriptions$2f$SubscriptionList$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/components/shared/subscriptions/SubscriptionList.tsx [app-ssr] (ecmascript)");
}}),
"[project]/app/(dashboard)/dashboard/customer/subscriptions/SubscriptionListClient.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>SubscriptionListClient)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$components$2f$shared$2f$subscriptions$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/app/components/shared/subscriptions/index.ts [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$components$2f$shared$2f$subscriptions$2f$SubscriptionList$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__SubscriptionList$3e$__ = __turbopack_context__.i("[project]/app/components/shared/subscriptions/SubscriptionList.tsx [app-ssr] (ecmascript) <export default as SubscriptionList>");
;
;
;
function SubscriptionListClient({ initialSubscriptions }) {
    const transformedSubscriptions = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMemo"])(()=>{
        return initialSubscriptions.map((sub)=>({
                id: sub.id,
                profile: sub.business_profiles ? {
                    id: sub.business_profiles.id,
                    name: sub.business_profiles.business_name,
                    slug: sub.business_profiles.business_slug,
                    logo_url: sub.business_profiles.logo_url,
                    city: sub.business_profiles.city,
                    state: sub.business_profiles.state,
                    locality: sub.business_profiles.locality,
                    type: 'business'
                } : null
            })).filter((sub)=>sub.profile !== null);
    }, [
        initialSubscriptions
    ]);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$components$2f$shared$2f$subscriptions$2f$SubscriptionList$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__SubscriptionList$3e$__["SubscriptionList"], {
        initialSubscriptions: transformedSubscriptions
    }, void 0, false, {
        fileName: "[project]/app/(dashboard)/dashboard/customer/subscriptions/SubscriptionListClient.tsx",
        lineNumber: 29,
        columnNumber: 5
    }, this);
}
}}),
"[project]/app/components/shared/subscriptions/SubscriptionSearch.tsx [app-ssr] (ecmascript) <export default as SubscriptionSearch>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "SubscriptionSearch": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$components$2f$shared$2f$subscriptions$2f$SubscriptionSearch$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$components$2f$shared$2f$subscriptions$2f$SubscriptionSearch$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/components/shared/subscriptions/SubscriptionSearch.tsx [app-ssr] (ecmascript)");
}}),
"[project]/components/ui/pagination.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Pagination": (()=>Pagination),
    "PaginationContent": (()=>PaginationContent),
    "PaginationEllipsis": (()=>PaginationEllipsis),
    "PaginationItem": (()=>PaginationItem),
    "PaginationLink": (()=>PaginationLink),
    "PaginationNext": (()=>PaginationNext),
    "PaginationPrevious": (()=>PaginationPrevious)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$chevron$2d$left$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ChevronLeft$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/chevron-left.js [app-ssr] (ecmascript) <export default as ChevronLeft>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$chevron$2d$right$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ChevronRight$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/chevron-right.js [app-ssr] (ecmascript) <export default as ChevronRight>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$ellipsis$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__MoreHorizontal$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/ellipsis.js [app-ssr] (ecmascript) <export default as MoreHorizontal>");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/utils.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/ui/button.tsx [app-ssr] (ecmascript)");
"use client";
;
;
;
;
;
const Pagination = ({ className, ...props })=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("nav", {
        role: "navigation",
        "aria-label": "pagination",
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])("mx-auto flex w-full justify-center", className),
        ...props
    }, void 0, false, {
        fileName: "[project]/components/ui/pagination.tsx",
        lineNumber: 15,
        columnNumber: 3
    }, this);
const PaginationContent = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["forwardRef"])(({ className, ...props }, ref)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("ul", {
        ref: ref,
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])("flex flex-row items-center gap-1", className),
        ...props
    }, void 0, false, {
        fileName: "[project]/components/ui/pagination.tsx",
        lineNumber: 27,
        columnNumber: 3
    }, this));
PaginationContent.displayName = "PaginationContent";
const PaginationItem = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["forwardRef"])(({ className, ...props }, ref)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
        ref: ref,
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])("", className),
        ...props
    }, void 0, false, {
        fileName: "[project]/components/ui/pagination.tsx",
        lineNumber: 39,
        columnNumber: 3
    }, this));
PaginationItem.displayName = "PaginationItem";
const PaginationLink = ({ className, isActive, size = "icon", ...props })=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("a", {
        "aria-current": isActive ? "page" : undefined,
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["buttonVariants"])({
            variant: isActive ? "outline" : "ghost",
            size
        }), className, isActive && "bg-muted hover:bg-muted pointer-events-none"),
        ...props
    }, void 0, false, {
        fileName: "[project]/components/ui/pagination.tsx",
        lineNumber: 54,
        columnNumber: 3
    }, this);
PaginationLink.displayName = "PaginationLink";
const PaginationPrevious = ({ className, ...props })=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(PaginationLink, {
        "aria-label": "Go to previous page",
        size: "default",
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])("gap-1 pl-2.5", className),
        ...props,
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$chevron$2d$left$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ChevronLeft$3e$__["ChevronLeft"], {
                className: "h-4 w-4"
            }, void 0, false, {
                fileName: "[project]/components/ui/pagination.tsx",
                lineNumber: 79,
                columnNumber: 5
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                children: "Previous"
            }, void 0, false, {
                fileName: "[project]/components/ui/pagination.tsx",
                lineNumber: 80,
                columnNumber: 5
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/components/ui/pagination.tsx",
        lineNumber: 73,
        columnNumber: 3
    }, this);
PaginationPrevious.displayName = "PaginationPrevious";
const PaginationNext = ({ className, ...props })=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(PaginationLink, {
        "aria-label": "Go to next page",
        size: "default",
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])("gap-1 pr-2.5", className),
        ...props,
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                children: "Next"
            }, void 0, false, {
                fileName: "[project]/components/ui/pagination.tsx",
                lineNumber: 95,
                columnNumber: 5
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$chevron$2d$right$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ChevronRight$3e$__["ChevronRight"], {
                className: "h-4 w-4"
            }, void 0, false, {
                fileName: "[project]/components/ui/pagination.tsx",
                lineNumber: 96,
                columnNumber: 5
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/components/ui/pagination.tsx",
        lineNumber: 89,
        columnNumber: 3
    }, this);
PaginationNext.displayName = "PaginationNext";
const PaginationEllipsis = ({ className, ...props })=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
        "aria-hidden": true,
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])("flex h-9 w-9 items-center justify-center", className),
        ...props,
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$ellipsis$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__MoreHorizontal$3e$__["MoreHorizontal"], {
                className: "h-4 w-4"
            }, void 0, false, {
                fileName: "[project]/components/ui/pagination.tsx",
                lineNumber: 110,
                columnNumber: 5
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                className: "sr-only",
                children: "More pages"
            }, void 0, false, {
                fileName: "[project]/components/ui/pagination.tsx",
                lineNumber: 111,
                columnNumber: 5
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/components/ui/pagination.tsx",
        lineNumber: 105,
        columnNumber: 3
    }, this);
PaginationEllipsis.displayName = "PaginationEllipsis";
;
}}),
"[project]/app/(dashboard)/dashboard/customer/subscriptions/components/SubscriptionsPageClient.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>SubscriptionsPageClient)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$bell$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Bell$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/bell.js [app-ssr] (ecmascript) <export default as Bell>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/navigation.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$dashboard$292f$dashboard$2f$customer$2f$subscriptions$2f$SubscriptionListClient$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/(dashboard)/dashboard/customer/subscriptions/SubscriptionListClient.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$components$2f$shared$2f$subscriptions$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/app/components/shared/subscriptions/index.ts [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$components$2f$shared$2f$subscriptions$2f$SubscriptionSearch$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__SubscriptionSearch$3e$__ = __turbopack_context__.i("[project]/app/components/shared/subscriptions/SubscriptionSearch.tsx [app-ssr] (ecmascript) <export default as SubscriptionSearch>");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$components$2f$shared$2f$subscriptions$2f$SubscriptionCardSkeleton$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/components/shared/subscriptions/SubscriptionCardSkeleton.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$pagination$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/ui/pagination.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/utils.ts [app-ssr] (ecmascript)");
"use client";
;
;
;
;
;
;
;
;
function SubscriptionsPageClient({ initialSubscriptions, totalCount, currentPage, searchTerm }) {
    const router = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRouter"])();
    const [isLoading, setIsLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    // Calculate total pages
    const itemsPerPage = 12; // Optimized for 3-column grid
    const totalPages = Math.max(1, Math.ceil(totalCount / itemsPerPage));
    // Reset loading state when component receives new data
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        setIsLoading(false);
    }, [
        initialSubscriptions
    ]);
    // Handle search - use useCallback to prevent infinite re-renders
    const handleSearch = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((term)=>{
        setIsLoading(true); // Show loading state
        const params = new URLSearchParams();
        if (term) {
            params.set('search', term);
        }
        params.set('page', '1'); // Reset to page 1 on new search
        router.push(`/dashboard/customer/subscriptions?${params.toString()}`);
    }, [
        router
    ]);
    // Handle page change - use useCallback to prevent infinite re-renders
    const handlePageChange = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((page)=>{
        setIsLoading(true); // Show loading state
        const params = new URLSearchParams();
        if (searchTerm) {
            params.set('search', searchTerm);
        }
        params.set('page', page.toString());
        router.push(`/dashboard/customer/subscriptions?${params.toString()}`);
    }, [
        router,
        searchTerm
    ]);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "space-y-6",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex flex-col gap-6",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex items-center gap-4",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "p-3 rounded-xl bg-muted hidden sm:block",
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$bell$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Bell$3e$__["Bell"], {
                                            className: "w-6 h-6 text-blue-600 dark:text-blue-400"
                                        }, void 0, false, {
                                            fileName: "[project]/app/(dashboard)/dashboard/customer/subscriptions/components/SubscriptionsPageClient.tsx",
                                            lineNumber: 73,
                                            columnNumber: 15
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/app/(dashboard)/dashboard/customer/subscriptions/components/SubscriptionsPageClient.tsx",
                                        lineNumber: 72,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                                                className: "text-2xl font-bold text-foreground",
                                                children: "Subscribed Businesses"
                                            }, void 0, false, {
                                                fileName: "[project]/app/(dashboard)/dashboard/customer/subscriptions/components/SubscriptionsPageClient.tsx",
                                                lineNumber: 76,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                className: "text-muted-foreground mt-1",
                                                children: [
                                                    (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["formatIndianNumberShort"])(totalCount),
                                                    " ",
                                                    totalCount === 1 ? 'business' : 'businesses',
                                                    " you're following for updates"
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/app/(dashboard)/dashboard/customer/subscriptions/components/SubscriptionsPageClient.tsx",
                                                lineNumber: 79,
                                                columnNumber: 15
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/app/(dashboard)/dashboard/customer/subscriptions/components/SubscriptionsPageClient.tsx",
                                        lineNumber: 75,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/app/(dashboard)/dashboard/customer/subscriptions/components/SubscriptionsPageClient.tsx",
                                lineNumber: 71,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "w-full sm:w-80",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$components$2f$shared$2f$subscriptions$2f$SubscriptionSearch$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__SubscriptionSearch$3e$__["SubscriptionSearch"], {
                                    onSearch: handleSearch,
                                    initialSearchTerm: searchTerm,
                                    placeholder: "Search businesses..."
                                }, void 0, false, {
                                    fileName: "[project]/app/(dashboard)/dashboard/customer/subscriptions/components/SubscriptionsPageClient.tsx",
                                    lineNumber: 87,
                                    columnNumber: 13
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/app/(dashboard)/dashboard/customer/subscriptions/components/SubscriptionsPageClient.tsx",
                                lineNumber: 86,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/app/(dashboard)/dashboard/customer/subscriptions/components/SubscriptionsPageClient.tsx",
                        lineNumber: 70,
                        columnNumber: 9
                    }, this),
                    searchTerm && !isLoading && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "text-sm text-muted-foreground border-l-4 border-primary pl-4",
                        children: [
                            "Found ",
                            (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["formatIndianNumberShort"])(totalCount),
                            " ",
                            totalCount === 1 ? 'business' : 'businesses',
                            searchTerm ? ` matching "${searchTerm}"` : ''
                        ]
                    }, void 0, true, {
                        fileName: "[project]/app/(dashboard)/dashboard/customer/subscriptions/components/SubscriptionsPageClient.tsx",
                        lineNumber: 97,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/app/(dashboard)/dashboard/customer/subscriptions/components/SubscriptionsPageClient.tsx",
                lineNumber: 69,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "space-y-6",
                children: isLoading ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$components$2f$shared$2f$subscriptions$2f$SubscriptionCardSkeleton$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["SubscriptionListSkeleton"], {}, void 0, false, {
                    fileName: "[project]/app/(dashboard)/dashboard/customer/subscriptions/components/SubscriptionsPageClient.tsx",
                    lineNumber: 108,
                    columnNumber: 11
                }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Fragment"], {
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$dashboard$292f$dashboard$2f$customer$2f$subscriptions$2f$SubscriptionListClient$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                            initialSubscriptions: initialSubscriptions,
                            totalCount: totalCount,
                            currentPage: currentPage
                        }, void 0, false, {
                            fileName: "[project]/app/(dashboard)/dashboard/customer/subscriptions/components/SubscriptionsPageClient.tsx",
                            lineNumber: 112,
                            columnNumber: 13
                        }, this),
                        totalPages > 1 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex justify-center pt-6",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$pagination$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Pagination"], {
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$pagination$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["PaginationContent"], {
                                    children: [
                                        currentPage > 1 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$pagination$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["PaginationItem"], {
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$pagination$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["PaginationPrevious"], {
                                                href: "#",
                                                onClick: (e)=>{
                                                    e.preventDefault();
                                                    handlePageChange(currentPage - 1);
                                                }
                                            }, void 0, false, {
                                                fileName: "[project]/app/(dashboard)/dashboard/customer/subscriptions/components/SubscriptionsPageClient.tsx",
                                                lineNumber: 125,
                                                columnNumber: 25
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/app/(dashboard)/dashboard/customer/subscriptions/components/SubscriptionsPageClient.tsx",
                                            lineNumber: 124,
                                            columnNumber: 23
                                        }, this),
                                        Array.from({
                                            length: Math.min(5, totalPages)
                                        }, (_, i)=>{
                                            let pageNum;
                                            if (totalPages <= 5) {
                                                pageNum = i + 1;
                                            } else if (currentPage <= 3) {
                                                pageNum = i + 1;
                                            } else if (currentPage >= totalPages - 2) {
                                                pageNum = totalPages - 4 + i;
                                            } else {
                                                pageNum = currentPage - 2 + i;
                                            }
                                            return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$pagination$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["PaginationItem"], {
                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$pagination$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["PaginationLink"], {
                                                    href: "#",
                                                    onClick: (e)=>{
                                                        e.preventDefault();
                                                        handlePageChange(pageNum);
                                                    },
                                                    isActive: currentPage === pageNum,
                                                    children: pageNum
                                                }, void 0, false, {
                                                    fileName: "[project]/app/(dashboard)/dashboard/customer/subscriptions/components/SubscriptionsPageClient.tsx",
                                                    lineNumber: 150,
                                                    columnNumber: 27
                                                }, this)
                                            }, pageNum, false, {
                                                fileName: "[project]/app/(dashboard)/dashboard/customer/subscriptions/components/SubscriptionsPageClient.tsx",
                                                lineNumber: 149,
                                                columnNumber: 25
                                            }, this);
                                        }),
                                        currentPage < totalPages && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$pagination$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["PaginationItem"], {
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$pagination$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["PaginationNext"], {
                                                href: "#",
                                                onClick: (e)=>{
                                                    e.preventDefault();
                                                    handlePageChange(currentPage + 1);
                                                }
                                            }, void 0, false, {
                                                fileName: "[project]/app/(dashboard)/dashboard/customer/subscriptions/components/SubscriptionsPageClient.tsx",
                                                lineNumber: 166,
                                                columnNumber: 25
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/app/(dashboard)/dashboard/customer/subscriptions/components/SubscriptionsPageClient.tsx",
                                            lineNumber: 165,
                                            columnNumber: 23
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/app/(dashboard)/dashboard/customer/subscriptions/components/SubscriptionsPageClient.tsx",
                                    lineNumber: 122,
                                    columnNumber: 19
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/app/(dashboard)/dashboard/customer/subscriptions/components/SubscriptionsPageClient.tsx",
                                lineNumber: 121,
                                columnNumber: 17
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/app/(dashboard)/dashboard/customer/subscriptions/components/SubscriptionsPageClient.tsx",
                            lineNumber: 120,
                            columnNumber: 15
                        }, this)
                    ]
                }, void 0, true)
            }, void 0, false, {
                fileName: "[project]/app/(dashboard)/dashboard/customer/subscriptions/components/SubscriptionsPageClient.tsx",
                lineNumber: 105,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/app/(dashboard)/dashboard/customer/subscriptions/components/SubscriptionsPageClient.tsx",
        lineNumber: 67,
        columnNumber: 5
    }, this);
}
}}),

};

//# sourceMappingURL=_ad0a5cb8._.js.map