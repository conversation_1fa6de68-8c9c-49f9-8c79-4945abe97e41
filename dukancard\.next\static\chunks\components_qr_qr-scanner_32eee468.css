/* [project]/components/qr/qr-scanner.css [app-client] (css) */
#qr-scanner-region {
  background: #000;
  border-radius: 12px;
  overflow: hidden;
}

#qr-scanner-region video {
  object-fit: cover;
  border-radius: 12px;
  width: 100% !important;
  height: auto !important;
}

#qr-scanner-region canvas {
  border-radius: 12px;
}

#qr-scanner-region button {
  color: #fff !important;
  background: #d4af37e6 !important;
  border: none !important;
  border-radius: 8px !important;
  margin: 4px !important;
  padding: 8px 16px !important;
  font-weight: 500 !important;
  transition: all .2s !important;
}

#qr-scanner-region button:hover {
  transform: translateY(-1px);
  background: #d4af37 !important;
}

#qr-scanner-region button[title*="torch"], #qr-scanner-region button[title*="Torch"], #qr-scanner-region button[title*="flash"], #qr-scanner-region button[title*="Flash"] {
  backdrop-filter: blur(10px);
  background: #ffffff1a !important;
  border: 1px solid #fff3 !important;
}

#qr-scanner-region select {
  color: #fff !important;
  background: #000c !important;
  border: 1px solid #d4af3780 !important;
  border-radius: 6px !important;
  padding: 6px 12px !important;
  font-size: 14px !important;
}

#qr-scanner-region span, #qr-scanner-region div {
  color: #fff !important;
  font-family: inherit !important;
}

#qr-scanner-region input[type="file"] {
  color: #fff !important;
  cursor: pointer !important;
  background: #d4af37e6 !important;
  border: none !important;
  border-radius: 8px !important;
  padding: 8px 16px !important;
}

#qr-scanner-region .qr-scanner-status {
  backdrop-filter: blur(10px);
  background: #000000b3 !important;
  border-radius: 8px !important;
  margin: 8px !important;
  padding: 8px 12px !important;
}

#qr-scanner-region .qr-scanner-permission {
  text-align: center !important;
  background: #d4af371a !important;
  border: 1px solid #d4af374d !important;
  border-radius: 12px !important;
  padding: 20px !important;
}

#qr-scanner-region .qr-scanner-error {
  color: #ef4444 !important;
  background: #ef44441a !important;
  border: 1px solid #ef44444d !important;
  border-radius: 8px !important;
  padding: 12px !important;
}

#qr-scanner-region .qr-scanner-success {
  color: #22c55e !important;
  background: #22c55e1a !important;
  border: 1px solid #22c55e4d !important;
  border-radius: 8px !important;
  padding: 12px !important;
}

#qr-scanner-region .qr-scanner-loading {
  justify-content: center !important;
  align-items: center !important;
  padding: 20px !important;
  display: flex !important;
}

@media (width <= 640px) {
  #qr-scanner-region {
    border-radius: 8px;
  }

  #qr-scanner-region button {
    padding: 6px 12px !important;
    font-size: 14px !important;
  }

  #qr-scanner-region select {
    padding: 4px 8px !important;
    font-size: 12px !important;
  }
}

@media (prefers-color-scheme: dark) {
  #qr-scanner-region {
    background: #0a0a0a;
  }

  #qr-scanner-region span, #qr-scanner-region div {
    color: #f5f5f5 !important;
  }
}

@keyframes qr-scanner-pulse {
  0% {
    box-shadow: 0 0 #d4af37b3;
  }

  70% {
    box-shadow: 0 0 0 10px #d4af3700;
  }

  100% {
    box-shadow: 0 0 #d4af3700;
  }
}

#qr-scanner-region .qr-scanner-active {
  animation: 2s infinite qr-scanner-pulse;
}


/*# sourceMappingURL=components_qr_qr-scanner_32eee468.css.map*/