# Epic: Comprehensive Test Coverage for Dashboard Features

## User Story
As a developer, I want to have comprehensive unit and integration tests for the likes, subscribers, and reviews features in the dashboard for both customer and business views, so that I can ensure the quality and stability of these features.

## Acceptance Criteria
- Unit and integration tests are created for data fetching on all three pages (likes, subscribers, reviews).
- Unit and integration tests are created for pagination on all three pages.
- Unit and integration tests are created for search functionality on all three pages.
- Unit and integration tests are created for sorting functionality on the reviews page.
- Unit and integration tests are created for searching by customer/business name on the business pages.
- Tests cover both customer and business views.
- All new tests are passing and integrated into the CI/CD pipeline.
- Code coverage for the new tests is above 80%.

## Tasks
- **Spike:** Identify all files and code paths related to the likes, subscribers, and reviews features for both customer and business views.
- Create unit tests for data fetching logic (e.g., API calls, data transformation).
- Create integration tests for data fetching and UI rendering.
- Create unit tests for pagination logic.
- Create integration tests for pagination UI (e.g., clicking next/previous buttons).
- Create unit tests for search logic.
- Create integration tests for search UI (e.g., typing in the search box and verifying results).
- Create unit tests for sorting logic on the reviews page.
- Create integration tests for sorting UI on the reviews page (e.g., selecting a sort option and verifying the order of reviews).
- Create unit tests for searching by customer/business name.
- Create integration tests for searching by customer/business name UI.
- Refactor existing code to improve testability where necessary.
- Configure and integrate new tests into the CI/CD pipeline.

## Testing Strategy
- **Mocking:** We will only mock external services like Supabase.
- **Component Behavior:** All local components will be used directly in tests without mocking to ensure we are testing the exact behavior of each component.


## File Dependencies

### Business Likes
- **Page:** `C:\web-app\dukancard\app\(dashboard)\dashboard\business\likes\page.tsx`
- **Client Component:** `C:\web-app\dukancard\app\(dashboard)\dashboard\business\likes\components\BusinessLikesPageClient.tsx`
- **Sub-components:**
    - `C:\web-app\dukancard\app\(dashboard)\dashboard\business\likes\components\BusinessLikesReceivedList.tsx`
    - `C:\web-app\dukancard\app\(dashboard)\dashboard\business\likes\components\BusinessMyLikesList.tsx`
    - `C:\web-app\dukancard\app\components\shared\likes\LikeCard.tsx`
- **Actions:** `C:\web-app\dukancard\app\(dashboard)\dashboard\business\likes\actions.ts`
- **Service:** `C:\web-app\dukancard\lib\services\socialService.ts` (likesService)

### Business Reviews
- **Page:** `C:\web-app\dukancard\app\(dashboard)\dashboard\business\reviews\page.tsx`
- **Client Component:** `C:\web-app\dukancard\app\(dashboard)\dashboard\business\reviews\components\BusinessReviewsPageClient.tsx`
- **Sub-components:**
    - `C:\web-app\dukancard\app\(dashboard)\dashboard\business\reviews\components\BusinessReviewListClient.tsx`
    - `C:\web-app\dukancard\app\(dashboard)\dashboard\business\reviews\components\BusinessMyReviewListClient.tsx`
- **Actions:** `C:\web-app\dukancard\app\(dashboard)\dashboard\business\reviews\actions.ts`
- **Service:** `C:\web-app\dukancard\lib\services\socialService.ts` (reviewsService)

### Business Subscriptions
- **Page:** `C:\web-app\dukancard\app\(dashboard)\dashboard\business\subscriptions\page.tsx`
- **Client Component:** `C:\web-app\dukancard\app\(dashboard)\dashboard\business\subscriptions\components\BusinessSubscriptionsPageClient.tsx`
- **Actions:** `C:\web-app\dukancard\app\(dashboard)\dashboard\business\subscriptions\actions.ts`
- **Service:** `C:\web-app\dukancard\lib\services\socialService.ts` (subscriptionsService)

### Customer Likes
- **Page:** `C:\web-app\dukancard\app\(dashboard)\dashboard\customer\likes\page.tsx`
- **Client Component:** `C:\web-app\dukancard\app\(dashboard)\dashboard\customer\likes\components\LikesPageClient.tsx`
- **Sub-components:** `C:\web-app\dukancard\app\(dashboard)\dashboard\customer\likes\LikeListClient.tsx`
- **Actions:** `C:\web-app\dukancard\app\(dashboard)\dashboard\customer\likes\actions.ts`
- **Service:** `C:\web-app\dukancard\lib\services\socialService.ts` (likesService)

### Customer Reviews
- **Page:** `C:\web-app\dukancard\app\(dashboard)\dashboard\customer\reviews\page.tsx`
- **Client Component:** `C:\web-app\dukancard\app\(dashboard)\dashboard\customer\reviews\components\ReviewsPageClient.tsx`
- **Sub-components:**
    - `C:\web-app\dukancard\app\(dashboard)\dashboard\customer\reviews\components\EnhancedReviewListClient.tsx`
    - `C:\web-app\dukancard\app\(dashboard)\dashboard\customer\reviews\ReviewListClient.tsx`
- **Actions:** `C:\web-app\dukancard\app\(dashboard)\dashboard\customer\reviews\actions.ts`
- **Service:** `C:\web-app\dukancard\lib\services\socialService.ts` (reviewsService)

### Customer Subscriptions
- **Page:** `C:\web-app\dukancard\app\(dashboard)\dashboard\customer\subscriptions\page.tsx`
- **Client Component:** `C:\web-app\dukancard\app\(dashboard)\dashboard\customer\subscriptions\components\SubscriptionsPageClient.tsx`
- **Sub-components:** `C:\web-app\dukancard\app\(dashboard)\dashboard\customer\subscriptions\SubscriptionListClient.tsx`
- **Actions:** `C:\web-app\dukancard\app\(dashboard)\dashboard\customer\subscriptions\actions.ts`
- **Service:** `C:\web-app\dukancard\lib\services\socialService.ts` (subscriptionsService)
