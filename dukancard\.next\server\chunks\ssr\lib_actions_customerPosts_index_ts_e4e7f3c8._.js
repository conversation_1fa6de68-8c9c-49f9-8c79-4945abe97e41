module.exports = {

"[project]/lib/actions/customerPosts/index.ts [app-ssr] (ecmascript) <exports>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "createCustomerPost": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$customerPosts$2f$crud$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createCustomerPost"]),
    "deleteCustomerPost": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$customerPosts$2f$crud$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["deleteCustomerPost"]),
    "getCustomerPost": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$customerPosts$2f$crud$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getCustomerPost"]),
    "getCustomerPosts": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$customerPosts$2f$crud$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getCustomerPosts"]),
    "updateCustomerPost": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$customerPosts$2f$crud$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["updateCustomerPost"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$customerPosts$2f$crud$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/actions/customerPosts/crud.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$customerPosts$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/lib/actions/customerPosts/index.ts [app-ssr] (ecmascript) <locals>");
}}),
"[project]/lib/actions/customerPosts/index.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "createCustomerPost": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$customerPosts$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$exports$3e$__["createCustomerPost"]),
    "deleteCustomerPost": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$customerPosts$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$exports$3e$__["deleteCustomerPost"]),
    "getCustomerPost": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$customerPosts$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$exports$3e$__["getCustomerPost"]),
    "getCustomerPosts": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$customerPosts$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$exports$3e$__["getCustomerPosts"]),
    "updateCustomerPost": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$customerPosts$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$exports$3e$__["updateCustomerPost"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$customerPosts$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/lib/actions/customerPosts/index.ts [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$customerPosts$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$exports$3e$__ = __turbopack_context__.i("[project]/lib/actions/customerPosts/index.ts [app-ssr] (ecmascript) <exports>");
}}),

};