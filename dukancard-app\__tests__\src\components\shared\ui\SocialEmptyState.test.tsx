import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react-native';
import { SocialEmptyState } from '@/src/components/shared/ui/SocialEmptyState';

// Mock useTheme hook
const mockUseTheme = {
  colors: {
    primary: '#007AFF',
    text: '#000000',
    textSecondary: '#666666',
  },
  isDark: false,
};

jest.mock('@/src/hooks/useTheme', () => ({
  useTheme: () => mockUseTheme,
}));

// Mock Lucide React Native icons
jest.mock('lucide-react-native', () => ({
  Heart: ({ size, color, testID }: any) => <div testID={testID || 'heart-icon'}>Heart-{size}-{color}</div>,
  Users: ({ size, color, testID }: any) => <div testID={testID || 'users-icon'}>Users-{size}-{color}</div>,
  Star: ({ size, color, testID }: any) => <div testID={testID || 'star-icon'}>Star-{size}-{color}</div>,
  UserPlus: ({ size, color, testID }: any) => <div testID={testID || 'userplus-icon'}>UserPlus-{size}-{color}</div>,
  MessageCircle: ({ size, color, testID }: any) => <div testID={testID || 'messagecircle-icon'}>MessageCircle-{size}-{color}</div>,
  Search: ({ size, color, testID }: any) => <div testID={testID || 'search-icon'}>Search-{size}-{color}</div>,
  Sparkles: ({ size, color, testID }: any) => <div testID={testID || 'sparkles-icon'}>Sparkles-{size}-{color}</div>,
}));

describe('SocialEmptyState', () => {
  const mockOnAction = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Component rendering', () => {
    it('should render without crashing for likes-received type', () => {
      const result = render(
        <SocialEmptyState
          type="likes-received"
          isBusinessProfile={true}
          onAction={mockOnAction}
        />
      );

      expect(result).toBeTruthy();
    });

    it('should render without crashing for all types', () => {
      const types = [
        'likes-received',
        'likes-given',
        'followers',
        'following',
        'reviews-received',
        'reviews-given',
        'subscriptions',
        'search-results'
      ] as const;

      types.forEach(type => {
        const result = render(
          <SocialEmptyState
            type={type}
            isBusinessProfile={true}
            onAction={mockOnAction}
          />
        );
        expect(result).toBeTruthy();
      });
    });

    it('should render with different profile types', () => {
      const businessResult = render(
        <SocialEmptyState
          type="likes-received"
          isBusinessProfile={true}
          onAction={mockOnAction}
        />
      );

      const customerResult = render(
        <SocialEmptyState
          type="likes-received"
          isBusinessProfile={false}
          onAction={mockOnAction}
        />
      );

      expect(businessResult).toBeTruthy();
      expect(customerResult).toBeTruthy();
    });
  });

  describe('Search state handling', () => {
    it('should render without crashing when searchTerm is provided', () => {
      const result = render(
        <SocialEmptyState
          type="likes-received"
          searchTerm="test search"
          isBusinessProfile={true}
          onAction={mockOnAction}
        />
      );

      expect(result).toBeTruthy();
    });

    it('should render without crashing for search state override', () => {
      const result = render(
        <SocialEmptyState
          type="followers"
          searchTerm="search term"
          isBusinessProfile={true}
          onAction={mockOnAction}
        />
      );

      expect(result).toBeTruthy();
    });
  });

  describe('Action button', () => {
    it('should render without crashing when onAction is provided', () => {
      const result = render(
        <SocialEmptyState
          type="likes-received"
          isBusinessProfile={true}
          onAction={mockOnAction}
        />
      );

      expect(result).toBeTruthy();
    });

    it('should render without crashing when custom actionText is provided', () => {
      const result = render(
        <SocialEmptyState
          type="likes-received"
          isBusinessProfile={true}
          actionText="Custom Action"
          onAction={mockOnAction}
        />
      );

      expect(result).toBeTruthy();
    });

    it('should render without crashing when onAction is not provided', () => {
      const result = render(
        <SocialEmptyState
          type="likes-received"
          isBusinessProfile={true}
        />
      );

      expect(result).toBeTruthy();
    });
  });

  describe('Theme integration', () => {
    it('should use theme colors', () => {
      const result = render(
        <SocialEmptyState
          type="likes-received"
          isBusinessProfile={true}
          onAction={mockOnAction}
        />
      );

      // The component should use colors from the theme
      // This is tested through the mock theme values
      expect(mockUseTheme.colors.primary).toBe('#007AFF');
      expect(mockUseTheme.colors.text).toBe('#000000');
      expect(result).toBeTruthy();
    });

    it('should adapt to dark theme', () => {
      // Update mock to dark theme
      mockUseTheme.isDark = true;
      mockUseTheme.colors = {
        primary: '#0A84FF',
        text: '#FFFFFF',
        textSecondary: '#EBEBF5',
      };

      const result = render(
        <SocialEmptyState
          type="likes-received"
          isBusinessProfile={true}
          onAction={mockOnAction}
        />
      );

      expect(mockUseTheme.isDark).toBe(true);
      expect(mockUseTheme.colors.text).toBe('#FFFFFF');
      expect(result).toBeTruthy();
    });
  });

  describe('Props validation', () => {
    it('should handle all required props correctly', () => {
      const result = render(
        <SocialEmptyState
          type="likes-received"
          isBusinessProfile={true}
        />
      );

      expect(result).toBeTruthy();
    });

    it('should handle all optional props correctly', () => {
      const result = render(
        <SocialEmptyState
          type="likes-received"
          searchTerm="test"
          actionText="Custom Action"
          onAction={mockOnAction}
          isBusinessProfile={true}
        />
      );

      expect(result).toBeTruthy();
    });
  });
});
