---
Status: Draft
Story: |
  As a developer, I need to enhance the unit and integration tests for the `ReviewListClient` component in the `dukancard` project. This component is responsible for displaying a list of customer reviews, managing the state of these reviews, and handling the deletion of reviews. The tests should ensure correct rendering of `ReviewCard` components, proper state updates upon deletion, and accurate display of the empty state.

Acceptance Criteria:
- **Initial Rendering with Data:**
    - Given `initialReviews` is not empty, when the component renders, then `ReviewCard` components are rendered for each review.
    - And each `ReviewCard` receives the correct `review` prop and an `onDeleteSuccess` callback.
- **Empty State Rendering:**
    - Given `initialReviews` is empty, when the component renders, then the enhanced empty state message "You haven't written any reviews yet" and its associated description and button are displayed.
    - And `ReviewCard` components are not rendered.
    - And the "Discover Businesses" button links to `/businesses` with `target="_blank"` and `rel="noopener noreferrer"`.
- **Review Deletion:**
    - Given `handleDeleteSuccess` is called with a `reviewIdToRemove`, then the review with that `reviewIdToRemove` is removed from the component's internal `reviews` state.
    - And the `ReviewCard` for the deleted review is no longer rendered.
- **UI Consistency:**
    - Ensure the styling of the empty state matches the provided design (e.g., icons, text sizes, colors, gradient background, pulse effect).
    - Ensure the action button in the empty state has correct styling and link attributes.
    - Ensure proper typography hierarchy and accessible button styling.
    - Ensure dark mode compatibility with correct classes for theme switching.

Tasks:
1. Update the existing test file: `C:\web-app\dukancard\__tests__\app\(dashboard)\dashboard\customer\reviews\ReviewListClient.test.tsx`.
2. Expand existing test cases to cover:
    - Testing the `handleDeleteSuccess` functionality: simulate a deletion and assert that the review is removed from the DOM.
    - Ensure `ReviewCard` is rendered with the correct `onDeleteSuccess` prop.
3. Add new test cases for:
    - Rendering with multiple reviews to ensure all are displayed correctly.
    - Verifying the `key` prop for `ReviewCard` components.

File: `C:\web-app\dukancard\app\(dashboard)\dashboard\customer\reviews\ReviewListClient.tsx`
Platform: dukancard
---