---
Status: Draft
Story: |
  As a developer, I need to implement comprehensive unit and integration tests for the `SubscriptionListClient` component in the `dukancard` project. This component is responsible for transforming raw subscription data into a format suitable for the shared `SubscriptionList` component and rendering it. The tests should ensure correct data mapping and proper prop passing to the `SubscriptionList` component.

Acceptance Criteria:
- **Data Transformation:**
    - Given `initialSubscriptions` containing `SubscriptionWithProfile` objects, when the component renders, then `transformedSubscriptions` correctly maps each `sub` object to a `SubscriptionData` object with `type: 'business'`.
    - And `profile` is `null` for any `sub` object where `business_profiles` is missing.
    - And `null` profiles are filtered out from `transformedSubscriptions`.
- **Rendering with Data:**
    - Given `initialSubscriptions` is not empty, when the component renders, then the `SubscriptionList` component is rendered with `initialSubscriptions` set to the `transformedSubscriptions` array.
- **Prop Delegation:**
    - Verify that `SubscriptionListClient` does not pass pagination and search props directly to `SubscriptionList`, as these are handled by the parent component.

Tasks:
1. Create a new test file: `C:\web-app\dukancard\__tests__\app\(dashboard)\dashboard\customer\subscriptions\SubscriptionListClient.test.tsx`.
2. Set up a testing environment that can render React components.
3. Write unit tests for the `SubscriptionListClient` component covering all acceptance criteria.
4. Mock the `SubscriptionList` component to verify its props without testing its internal rendering.
5. Test rendering with various `initialSubscriptions` data scenarios (empty, with data).
6. Verify the correct props are passed to `SubscriptionList`.

File: `C:\web-app\dukancard\app\(dashboard)\dashboard\customer\subscriptions\SubscriptionListClient.tsx`
Platform: dukancard
---