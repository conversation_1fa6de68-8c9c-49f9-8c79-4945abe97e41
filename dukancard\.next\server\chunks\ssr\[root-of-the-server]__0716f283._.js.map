{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/inter_edb9147b.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"inter_edb9147b-module__xWQRDW__className\",\n  \"variable\": \"inter_edb9147b-module__xWQRDW__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 16, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/inter_edb9147b.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22Inter%22,%22arguments%22:[{%22subsets%22:[%22latin%22],%22variable%22:%22--font-sans%22}],%22variableName%22:%22inter%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Inter', 'Inter Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,qJAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,qJAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,qJAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 38, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/components/ui/sonner.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Toaster = registerClientReference(\n    function() { throw new Error(\"Attempted to call Toaster() from the server but To<PERSON> is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/sonner.tsx <module evaluation>\",\n    \"Toaster\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,UAAU,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,0DACA", "debugId": null}}, {"offset": {"line": 52, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/components/ui/sonner.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Toaster = registerClientReference(\n    function() { throw new Error(\"Attempted to call Toaster() from the server but To<PERSON> is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/sonner.tsx\",\n    \"Toaster\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,UAAU,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,sCACA", "debugId": null}}, {"offset": {"line": 66, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 76, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/components/GoogleAnalytics.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/app/components/GoogleAnalytics.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/app/components/GoogleAnalytics.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAsS,GACnU,oEACA", "debugId": null}}, {"offset": {"line": 90, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/components/GoogleAnalytics.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/app/components/GoogleAnalytics.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/app/components/GoogleAnalytics.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAkR,GAC/S,gDACA", "debugId": null}}, {"offset": {"line": 104, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 114, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/components/MetaPixel.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/app/components/MetaPixel.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/app/components/MetaPixel.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAgS,GAC7T,8DACA", "debugId": null}}, {"offset": {"line": 128, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/components/MetaPixel.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/app/components/MetaPixel.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/app/components/MetaPixel.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA4Q,GACzS,0CACA", "debugId": null}}, {"offset": {"line": 142, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 152, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/layout.tsx"], "sourcesContent": ["import { Inter } from \"next/font/google\"; // Import Inter font\r\nimport \"./globals.css\";\r\nimport { ThemeProvider } from \"next-themes\"; // Import ThemeProvider\r\nimport { Toaster } from \"@/components/ui/sonner\";\r\nimport GoogleAnalytics from \"@/app/components/GoogleAnalytics\";\r\nimport MetaPixel from \"@/app/components/MetaPixel\";\r\n\r\n// Configure Inter font\r\nconst inter = Inter({\r\n  subsets: [\"latin\"],\r\n  variable: \"--font-sans\", // Assign CSS variable\r\n});\r\n\r\nexport async function generateMetadata() {\r\n  const siteUrl = process.env.NEXT_PUBLIC_BASE_URL || \"https://dukancard.in\";\r\n  const siteTitle = \"Dukancard - Your Digital Business Card Solution\";\r\n  const siteDescription =\r\n    \"Create a digital business card with Dukancard to showcase your shop, services, or portfolio. Boost your online presence, connect with customers, and grow your business across India.\";\r\n\r\n  const ogImage = `${siteUrl}/opengraph-image.png`;\r\n\r\n  return {\r\n    metadataBase: new URL(siteUrl),\r\n    title: {\r\n      default: siteTitle,\r\n      template: `%s - Dukancard`,\r\n    },\r\n    description: siteDescription,\r\n    keywords:\r\n      \"digital business card, online presence, small business India, shop digital card, freelancer portfolio, Tier 2 cities, Tier 3 cities, Dukancard, local business growth, QR code business card\",\r\n    robots: \"index, follow\",\r\n    alternates: {\r\n      canonical: siteUrl,\r\n    },\r\n    openGraph: {\r\n      title: siteTitle,\r\n      description: siteDescription,\r\n      url: siteUrl,\r\n      siteName: \"Dukancard\",\r\n      type: \"website\",\r\n      locale: \"en_IN\",\r\n      images: [\r\n        {\r\n          url: ogImage,\r\n          width: 1200,\r\n          height: 630,\r\n          alt: \"Dukancard - Digital Business Card Platform\",\r\n        },\r\n      ],\r\n    },\r\n    twitter: {\r\n      card: \"summary_large_image\",\r\n      title: siteTitle,\r\n      description: siteDescription,\r\n      image: ogImage,\r\n    },\r\n  };\r\n}\r\n\r\nexport default function RootLayout({\r\n  children,\r\n}: Readonly<{\r\n  children: React.ReactNode;\r\n}>) {\r\n  return (\r\n    // Apply font variable to html tag\r\n    <html lang=\"en\" className={inter.variable} suppressHydrationWarning>\r\n      <head>\r\n        <GoogleAnalytics />\r\n        <MetaPixel />\r\n      </head>\r\n      {/* Apply flex layout to body for sticky footer */}\r\n      <body className=\"antialiased flex flex-col min-h-screen bg-white dark:bg-black text-black dark:text-white transition-colors duration-300\">\r\n        <ThemeProvider\r\n          attribute=\"class\"\r\n          defaultTheme=\"system\" // Or \"dark\" / \"light\" if preferred\r\n          enableSystem\r\n          disableTransitionOnChange\r\n        >\r\n          {/* Header/Footer could be added back here if needed globally */}\r\n          {children}\r\n          <Toaster />\r\n          {/* Removed GoogleCMP - only custom ads now */}\r\n        </ThemeProvider>\r\n      </body>\r\n    </html>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;;AAEA,4PAA6C,uBAAuB;AACpE;AACA;AACA;;;;;;;;AAQO,eAAe;IACpB,MAAM,UAAU,6DAAoC;IACpD,MAAM,YAAY;IAClB,MAAM,kBACJ;IAEF,MAAM,UAAU,GAAG,QAAQ,oBAAoB,CAAC;IAEhD,OAAO;QACL,cAAc,IAAI,IAAI;QACtB,OAAO;YACL,SAAS;YACT,UAAU,CAAC,cAAc,CAAC;QAC5B;QACA,aAAa;QACb,UACE;QACF,QAAQ;QACR,YAAY;YACV,WAAW;QACb;QACA,WAAW;YACT,OAAO;YACP,aAAa;YACb,KAAK;YACL,UAAU;YACV,MAAM;YACN,QAAQ;YACR,QAAQ;gBACN;oBACE,KAAK;oBACL,OAAO;oBACP,QAAQ;oBACR,KAAK;gBACP;aACD;QACH;QACA,SAAS;YACP,MAAM;YACN,OAAO;YACP,aAAa;YACb,OAAO;QACT;IACF;AACF;AAEe,SAAS,WAAW,EACjC,QAAQ,EAGR;IACA,OACE,kCAAkC;kBAClC,8OAAC;QAAK,MAAK;QAAK,WAAW,yIAAA,CAAA,UAAK,CAAC,QAAQ;QAAE,wBAAwB;;0BACjE,8OAAC;;kCACC,8OAAC,qIAAA,CAAA,UAAe;;;;;kCAChB,8OAAC,+HAAA,CAAA,UAAS;;;;;;;;;;;0BAGZ,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,gJAAA,CAAA,gBAAa;oBACZ,WAAU;oBACV,cAAa,SAAS,mCAAmC;;oBACzD,YAAY;oBACZ,yBAAyB;;wBAGxB;sCACD,8OAAC,2HAAA,CAAA,UAAO;;;;;;;;;;;;;;;;;;;;;;AAMlB", "debugId": null}}, {"offset": {"line": 273, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/node_modules/next/src/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored[\n  'react-rsc'\n].ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CACxD,YACD,CAACC,kBAAkB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 281, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/node_modules/next-themes/dist/index.mjs/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const ThemeProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/next-themes/dist/index.mjs <module evaluation>\",\n    \"ThemeProvider\",\n);\nexport const useTheme = registerClientReference(\n    function() { throw new Error(\"Attempted to call useTheme() from the server but useTheme is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/next-themes/dist/index.mjs <module evaluation>\",\n    \"useTheme\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,gBAAgB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,yEACA;AAEG,MAAM,WAAW,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC1C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,yEACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 299, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/node_modules/next-themes/dist/index.mjs/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const ThemeProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/next-themes/dist/index.mjs\",\n    \"ThemeProvider\",\n);\nexport const useTheme = registerClientReference(\n    function() { throw new Error(\"Attempted to call useTheme() from the server but useTheme is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/next-themes/dist/index.mjs\",\n    \"useTheme\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,gBAAgB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,qDACA;AAEG,MAAM,WAAW,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC1C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,qDACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 317, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}]}