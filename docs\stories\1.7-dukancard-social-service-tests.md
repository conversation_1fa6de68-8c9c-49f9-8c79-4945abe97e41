---
Status: Draft
Story: |
  As a developer, I need to enhance the unit tests for the `socialService.ts` file within the `dukancard` project's social service module. This service provides core functionalities for managing subscriptions, likes, and reviews, including data fetching with pagination, searching, sorting, and profile data enrichment. The expanded tests should cover all public methods, ensuring correct data manipulation, error handling, and edge case scenarios, especially concerning different profile types (customer vs. business) and data transformation.

Acceptance Criteria:
- **General Service Behavior:**
    - All public methods (`subscriptionsService.fetchSubscriptions`, `subscriptionsService.unsubscribe`, `subscriptionsService.fetchBusinessFollowers`, `likesService.fetchLikes`, `likesService.unlike`, `likesService.fetchBusinessLikesReceived`, `reviewsService.fetchReviews`, `reviewsService.deleteReview`, `reviewsService.updateReview`, `reviewsService.fetchBusinessReviewsReceived`, `getActivityMetrics`) are tested.
    - Each method correctly interacts with the Supabase client (`supabase.from`, `select`, `eq`, `ilike`, `order`, `range`, `delete`, `update`, `in`).
    - Error handling is robust: methods should gracefully handle Supabase errors (e.g., network issues, database errors) and throw appropriate errors or return expected error states.
    - Edge cases like empty data sets, single-item results, and pagination boundaries are covered.

- **`subscriptionsService.fetchSubscriptions`:**
    - **Success with Data:**
        - Given a `userId` and matching subscriptions with `business_profiles`, returns `SubscriptionsResult` with correct `items`, `totalCount`, `hasMore`, and `currentPage`.
        - Verifies `business_profiles` are correctly transformed (handling array vs. single object).
    - **Success with No Data:**
        - Given a `userId` with no subscriptions, returns `SubscriptionsResult` with empty `items`, `totalCount: 0`, `hasMore: false`.
    - **Search Functionality:**
        - Given a `searchTerm`, `ilike` is called on `business_profiles.business_name` for both count and main queries.
        - Returns filtered results correctly.
    - **Pagination:**
        - `range` method is called with correct `offset` and `limit` values.
        - `hasMore` is correctly calculated based on `totalCount` and pagination parameters.
    - **Error Handling:**
        - Handles errors during count query.
        - Handles errors during main data query.

- **`subscriptionsService.unsubscribe`:**
    - **Success:**
        - Given a `subscriptionId`, `delete` is called on the `subscriptions` table with the correct `id`.
    - **Error Handling:**
        - Handles errors during the delete operation.

- **`subscriptionsService.fetchBusinessFollowers`:**
    - **Success with Data:**
        - Given a `businessId` and matching followers (both customer and business profiles), returns `FollowersResult` with correctly transformed `items` (distinguishing `customer_profiles` and `business_profiles`), `totalCount`, `hasMore`, and `currentPage`.
        - Verifies `customer_profiles` and `business_profiles` are fetched using `in` clause with `userIds`.
    - **Success with No Data:**
        - Given a `businessId` with no followers, returns `FollowersResult` with empty `items`, `totalCount: 0`, `hasMore: false`.
    - **Pagination:**
        - `range` method is called with correct `offset` and `limit` values.
        - `hasMore` is correctly calculated.
    - **Error Handling:**
        - Handles errors during count query.
        - Handles errors during subscriptions query.
        - Handles errors during customer profile fetch.
        - Handles errors during business profile fetch.

- **`likesService.fetchLikes`:**
    - **Success with Data:**
        - Given a `userId` and matching likes with `business_profiles`, returns `LikesResult` with correct `items`, `totalCount`, `hasMore`, and `currentPage`.
        - Verifies `business_profiles` are correctly transformed.
    - **Success with No Data:**
        - Given a `userId` with no likes, returns `LikesResult` with empty `items`, `totalCount: 0`, `hasMore: false`.
    - **Search Functionality:**
        - Given a `searchTerm`, `ilike` is called on `business_profiles.business_name` for both count and main queries.
        - Returns filtered results correctly.
    - **Pagination:**
        - `range` method is called with correct `offset` and `limit` values.
        - `hasMore` is correctly calculated.
    - **Error Handling:**
        - Handles errors during count query.
        - Handles errors during main data query.

- **`likesService.unlike`:**
    - **Success:**
        - Given a `likeId`, `delete` is called on the `likes` table with the correct `id`.
    - **Error Handling:**
        - Handles errors during the delete operation.

- **`likesService.fetchBusinessLikesReceived`:**
    - **Success with Data:**
        - Given a `businessId` and matching likes (both customer and business profiles), returns `BusinessLikesReceivedResult` with correctly transformed `items` (distinguishing `customer_profiles` and `business_profiles`), `totalCount`, `hasMore`, and `currentPage`.
        - Verifies `customer_profiles` and `business_profiles` are fetched using `in` clause with `userIds`.
    - **Success with No Data:**
        - Given a `businessId` with no likes, returns `BusinessLikesReceivedResult` with empty `items`, `totalCount: 0`, `hasMore: false`.
    - **Pagination:**
        - `range` method is called with correct `offset` and `limit` values.
        - `hasMore` is correctly calculated.
    - **Error Handling:**
        - Handles errors during count query.
        - Handles errors during likes query.
        - Handles errors during customer profile fetch.
        - Handles errors during business profile fetch.

- **`reviewsService.fetchReviews`:**
    - **Success with Data:**
        - Given a `userId` and matching reviews with `business_profiles`, returns `ReviewsResult` with correct `items`, `totalCount`, `hasMore`, and `currentPage`.
        - Verifies `business_profiles` are correctly transformed.
    - **Success with No Data:**
        - Given a `userId` with no reviews, returns `ReviewsResult` with empty `items`, `totalCount: 0`, `hasMore: false`.
    - **Search Functionality:**
        - Given a `searchTerm`, `ilike` is called on `business_profiles.business_name` for both count and main queries.
        - Returns filtered results correctly.
    - **Sorting Functionality:**
        - Tests all `sortBy` options (`newest`, `oldest`, `rating_high`, `rating_low`) and verifies `order` method calls.
    - **Pagination:**
        - `range` method is called with correct `offset` and `limit` values.
        - `hasMore` is correctly calculated.
    - **Error Handling:**
        - Handles errors during count query.
        - Handles errors during main data query.

- **`reviewsService.deleteReview`:**
    - **Success:**
        - Given a `reviewId`, `delete` is called on the `ratings_reviews` table with the correct `id`.
    - **Error Handling:**
        - Handles errors during the delete operation.

- **`reviewsService.updateReview`:**
    - **Success:**
        - Given a `reviewId`, `rating`, and `reviewText`, `update` is called on the `ratings_reviews` table with the correct `id`, `rating`, `review_text`, and `updated_at`.
    - **Error Handling:**
        - Handles errors during the update operation.

- **`reviewsService.fetchBusinessReviewsReceived`:**
    - **Success with Data:**
        - Given a `businessId` and matching reviews (both customer and business profiles), returns `BusinessReviewsReceivedResult` with correctly transformed `items` (distinguishing `customer_profiles` and `business_profiles`), `totalCount`, `hasMore`, and `currentPage`.
        - Verifies `customer_profiles` and `business_profiles` are fetched using `in` clause with `userIds`.
    - **Success with No Data:**
        - Given a `businessId` with no reviews, returns `BusinessReviewsReceivedResult` with empty `items`, `totalCount: 0`, `hasMore: false`.
    - **Sorting Functionality:**
        - Tests all `sortBy` options (`newest`, `oldest`, `rating_high`, `rating_low`) and verifies `order` method calls.
    - **Pagination:**
        - `range` method is called with correct `offset` and `limit` values.
        - `hasMore` is correctly calculated.
    - **Error Handling:**
        - Handles errors during count query.
        - Handles errors during reviews query.
        - Handles errors during customer profile fetch.
        - Handles errors during business profile fetch.

- **`getActivityMetrics`:**
    - **Success:**
        - Given a `userId`, returns `ActivityMetrics` with correct `likesCount`, `reviewCount`, `subscriptionCount`, and `lastUpdated`.
        - Verifies all three count queries are executed.
    - **Error Handling:**
        - Handles errors during any of the count queries and returns `null`.

Tasks:
1. Update the existing test file: `C:\web-app\dukancard\__tests__\lib\services\socialService.test.ts`.
2. Expand existing test cases to cover:
    - Empty data responses from `socialService`.
    - Pagination logic: verify correct `page` and `limit` parameters are passed.
    - `hasMore` flag propagation.
    - Edge cases for `totalCount` (e.g., when `totalCount` is 0).
3. Add new test cases for:
    - `fetchBusinessMyLikes` when `socialService.likesService.fetchLikes` returns an empty array.
    - `fetchBusinessLikesReceived` when `socialService.likesService.fetchBusinessLikesReceived` returns an empty array.
    - Explicitly test the return values (`items`, `totalCount`, `hasMore`, `currentPage`) for all success and error scenarios.
4. Ensure mocks for `socialService` and `createClient` are robust enough to simulate various scenarios.

File: `C:\web-app\dukancard\lib\services\socialService.ts`
Platform: dukancard
---