---
Status: Draft
Story: |
  As a developer, I need to implement comprehensive unit and integration tests for the `LikeCard` component in the `dukancard` project. This component is a shared UI element responsible for displaying information about a liked business or customer profile, including their name, avatar, location, and providing an "Unlike" action. The tests should ensure correct rendering based on `ProfileData` and various props, proper handling of the "Unlike" action, and accurate display of loading states and toast messages.

Acceptance Criteria:
- **Initial Rendering (Business Profile):**
    - Given `profile.type` is 'business' and `profile.slug` is present, when the component renders, then:
        - The avatar is clickable and links to `/${profile.slug}`.
        - The display name is clickable and links to `/${profile.slug}`.
        - The "Business" badge is displayed.
        - If `showRedirectIcon` is `true`, the `ExternalLink` icon is displayed next to the name.
        - If `showAddress` is `true`, the formatted location (locality, city, state) is displayed.
        - If `showVisitButton` is `true`, the "Visit Card" button is displayed and links to `/${profile.slug}`.
        - If `showUnlike` is `true`, the "Unlike" button is displayed.
- **Initial Rendering (Customer Profile):**
    - Given `profile.type` is 'customer', when the component renders, then:
        - The avatar is not clickable.
        - The display name is not clickable.
        - The "Customer" badge is displayed.
        - The `ExternalLink` icon is not displayed.
        - The formatted location is not displayed (as `showAddress` is `true` but customer profiles don't have locality, city, state).
        - The "Visit Card" button is not displayed.
        - If `showUnlike` is `true`, the "Unlike" button is displayed.
- **Avatar Fallback:**
    - Given `avatarUrl` is `null` or `undefined`, when the component renders, then the `AvatarFallback` displays the first letter of the `displayName` (or '?' if `displayName` is empty).
- **Location Formatting:**
    - Given `showAddress` is `true` and `profile` has `locality`, `city`, and `state`, then `formatLocation` returns a string like "Locality, City, State".
    - Given `showAddress` is `true` and `profile` has partial address data, then `formatLocation` correctly formats the available parts.
    - Given `showAddress` is `false`, then the location text is not rendered.
- **Unlike Action:**
    - Given `showUnlike` is `true` and `onUnlikeSuccess` is provided, when the "Unlike" button is clicked:
        - `isLoading` state becomes `true`, and the `Loader2` icon is displayed.
        - `unlikeBusiness` is called with `profile.id`.
        - If `unlikeBusiness` succeeds, `toast.success` is called with a success message, and `onUnlikeSuccess` is called with `likeId`.
        - If `unlikeBusiness` fails, `toast.error` is called with an error message.
        - `isLoading` state becomes `false` after the action completes (success or failure).
    - Given `showUnlike` is `false` or `onUnlikeSuccess` is not provided, when the "Unlike" button is clicked, then `unlikeBusiness` is not called.
- **Loading State (Unlike Button):**
    - Given `isLoading` is `true`, then the "Unlike" button is disabled.
- **Prop Variations:**
    - Test `variant='compact'` to ensure the `max-w-sm` class is applied.
    - Test `showVisitButton={false}` to ensure the "Visit Card" button is not rendered.
    - Test `showAddress={false}` to ensure the address section is not rendered.
    - Test `showRedirectIcon={true}` for business profiles to ensure the `ExternalLink` icon is rendered.

Tasks:
1. Create a new test file: `C:\web-app\dukancard\__tests__\app\components\shared\likes\LikeCard.test.tsx`.
2. Set up a testing environment that can render React components and mock `next/link` and `sonner` (for `toast`).
3. Mock the `unlikeBusiness` server action.
4. Write unit tests for the `LikeCard` component covering all acceptance criteria.
5. Test rendering with various `ProfileData` inputs (business with slug, business without slug, customer).
6. Simulate clicks on the "Unlike" button and verify `unlikeBusiness` calls and `toast` messages.
7. Verify loading state and button disabled state during the `unlikeBusiness` call.
8. Test conditional rendering based on `showUnlike`, `showVisitButton`, `showAddress`, and `showRedirectIcon` props.
9. Verify correct URL generation for `Link` components.

File: `C:\web-app\dukancard\app\components\shared\likes\LikeCard.tsx`
Platform: dukancard
---