---
Status: Draft
Story: |
  As a developer, I need to implement comprehensive unit tests for the `actions.ts` file within the `dukancard` project's business subscriptions module. This file contains server actions (`fetchBusinessSubscribers` and `fetchBusinessFollowing`) responsible for fetching subscription data from the `subscriptionsService`. The tests should ensure these actions correctly interact with the `subscriptionsService`, handle various data scenarios (including pagination and search), and gracefully manage service-level errors.

Acceptance Criteria:
- **`fetchBusinessSubscribers` Functionality:**
    - Given a valid `businessId`, `page`, and `limit`, when `fetchBusinessSubscribers` is called, then `subscriptionsService.fetchBusinessFollowers` is invoked with the correct parameters.
    - Given `subscriptionsService.fetchBusinessFollowers` returns data, then `fetchBusinessSubscribers` returns a `SubscribersResult` with correctly transformed `items`, `totalCount`, `hasMore`, and `currentPage`.
    - Given `subscriptionsService.fetchBusinessFollowers` throws an error, then `fetchBusinessSubscribers` catches the error and re-throws it.
- **`fetchBusinessFollowing` Functionality:**
    - Given a valid `businessId`, `page`, `limit`, and `searchTerm`, when `fetchBusinessFollowing` is called, then `subscriptionsService.fetchSubscriptions` is invoked with the correct parameters.
    - Given `subscriptionsService.fetchSubscriptions` returns data, then `fetchBusinessFollowing` returns a `SubscriptionsResult` with correctly transformed `items`, `totalCount`, `hasMore`, and `currentPage`.
    - Given `subscriptionsService.fetchSubscriptions` throws an error, then `fetchBusinessFollowing` catches the error and re-throws it.

Tasks:
1. Create a new test file: `C:\web-app\dukancard\__tests__\app\(dashboard)\dashboard\business\subscriptions\actions.test.ts`.
2. Set up a testing environment that can mock `subscriptionsService`.
3. Write unit tests for `fetchBusinessSubscribers` covering:
    - Successful data retrieval and transformation.
    - Error handling when `subscriptionsService.fetchBusinessFollowers` throws an error.
    - Correct parameter passing (businessId, page, limit).
4. Write unit tests for `fetchBusinessFollowing` covering:
    - Successful data retrieval and transformation.
    - Error handling when `subscriptionsService.fetchSubscriptions` throws an error.
    - Correct parameter passing (businessId, page, limit, searchTerm).

File: `C:\web-app\dukancard\app\(dashboard)\dashboard\business\subscriptions\actions.ts`
Platform: dukancard
---