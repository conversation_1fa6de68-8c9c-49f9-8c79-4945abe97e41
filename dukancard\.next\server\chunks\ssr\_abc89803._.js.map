{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/auth/actions.ts"], "sourcesContent": ["\"use server\";\r\n\r\nimport { createClient } from \"@/utils/supabase/server\";\r\nimport { redirect } from \"next/navigation\";\r\n// Removed unused headers import\r\n\r\nexport async function signOutUser() {\r\n  const supabase = await createClient();\r\n\r\n  try {\r\n    const { error: _error } = await supabase.auth.signOut();\r\n    // Note: Sign out errors are typically not critical for user experience\r\n    // The user will be redirected to login regardless\r\n\r\n    // Explicitly clear auth cookies to ensure logout\r\n    const cookieStore = await import(\"next/headers\").then((m) => m.cookies());\r\n    const cookiesToClear = [\"sb-access-token\", \"sb-refresh-token\"];\r\n\r\n    for (const cookieName of cookiesToClear) {\r\n      try {\r\n        cookieStore.set(cookieName, \"\", {\r\n          expires: new Date(0),\r\n          maxAge: -1,\r\n        });\r\n      } catch {\r\n        // Cookie clearing errors are not critical for sign out\r\n        // Continue with the sign out process\r\n      }\r\n    }\r\n  } catch {\r\n    // Even if sign out fails, redirect to login for security\r\n    // User will be treated as logged out\r\n  }\r\n\r\n  // Redirect to login with a flag to prevent middleware redirect loop\r\n  return redirect(\"/login?logged_out=true\");\r\n}\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAAA;;;;;;AAGO,eAAe;IACpB,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD;IAElC,IAAI;QACF,MAAM,EAAE,OAAO,MAAM,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;QACrD,uEAAuE;QACvE,kDAAkD;QAElD,iDAAiD;QACjD,MAAM,cAAc,MAAM,gIAAuB,IAAI,CAAC,CAAC,IAAM,EAAE,OAAO;QACtE,MAAM,iBAAiB;YAAC;YAAmB;SAAmB;QAE9D,KAAK,MAAM,cAAc,eAAgB;YACvC,IAAI;gBACF,YAAY,GAAG,CAAC,YAAY,IAAI;oBAC9B,SAAS,IAAI,KAAK;oBAClB,QAAQ,CAAC;gBACX;YACF,EAAE,OAAM;YACN,uDAAuD;YACvD,qCAAqC;YACvC;QACF;IACF,EAAE,OAAM;IACN,yDAAyD;IACzD,qCAAqC;IACvC;IAEA,oEAAoE;IACpE,OAAO,CAAA,GAAA,qLAAA,CAAA,WAAQ,AAAD,EAAE;AAClB;;;IA9BsB;;AAAA,+OAAA", "debugId": null}}, {"offset": {"line": 61, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/utils/addressValidation.ts"], "sourcesContent": ["/**\r\n * Customer address validation utility\r\n * Checks if customer has complete address information\r\n */\r\n\r\nexport interface CustomerAddressData {\r\n  pincode?: string | null;\r\n  state?: string | null;\r\n  city?: string | null;\r\n  locality?: string | null;\r\n  // address is optional as per requirements\r\n  address?: string | null;\r\n}\r\n\r\nexport interface CustomerProfileData extends CustomerAddressData {\r\n  name?: string | null;\r\n}\r\n\r\n/**\r\n * Validates if customer address is complete\r\n * Address field is optional, but pincode, state, city, and locality are required\r\n */\r\nexport function isCustomerAddressComplete(addressData: CustomerAddressData): boolean {\r\n  const { pincode, state, city, locality } = addressData;\r\n\r\n  // Check if required fields are present and not empty\r\n  return !!(\r\n    pincode && pincode.trim() !== '' &&\r\n    state && state.trim() !== '' &&\r\n    city && city.trim() !== '' &&\r\n    locality && locality.trim() !== ''\r\n  );\r\n}\r\n\r\n/**\r\n * Gets missing address fields for customer\r\n */\r\nexport function getMissingAddressFields(addressData: CustomerAddressData): string[] {\r\n  const missing: string[] = [];\r\n\r\n  if (!addressData.pincode || addressData.pincode.trim() === '') {\r\n    missing.push('pincode');\r\n  }\r\n  if (!addressData.state || addressData.state.trim() === '') {\r\n    missing.push('state');\r\n  }\r\n  if (!addressData.city || addressData.city.trim() === '') {\r\n    missing.push('city');\r\n  }\r\n  if (!addressData.locality || addressData.locality.trim() === '') {\r\n    missing.push('locality');\r\n  }\r\n\r\n  return missing;\r\n}\r\n\r\n/**\r\n * Generates a user-friendly message for missing address fields\r\n */\r\nexport function getAddressValidationMessage(missingFields: string[]): string {\r\n  if (missingFields.length === 0) {\r\n    return '';\r\n  }\r\n\r\n  const fieldNames = missingFields.map(field => {\r\n    switch (field) {\r\n      case 'pincode': return 'Pincode';\r\n      case 'state': return 'State';\r\n      case 'city': return 'City';\r\n      case 'locality': return 'Locality';\r\n      default: return field;\r\n    }\r\n  });\r\n\r\n  if (fieldNames.length === 1) {\r\n    return `Please update your ${fieldNames[0]} in your profile.`;\r\n  } else if (fieldNames.length === 2) {\r\n    return `Please update your ${fieldNames.join(' and ')} in your profile.`;\r\n  } else {\r\n    const lastField = fieldNames.pop();\r\n    return `Please update your ${fieldNames.join(', ')}, and ${lastField} in your profile.`;\r\n  }\r\n}\r\n\r\n/**\r\n * Validates if customer name is complete\r\n */\r\nexport function isCustomerNameComplete(name?: string | null): boolean {\r\n  return !!(name && name.trim() !== '');\r\n}\r\n\r\n/**\r\n * Validates if customer profile is complete (both name and address)\r\n */\r\nexport function isCustomerProfileComplete(profileData: CustomerProfileData): boolean {\r\n  return isCustomerNameComplete(profileData.name) && isCustomerAddressComplete(profileData);\r\n}\r\n\r\n/**\r\n * Gets missing profile fields for customer (name + address)\r\n */\r\nexport function getMissingProfileFields(profileData: CustomerProfileData): string[] {\r\n  const missing: string[] = [];\r\n\r\n  // Check name\r\n  if (!isCustomerNameComplete(profileData.name)) {\r\n    missing.push('name');\r\n  }\r\n\r\n  // Check address fields\r\n  const missingAddressFields = getMissingAddressFields(profileData);\r\n  missing.push(...missingAddressFields);\r\n\r\n  return missing;\r\n}\r\n\r\n/**\r\n * Generates a user-friendly message for missing profile fields (name + address)\r\n */\r\nexport function getProfileValidationMessage(missingFields: string[]): string {\r\n  if (missingFields.length === 0) {\r\n    return '';\r\n  }\r\n\r\n  const fieldNames = missingFields.map(field => {\r\n    switch (field) {\r\n      case 'name': return 'Name';\r\n      case 'pincode': return 'Pincode';\r\n      case 'state': return 'State';\r\n      case 'city': return 'City';\r\n      case 'locality': return 'Locality';\r\n      default: return field;\r\n    }\r\n  });\r\n\r\n  if (fieldNames.length === 1) {\r\n    return `Please update your ${fieldNames[0]} in your profile.`;\r\n  } else if (fieldNames.length === 2) {\r\n    return `Please update your ${fieldNames.join(' and ')} in your profile.`;\r\n  } else {\r\n    const lastField = fieldNames.pop();\r\n    return `Please update your ${fieldNames.join(', ')}, and ${lastField} in your profile.`;\r\n  }\r\n}\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;;AAmBM,SAAS,0BAA0B,WAAgC;IACxE,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG;IAE3C,qDAAqD;IACrD,OAAO,CAAC,CAAC,CACP,WAAW,QAAQ,IAAI,OAAO,MAC9B,SAAS,MAAM,IAAI,OAAO,MAC1B,QAAQ,KAAK,IAAI,OAAO,MACxB,YAAY,SAAS,IAAI,OAAO,EAClC;AACF;AAKO,SAAS,wBAAwB,WAAgC;IACtE,MAAM,UAAoB,EAAE;IAE5B,IAAI,CAAC,YAAY,OAAO,IAAI,YAAY,OAAO,CAAC,IAAI,OAAO,IAAI;QAC7D,QAAQ,IAAI,CAAC;IACf;IACA,IAAI,CAAC,YAAY,KAAK,IAAI,YAAY,KAAK,CAAC,IAAI,OAAO,IAAI;QACzD,QAAQ,IAAI,CAAC;IACf;IACA,IAAI,CAAC,YAAY,IAAI,IAAI,YAAY,IAAI,CAAC,IAAI,OAAO,IAAI;QACvD,QAAQ,IAAI,CAAC;IACf;IACA,IAAI,CAAC,YAAY,QAAQ,IAAI,YAAY,QAAQ,CAAC,IAAI,OAAO,IAAI;QAC/D,QAAQ,IAAI,CAAC;IACf;IAEA,OAAO;AACT;AAKO,SAAS,4BAA4B,aAAuB;IACjE,IAAI,cAAc,MAAM,KAAK,GAAG;QAC9B,OAAO;IACT;IAEA,MAAM,aAAa,cAAc,GAAG,CAAC,CAAA;QACnC,OAAQ;YACN,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAS,OAAO;YACrB,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAY,OAAO;YACxB;gBAAS,OAAO;QAClB;IACF;IAEA,IAAI,WAAW,MAAM,KAAK,GAAG;QAC3B,OAAO,CAAC,mBAAmB,EAAE,UAAU,CAAC,EAAE,CAAC,iBAAiB,CAAC;IAC/D,OAAO,IAAI,WAAW,MAAM,KAAK,GAAG;QAClC,OAAO,CAAC,mBAAmB,EAAE,WAAW,IAAI,CAAC,SAAS,iBAAiB,CAAC;IAC1E,OAAO;QACL,MAAM,YAAY,WAAW,GAAG;QAChC,OAAO,CAAC,mBAAmB,EAAE,WAAW,IAAI,CAAC,MAAM,MAAM,EAAE,UAAU,iBAAiB,CAAC;IACzF;AACF;AAKO,SAAS,uBAAuB,IAAoB;IACzD,OAAO,CAAC,CAAC,CAAC,QAAQ,KAAK,IAAI,OAAO,EAAE;AACtC;AAKO,SAAS,0BAA0B,WAAgC;IACxE,OAAO,uBAAuB,YAAY,IAAI,KAAK,0BAA0B;AAC/E;AAKO,SAAS,wBAAwB,WAAgC;IACtE,MAAM,UAAoB,EAAE;IAE5B,aAAa;IACb,IAAI,CAAC,uBAAuB,YAAY,IAAI,GAAG;QAC7C,QAAQ,IAAI,CAAC;IACf;IAEA,uBAAuB;IACvB,MAAM,uBAAuB,wBAAwB;IACrD,QAAQ,IAAI,IAAI;IAEhB,OAAO;AACT;AAKO,SAAS,4BAA4B,aAAuB;IACjE,IAAI,cAAc,MAAM,KAAK,GAAG;QAC9B,OAAO;IACT;IAEA,MAAM,aAAa,cAAc,GAAG,CAAC,CAAA;QACnC,OAAQ;YACN,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAS,OAAO;YACrB,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAY,OAAO;YACxB;gBAAS,OAAO;QAClB;IACF;IAEA,IAAI,WAAW,MAAM,KAAK,GAAG;QAC3B,OAAO,CAAC,mBAAmB,EAAE,UAAU,CAAC,EAAE,CAAC,iBAAiB,CAAC;IAC/D,OAAO,IAAI,WAAW,MAAM,KAAK,GAAG;QAClC,OAAO,CAAC,mBAAmB,EAAE,WAAW,IAAI,CAAC,SAAS,iBAAiB,CAAC;IAC1E,OAAO;QACL,MAAM,YAAY,WAAW,GAAG;QAChC,OAAO,CAAC,mBAAmB,EAAE,WAAW,IAAI,CAAC,MAAM,MAAM,EAAE,UAAU,iBAAiB,CAAC;IACzF;AACF", "debugId": null}}, {"offset": {"line": 173, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/actions/customerProfiles/addressValidation.ts"], "sourcesContent": ["\"use server\";\r\n\r\nimport { createClient } from \"@/utils/supabase/server\";\r\nimport { isCustomerAddressComplete, getMissingAddressFields, getAddressValidationMessage, type CustomerAddressData } from \"@/lib/utils/addressValidation\";\r\nimport { redirect } from \"next/navigation\";\r\n\r\n/**\r\n * Checks if customer has complete address information\r\n * Returns validation result and redirect URL if needed\r\n */\r\nexport async function validateCustomerAddress(userId: string): Promise<{\r\n  isValid: boolean;\r\n  missingFields?: string[];\r\n  message?: string;\r\n  redirectUrl?: string;\r\n}> {\r\n  const supabase = await createClient();\r\n  \r\n  try {\r\n    // Fetch customer address data\r\n    const { data: profile, error } = await supabase\r\n      .from('customer_profiles')\r\n      .select('pincode, state, city, locality, address')\r\n      .eq('id', userId)\r\n      .single();\r\n    \r\n    if (error) {\r\n      console.error('Error fetching customer profile for address validation:', error);\r\n      // If we can't fetch the profile, assume invalid and redirect\r\n      return {\r\n        isValid: false,\r\n        message: 'Unable to verify your address information. Please update your profile.',\r\n        redirectUrl: '/dashboard/customer/profile?message=Please update your address information'\r\n      };\r\n    }\r\n    \r\n    const addressData: CustomerAddressData = {\r\n      pincode: profile?.pincode,\r\n      state: profile?.state,\r\n      city: profile?.city,\r\n      locality: profile?.locality,\r\n      address: profile?.address\r\n    };\r\n    \r\n    const isValid = isCustomerAddressComplete(addressData);\r\n    \r\n    if (!isValid) {\r\n      const missingFields = getMissingAddressFields(addressData);\r\n      const message = getAddressValidationMessage(missingFields);\r\n      const redirectUrl = `/dashboard/customer/profile?message=${encodeURIComponent(message)}`;\r\n      \r\n      return {\r\n        isValid: false,\r\n        missingFields,\r\n        message,\r\n        redirectUrl\r\n      };\r\n    }\r\n    \r\n    return { isValid: true };\r\n    \r\n  } catch (error) {\r\n    console.error('Unexpected error during address validation:', error);\r\n    return {\r\n      isValid: false,\r\n      message: 'An error occurred while validating your address. Please update your profile.',\r\n      redirectUrl: '/dashboard/customer/profile?message=Please update your address information'\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Middleware function to check address and redirect if incomplete\r\n * Use this in customer dashboard pages\r\n */\r\nexport async function requireCompleteAddress(userId: string): Promise<void> {\r\n  const validation = await validateCustomerAddress(userId);\r\n\r\n  if (!validation.isValid && validation.redirectUrl) {\r\n    redirect(validation.redirectUrl);\r\n  }\r\n}\r\n\r\n/**\r\n * Checks if customer has complete name information\r\n * Returns validation result and redirect URL if needed\r\n */\r\nexport async function validateCustomerName(userId: string): Promise<{\r\n  isValid: boolean;\r\n  message?: string;\r\n  redirectUrl?: string;\r\n}> {\r\n  const supabase = await createClient();\r\n\r\n  try {\r\n    // Fetch customer name data\r\n    const { data: profile, error } = await supabase\r\n      .from('customer_profiles')\r\n      .select('name')\r\n      .eq('id', userId)\r\n      .single();\r\n\r\n    if (error) {\r\n      console.error('Error fetching customer profile for name validation:', error);\r\n      // If we can't fetch the profile, assume invalid and redirect\r\n      return {\r\n        isValid: false,\r\n        message: 'Unable to verify your profile information. Please update your profile.',\r\n        redirectUrl: '/dashboard/customer/profile?message=Please update your profile information'\r\n      };\r\n    }\r\n\r\n    // Check if name is present and not empty\r\n    const isValid = !!(profile?.name && profile.name.trim() !== '');\r\n\r\n    if (!isValid) {\r\n      const message = 'Please complete your name in your profile to access the dashboard.';\r\n      const redirectUrl = `/dashboard/customer/profile?message=${encodeURIComponent(message)}`;\r\n\r\n      return {\r\n        isValid: false,\r\n        message,\r\n        redirectUrl\r\n      };\r\n    }\r\n\r\n    return { isValid: true };\r\n\r\n  } catch (error) {\r\n    console.error('Unexpected error during name validation:', error);\r\n    return {\r\n      isValid: false,\r\n      message: 'An error occurred while validating your profile. Please update your profile.',\r\n      redirectUrl: '/dashboard/customer/profile?message=Please update your profile information'\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Middleware function to check name and redirect if incomplete\r\n * Use this in customer dashboard pages\r\n */\r\nexport async function requireCompleteName(userId: string): Promise<void> {\r\n  const validation = await validateCustomerName(userId);\r\n\r\n  if (!validation.isValid && validation.redirectUrl) {\r\n    redirect(validation.redirectUrl);\r\n  }\r\n}\r\n\r\n/**\r\n * Middleware function to check both address and name, redirect if incomplete\r\n * Use this in customer dashboard pages (except settings page)\r\n * Settings page is exempt from address validation\r\n */\r\nexport async function requireCompleteProfile(userId: string, exemptFromAddressValidation: boolean = false): Promise<void> {\r\n  // Always check name (required for all dashboard access)\r\n  await requireCompleteName(userId);\r\n\r\n  // Only check address if not exempt (settings page is exempt)\r\n  if (!exemptFromAddressValidation) {\r\n    await requireCompleteAddress(userId);\r\n  }\r\n}\r\n\r\n/**\r\n * Get customer address data for forms\r\n */\r\nexport async function getCustomerAddressData(userId: string): Promise<{\r\n  data?: CustomerAddressData;\r\n  error?: string;\r\n}> {\r\n  const supabase = await createClient();\r\n  \r\n  try {\r\n    const { data: profile, error } = await supabase\r\n      .from('customer_profiles')\r\n      .select('pincode, state, city, locality, address')\r\n      .eq('id', userId)\r\n      .single();\r\n    \r\n    if (error) {\r\n      console.error('Error fetching customer address data:', error);\r\n      return { error: 'Failed to fetch address data' };\r\n    }\r\n    \r\n    return {\r\n      data: {\r\n        pincode: profile?.pincode,\r\n        state: profile?.state,\r\n        city: profile?.city,\r\n        locality: profile?.locality,\r\n        address: profile?.address\r\n      }\r\n    };\r\n    \r\n  } catch (error) {\r\n    console.error('Unexpected error fetching address data:', error);\r\n    return { error: 'An unexpected error occurred' };\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;AACA;AACA;AAAA;;;;;;;AAMO,eAAe,wBAAwB,MAAc;IAM1D,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD;IAElC,IAAI;QACF,8BAA8B;QAC9B,MAAM,EAAE,MAAM,OAAO,EAAE,KAAK,EAAE,GAAG,MAAM,SACpC,IAAI,CAAC,qBACL,MAAM,CAAC,2CACP,EAAE,CAAC,MAAM,QACT,MAAM;QAET,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,2DAA2D;YACzE,6DAA6D;YAC7D,OAAO;gBACL,SAAS;gBACT,SAAS;gBACT,aAAa;YACf;QACF;QAEA,MAAM,cAAmC;YACvC,SAAS,SAAS;YAClB,OAAO,SAAS;YAChB,MAAM,SAAS;YACf,UAAU,SAAS;YACnB,SAAS,SAAS;QACpB;QAEA,MAAM,UAAU,CAAA,GAAA,iIAAA,CAAA,4BAAyB,AAAD,EAAE;QAE1C,IAAI,CAAC,SAAS;YACZ,MAAM,gBAAgB,CAAA,GAAA,iIAAA,CAAA,0BAAuB,AAAD,EAAE;YAC9C,MAAM,UAAU,CAAA,GAAA,iIAAA,CAAA,8BAA2B,AAAD,EAAE;YAC5C,MAAM,cAAc,CAAC,oCAAoC,EAAE,mBAAmB,UAAU;YAExF,OAAO;gBACL,SAAS;gBACT;gBACA;gBACA;YACF;QACF;QAEA,OAAO;YAAE,SAAS;QAAK;IAEzB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,+CAA+C;QAC7D,OAAO;YACL,SAAS;YACT,SAAS;YACT,aAAa;QACf;IACF;AACF;AAMO,eAAe,uBAAuB,MAAc;IACzD,MAAM,aAAa,MAAM,wBAAwB;IAEjD,IAAI,CAAC,WAAW,OAAO,IAAI,WAAW,WAAW,EAAE;QACjD,CAAA,GAAA,qLAAA,CAAA,WAAQ,AAAD,EAAE,WAAW,WAAW;IACjC;AACF;AAMO,eAAe,qBAAqB,MAAc;IAKvD,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD;IAElC,IAAI;QACF,2BAA2B;QAC3B,MAAM,EAAE,MAAM,OAAO,EAAE,KAAK,EAAE,GAAG,MAAM,SACpC,IAAI,CAAC,qBACL,MAAM,CAAC,QACP,EAAE,CAAC,MAAM,QACT,MAAM;QAET,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,wDAAwD;YACtE,6DAA6D;YAC7D,OAAO;gBACL,SAAS;gBACT,SAAS;gBACT,aAAa;YACf;QACF;QAEA,yCAAyC;QACzC,MAAM,UAAU,CAAC,CAAC,CAAC,SAAS,QAAQ,QAAQ,IAAI,CAAC,IAAI,OAAO,EAAE;QAE9D,IAAI,CAAC,SAAS;YACZ,MAAM,UAAU;YAChB,MAAM,cAAc,CAAC,oCAAoC,EAAE,mBAAmB,UAAU;YAExF,OAAO;gBACL,SAAS;gBACT;gBACA;YACF;QACF;QAEA,OAAO;YAAE,SAAS;QAAK;IAEzB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4CAA4C;QAC1D,OAAO;YACL,SAAS;YACT,SAAS;YACT,aAAa;QACf;IACF;AACF;AAMO,eAAe,oBAAoB,MAAc;IACtD,MAAM,aAAa,MAAM,qBAAqB;IAE9C,IAAI,CAAC,WAAW,OAAO,IAAI,WAAW,WAAW,EAAE;QACjD,CAAA,GAAA,qLAAA,CAAA,WAAQ,AAAD,EAAE,WAAW,WAAW;IACjC;AACF;AAOO,eAAe,uBAAuB,MAAc,EAAE,8BAAuC,KAAK;IACvG,wDAAwD;IACxD,MAAM,oBAAoB;IAE1B,6DAA6D;IAC7D,IAAI,CAAC,6BAA6B;QAChC,MAAM,uBAAuB;IAC/B;AACF;AAKO,eAAe,uBAAuB,MAAc;IAIzD,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD;IAElC,IAAI;QACF,MAAM,EAAE,MAAM,OAAO,EAAE,KAAK,EAAE,GAAG,MAAM,SACpC,IAAI,CAAC,qBACL,MAAM,CAAC,2CACP,EAAE,CAAC,MAAM,QACT,MAAM;QAET,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,yCAAyC;YACvD,OAAO;gBAAE,OAAO;YAA+B;QACjD;QAEA,OAAO;YACL,MAAM;gBACJ,SAAS,SAAS;gBAClB,OAAO,SAAS;gBAChB,MAAM,SAAS;gBACf,UAAU,SAAS;gBACnB,SAAS,SAAS;YACpB;QACF;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2CAA2C;QACzD,OAAO;YAAE,OAAO;QAA+B;IACjD;AACF;;;IA9LsB;IAiEA;IAYA;IAuDA;IAaA;IAaA;;AA9JA,+OAAA;AAiEA,+OAAA;AAYA,+OAAA;AAuDA,+OAAA;AAaA,+OAAA;AAaA,+OAAA", "debugId": null}}, {"offset": {"line": 342, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/supabase/constants.ts"], "sourcesContent": ["// lib/supabase/constants.ts\r\n\r\nexport const TABLES = {\r\n  BLOGS: \"blogs\",\r\n  BUSINESS_ACTIVITIES: \"business_activities\",\r\n  BUSINESS_PROFILES: \"business_profiles\",\r\n  CARD_VISITS: \"card_visits\",\r\n  CUSTOMER_POSTS: \"customer_posts\",\r\n  CUSTOMER_PROFILES: \"customer_profiles\",\r\n  LIKES: \"likes\",\r\n  PAYMENT_SUBSCRIPTIONS: \"payment_subscriptions\",\r\n  PINCODES: \"pincodes\",\r\n  PRODUCTS_SERVICES: \"products_services\",\r\n  PRODUCT_VARIANTS: \"product_variants\",\r\n  STORAGE_CLEANUP_CONFIG: \"storage_cleanup_config\",\r\n  STORAGE_CLEANUP_PROGRESS: \"storage_cleanup_progress\",\r\n  SUBSCRIPTIONS: \"subscriptions\",\r\n  SYSTEM_ALERTS: \"system_alerts\",\r\n  RATINGS_REVIEWS: \"ratings_reviews\",\r\n} as const;\r\n\r\nexport const BUCKETS = {\r\n  BUSINESS: \"business\",\r\n  CUSTOMERS: \"customers\",\r\n} as const;\r\n\r\nexport const COLUMNS = {\r\n  ID: \"id\",\r\n  CREATED_AT: \"created_at\",\r\n  UPDATED_AT: \"updated_at\",\r\n  NAME: \"name\",\r\n  EMAIL: \"email\",\r\n  PHONE: \"phone\",\r\n  CITY: \"city\",\r\n  STATE: \"state\",\r\n  PINCODE: \"pincode\",\r\n  PLAN_ID: \"plan_id\",\r\n  LOCALITY: \"locality\",\r\n  CITY_SLUG: \"city_slug\",\r\n  STATE_SLUG: \"state_slug\",\r\n  LOCALITY_SLUG: \"locality_slug\",\r\n  LOGO_URL: \"logo_url\",\r\n  IMAGE_URL: \"image_url\",\r\n  IMAGES: \"images\",\r\n  SLUG: \"slug\",\r\n  STATUS: \"status\",\r\n  CONTENT: \"content\",\r\n  GALLERY: \"gallery\",\r\n  DESCRIPTION: \"description\",\r\n  TITLE: \"title\",\r\n  USER_ID: \"user_id\",\r\n  BUSINESS_ID: \"business_id\",\r\n  BUSINESS_NAME: \"business_name\",\r\n  BUSINESS_SLUG: \"business_slug\",\r\n  PRODUCT_ID: \"product_id\",\r\n  PRODUCT_TYPE: \"product_type\",\r\n  BUSINESS_PROFILE_ID: \"business_profile_id\",\r\n  RAZORPAY_SUBSCRIPTION_ID: \"razorpay_subscription_id\",\r\n  SUBSCRIPTION_STATUS: \"subscription_status\",\r\n  RATING: \"rating\",\r\n  REVIEW_TEXT: \"review_text\",\r\n  AVATAR_URL: \"avatar_url\",\r\n  ADDRESS_LINE: \"address_line\"\r\n} as const;"], "names": [], "mappings": "AAAA,4BAA4B;;;;;;AAErB,MAAM,SAAS;IACpB,OAAO;IACP,qBAAqB;IACrB,mBAAmB;IACnB,aAAa;IACb,gBAAgB;IAChB,mBAAmB;IACnB,OAAO;IACP,uBAAuB;IACvB,UAAU;IACV,mBAAmB;IACnB,kBAAkB;IAClB,wBAAwB;IACxB,0BAA0B;IAC1B,eAAe;IACf,eAAe;IACf,iBAAiB;AACnB;AAEO,MAAM,UAAU;IACrB,UAAU;IACV,WAAW;AACb;AAEO,MAAM,UAAU;IACrB,IAAI;IACJ,YAAY;IACZ,YAAY;IACZ,MAAM;IACN,OAAO;IACP,OAAO;IACP,MAAM;IACN,OAAO;IACP,SAAS;IACT,SAAS;IACT,UAAU;IACV,WAAW;IACX,YAAY;IACZ,eAAe;IACf,UAAU;IACV,WAAW;IACX,QAAQ;IACR,MAAM;IACN,QAAQ;IACR,SAAS;IACT,SAAS;IACT,aAAa;IACb,OAAO;IACP,SAAS;IACT,aAAa;IACb,eAAe;IACf,eAAe;IACf,YAAY;IACZ,cAAc;IACd,qBAAqB;IACrB,0BAA0B;IAC1B,qBAAqB;IACrB,QAAQ;IACR,aAAa;IACb,YAAY;IACZ,cAAc;AAChB", "debugId": null}}, {"offset": {"line": 414, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/services/socialService.ts"], "sourcesContent": ["/**\n * Unified Social Service for Next.js\n * Handles subscriptions, likes, and reviews data fetching and management\n * Supports both customer and business contexts with identical backend functionality to React Native\n */\n\nimport { createClient } from '@/utils/supabase/server';\nimport { Database } from '@/types/supabase';\nimport { TABLES, COLUMNS } from '@/lib/supabase/constants';\n\nexport interface ActivityMetrics {\n  likesCount: number;\n  reviewCount: number;\n  subscriptionCount: number;\n  lastUpdated: string;\n}\n\n// Types for subscriptions\nexport interface SubscriptionWithProfile {\n  id: string;\n  business_profiles: Database['public']['Tables']['business_profiles']['Row'] | null;\n}\n\nexport interface SubscriptionsResult {\n  items: SubscriptionWithProfile[];\n  totalCount: number;\n  hasMore: boolean;\n  currentPage: number;\n}\n\n// Types for likes\nexport interface LikeWithProfile {\n  id: string;\n  business_profiles: Database['public']['Tables']['business_profiles']['Row'] | null;\n}\n\nexport interface LikesResult {\n  items: LikeWithProfile[];\n  totalCount: number;\n  hasMore: boolean;\n  currentPage: number;\n}\n\n// Types for reviews\nexport interface ReviewBusinessProfile {\n  id: string;\n  business_name: string | null;\n  business_slug: string | null;\n  logo_url: string | null;\n}\n\nexport interface ReviewData {\n  id: string;\n  rating: number;\n  review_text: string | null;\n  created_at: string;\n  updated_at: string;\n  business_profile_id: string;\n  user_id: string;\n  business_profiles: ReviewBusinessProfile | null;\n}\n\nexport interface ReviewsResult {\n  items: ReviewData[];\n  totalCount: number;\n  hasMore: boolean;\n  currentPage: number;\n}\n\n// Business-specific types for received interactions\nexport interface BusinessLikeReceived {\n  id: string;\n  user_id: string;\n  customer_profiles?: {\n    id: string;\n    name: string | null;\n    email: string | null;\n    avatar_url: string | null;\n  } | null;\n  business_profiles?: {\n    id: string;\n    business_name: string | null;\n    business_slug: string | null;\n    logo_url: string | null;\n    city: string | null;\n    state: string | null;\n    pincode: string | null;\n    address_line: string | null;\n    locality: string | null;\n  } | null;\n  profile_type: 'customer' | 'business';\n}\n\nexport interface BusinessLikesReceivedResult {\n  items: BusinessLikeReceived[];\n  totalCount: number;\n  hasMore: boolean;\n  currentPage: number;\n}\n\n// Follower types for business\nexport interface FollowerProfileData {\n  id: string;\n  name: string | null;\n  slug: string | null;\n  logo_url?: string | null;\n  avatar_url?: string | null;\n  city: string | null;\n  state: string | null;\n  pincode: string | null;\n  address_line: string | null;\n  type: \"business\" | \"customer\";\n}\n\nexport interface FollowerWithProfile {\n  id: string;\n  profile: FollowerProfileData | null;\n}\n\nexport interface FollowersResult {\n  items: FollowerWithProfile[];\n  totalCount: number;\n  hasMore: boolean;\n  currentPage: number;\n}\n\n/**\n * Subscriptions Service\n */\nexport const subscriptionsService = {\n  /**\n   * Fetch user subscriptions with pagination and search\n   */\n  async fetchSubscriptions(\n    userId: string,\n    page: number = 1,\n    limit: number = 20,\n    searchTerm: string = \"\"\n  ): Promise<SubscriptionsResult> {\n    try {\n      const supabase = await createClient();\n\n      // Get total count first with separate query to avoid count issues with joins\n      let countQuery = supabase\n        .from(TABLES.SUBSCRIPTIONS)\n        .select(\n          `\n          ${COLUMNS.ID},\n          ${TABLES.BUSINESS_PROFILES}!inner (\n            ${COLUMNS.ID},\n            ${COLUMNS.BUSINESS_NAME}\n          )\n        `,\n          { count: \"exact\", head: true }\n        )\n        .eq(COLUMNS.USER_ID, userId);\n\n      // Apply search filter to count query if provided\n      if (searchTerm && searchTerm.trim()) {\n        countQuery = countQuery.ilike(\n          `${TABLES.BUSINESS_PROFILES}.${COLUMNS.BUSINESS_NAME}`,\n          `%${searchTerm.trim()}%`\n        );\n      }\n\n      const { count: totalCount, error: countError } = await countQuery;\n\n      if (countError) {\n        throw new Error(`Failed to get subscriptions count: ${countError.message}`);\n      }\n\n      // If no subscriptions, return empty result\n      if (!totalCount || totalCount === 0) {\n        return {\n          items: [],\n          totalCount: 0,\n          hasMore: false,\n          currentPage: page,\n        };\n      }\n\n      // Build the main query for fetching data\n      let query = supabase\n        .from(TABLES.SUBSCRIPTIONS)\n        .select(\n          `\n          ${COLUMNS.ID},\n          ${COLUMNS.BUSINESS_PROFILE_ID},\n          ${TABLES.BUSINESS_PROFILES}!inner (*)\n        `\n        )\n        .eq(COLUMNS.USER_ID, userId);\n\n      // Apply search filter if provided\n      if (searchTerm && searchTerm.trim()) {\n        query = query.ilike(\n          `${TABLES.BUSINESS_PROFILES}.${COLUMNS.BUSINESS_NAME}`,\n          `%${searchTerm.trim()}%`\n        );\n      }\n\n      // Apply pagination\n      const offset = (page - 1) * limit;\n      console.log(`[subscriptionsService.fetchSubscriptions] Applying pagination: offset=${offset}, limit=${limit}`);\n      query = query.range(offset, offset + limit - 1);\n\n      const { data: subscriptionsWithProfiles, error } = await query;\n      console.log(`[subscriptionsService.fetchSubscriptions] Query returned ${subscriptionsWithProfiles?.length} items`);\n\n      if (error) {\n        throw new Error(`Failed to fetch subscriptions: ${error.message}`);\n      }\n\n      // Transform the data\n      const transformedSubscriptions: SubscriptionWithProfile[] = (\n        subscriptionsWithProfiles || []\n      ).map(\n        (sub: {\n          id: string;\n          business_profiles: Database['public']['Tables']['business_profiles']['Row'] | Database['public']['Tables']['business_profiles']['Row'][];\n        }) => ({\n          id: sub.id,\n          business_profiles: Array.isArray(sub.business_profiles)\n            ? sub.business_profiles[0]\n            : sub.business_profiles,\n        })\n      );\n\n      const hasMore = totalCount > offset + limit;\n\n      return {\n        items: transformedSubscriptions,\n        totalCount,\n        hasMore,\n        currentPage: page,\n      };\n    } catch (error) {\n      console.error(\"Error in fetchSubscriptions:\", error);\n      throw error;\n    }\n  },\n\n  /**\n   * Unsubscribe from a business\n   */\n  async unsubscribe(subscriptionId: string): Promise<void> {\n    try {\n      const supabase = await createClient();\n      const { error } = await supabase\n        .from(TABLES.SUBSCRIPTIONS)\n        .delete()\n        .eq(COLUMNS.ID, subscriptionId);\n\n      if (error) {\n        throw new Error(`Failed to unsubscribe: ${error.message}`);\n      }\n    } catch (error) {\n      console.error(\"Error in unsubscribe:\", error);\n      throw error;\n    }\n  },\n\n  /**\n   * Fetch followers to a business (both customers and other businesses)\n   */\n  async fetchBusinessFollowers(\n    businessId: string,\n    page: number = 1,\n    limit: number = 10\n  ): Promise<FollowersResult> {\n    try {\n      const supabase = await createClient();\n      \n      // Calculate offset for pagination\n      const offset = (page - 1) * limit;\n\n      // Get total count first\n      const { count: totalCount, error: countError } = await supabase\n        .from(TABLES.SUBSCRIPTIONS)\n        .select(\"*\", { count: \"exact\", head: true })\n        .eq(COLUMNS.BUSINESS_PROFILE_ID, businessId);\n\n      if (countError) {\n        throw new Error(\"Failed to fetch subscription count\");\n      }\n\n      if (!totalCount || totalCount === 0) {\n        return {\n          items: [],\n          totalCount: 0,\n          hasMore: false,\n          currentPage: page,\n        };\n      }\n\n      // Get paginated subscriptions (database-level pagination)\n      const { data: subscriptions, error: subsError } = await supabase\n        .from(TABLES.SUBSCRIPTIONS)\n        .select(`${COLUMNS.ID}, ${COLUMNS.USER_ID}, ${COLUMNS.CREATED_AT}`)\n        .eq(COLUMNS.BUSINESS_PROFILE_ID, businessId)\n        .order(COLUMNS.CREATED_AT, { ascending: false })\n        .range(offset, offset + limit - 1);\n\n      if (subsError) {\n        throw new Error(\"Failed to fetch subscriptions\");\n      }\n\n      if (!subscriptions || subscriptions.length === 0) {\n        return {\n          items: [],\n          totalCount: totalCount || 0,\n          hasMore: false,\n          currentPage: page,\n        };\n      }\n\n      // Get user IDs from the paginated subscriptions only\n      const userIds = subscriptions.map((sub) => sub.user_id);\n\n      // Fetch profiles for only the paginated user IDs (not all users)\n      const [customerProfiles, businessProfiles] = await Promise.all([\n        supabase\n          .from(TABLES.CUSTOMER_PROFILES)\n          .select(`${COLUMNS.ID}, ${COLUMNS.NAME}, ${COLUMNS.AVATAR_URL}`)\n          .in(COLUMNS.ID, userIds),\n        supabase\n          .from(TABLES.BUSINESS_PROFILES)\n          .select(\n            `${COLUMNS.ID}, ${COLUMNS.BUSINESS_NAME}, ${COLUMNS.BUSINESS_SLUG}, ${COLUMNS.LOGO_URL}, ${COLUMNS.CITY}, ${COLUMNS.STATE}, ${COLUMNS.PINCODE}, ${COLUMNS.ADDRESS_LINE}`\n          )\n          .in(COLUMNS.ID, userIds),\n      ]);\n\n      if (customerProfiles.error) {\n        throw new Error(\"Failed to fetch customer profiles\");\n      }\n\n      if (businessProfiles.error) {\n        throw new Error(\"Failed to fetch business profiles\");\n      }\n\n      // Create profile maps for efficient lookup\n      const customerMap = new Map(\n        customerProfiles.data?.map((p) => [p.id, p]) || []\n      );\n      const businessMap = new Map(\n        businessProfiles.data?.map((p) => [p.id, p]) || []\n      );\n\n      // Transform subscriptions to include profile data\n      const allFollowers: FollowerWithProfile[] = subscriptions\n        .map((sub) => {\n          const customerProfile = customerMap.get(sub.user_id);\n          const businessProfile = businessMap.get(sub.user_id);\n\n          if (customerProfile) {\n            return {\n              id: sub.id,\n              profile: {\n                id: customerProfile.id,\n                name: customerProfile.name,\n                slug: null,\n                avatar_url: customerProfile.avatar_url,\n                city: null,\n                state: null,\n                pincode: null,\n                address_line: null,\n                type: \"customer\" as const,\n              },\n            };\n          } else if (businessProfile) {\n            return {\n              id: sub.id,\n              profile: {\n                id: businessProfile.id,\n                name: businessProfile.business_name,\n                slug: businessProfile.business_slug,\n                logo_url: businessProfile.logo_url,\n                city: businessProfile.city,\n                state: businessProfile.state,\n                pincode: businessProfile.pincode,\n                address_line: businessProfile.address_line,\n                type: \"business\" as const,\n              },\n            };\n          }\n          return null;\n        })\n        .filter((sub) => sub !== null) as FollowerWithProfile[];\n\n      // Calculate hasMore based on database-level pagination\n      const hasMore = totalCount > offset + limit;\n\n      return {\n        items: allFollowers,\n        totalCount: totalCount || 0,\n        hasMore,\n        currentPage: page,\n      };\n    } catch (error) {\n      throw error;\n    }\n  },\n};\n\n/**\n * Likes Service\n */\nexport const likesService = {\n  /**\n   * Fetch user likes with pagination and search\n   */\n  async fetchLikes(\n    userId: string,\n    page: number = 1,\n    limit: number = 20,\n    searchTerm: string = \"\"\n  ): Promise<LikesResult> {\n    try {\n      const supabase = await createClient();\n\n      // Build query with proper joins and filtering\n      let query = supabase\n        .from(TABLES.LIKES)\n        .select(\n          `\n          ${COLUMNS.ID},\n          ${TABLES.BUSINESS_PROFILES}!inner (*)\n        `\n        )\n        .eq(COLUMNS.USER_ID, userId);\n\n      // Apply search filter if provided\n      if (searchTerm && searchTerm.trim()) {\n        query = query.ilike(\n          `${TABLES.BUSINESS_PROFILES}.${COLUMNS.BUSINESS_NAME}`,\n          `%${searchTerm.trim()}%`\n        );\n      }\n\n      // Get total count for pagination\n      let countQuery = supabase\n        .from(TABLES.LIKES)\n        .select(\n          `\n          ${COLUMNS.ID},\n          ${TABLES.BUSINESS_PROFILES}!inner (\n            ${COLUMNS.ID},\n            ${COLUMNS.BUSINESS_NAME}\n          )\n        `,\n          { count: \"exact\", head: true }\n        )\n        .eq(COLUMNS.USER_ID, userId);\n\n      if (searchTerm && searchTerm.trim()) {\n        countQuery = countQuery.ilike(\n          `${TABLES.BUSINESS_PROFILES}.${COLUMNS.BUSINESS_NAME}`,\n          `%${searchTerm.trim()}%`\n        );\n      }\n\n      const { count: totalCount, error: countError } = await countQuery;\n\n      if (countError) {\n        throw new Error(`Failed to get likes count: ${countError.message}`);\n      }\n\n      // If no likes, return empty result\n      if (!totalCount || totalCount === 0) {\n        return {\n          items: [],\n          totalCount: 0,\n          hasMore: false,\n          currentPage: page,\n        };\n      }\n\n      // Apply pagination to the main query\n      const offset = (page - 1) * limit;\n      console.log(`[likesService.fetchLikes] Applying pagination: offset=${offset}, limit=${limit}`);\n      query = query.range(offset, offset + limit - 1);\n\n      const { data: likesWithProfiles, error } = await query;\n      console.log(`[likesService.fetchLikes] Query returned ${likesWithProfiles?.length} items`);\n\n      if (error) {\n        throw new Error(`Failed to fetch likes: ${error.message}`);\n      }\n\n      // Transform the data\n      const transformedLikes: LikeWithProfile[] = (likesWithProfiles || []).map(\n        (like: {\n          id: string;\n          business_profiles: Database['public']['Tables']['business_profiles']['Row'] | Database['public']['Tables']['business_profiles']['Row'][];\n        }) => ({\n          id: like.id,\n          business_profiles: Array.isArray(like.business_profiles)\n            ? like.business_profiles[0]\n            : like.business_profiles,\n        })\n      );\n\n      const hasMore = totalCount > offset + limit;\n\n      return {\n        items: transformedLikes,\n        totalCount,\n        hasMore,\n        currentPage: page,\n      };\n    } catch (error) {\n      console.error(\"Error in fetchLikes:\", error);\n      throw error;\n    }\n  },\n\n  /**\n   * Unlike a business\n   */\n  async unlike(likeId: string): Promise<void> {\n    try {\n      const supabase = await createClient();\n      const { error } = await supabase.from(TABLES.LIKES).delete().eq(COLUMNS.ID, likeId);\n\n      if (error) {\n        throw new Error(`Failed to unlike: ${error.message}`);\n      }\n    } catch (error) {\n      console.error(\"Error in unlike:\", error);\n      throw error;\n    }\n  },\n\n  /**\n   * Fetch likes received by a business (customers/businesses who liked this business)\n   */\n  async fetchBusinessLikesReceived(\n    businessId: string,\n    page: number = 1,\n    limit: number = 10\n  ): Promise<BusinessLikesReceivedResult> {\n    try {\n      const supabase = await createClient();\n\n      // Get total count first\n      const { count: totalCount, error: countError } = await supabase\n        .from(TABLES.LIKES)\n        .select(COLUMNS.ID, { count: \"exact\", head: true })\n        .eq(COLUMNS.BUSINESS_PROFILE_ID, businessId);\n\n      if (countError) {\n        throw new Error(\"Failed to get total count\");\n      }\n\n      if (!totalCount || totalCount === 0) {\n        return {\n          items: [],\n          totalCount: 0,\n          hasMore: false,\n          currentPage: page,\n        };\n      }\n\n      // Get likes with pagination (database-level pagination)\n      const from = (page - 1) * limit;\n      console.log(`[likesService.fetchBusinessLikesReceived] Applying pagination: from=${from}, limit=${limit}`);\n      const { data: likes, error: likesError } = await supabase\n        .from(TABLES.LIKES)\n        .select(`${COLUMNS.ID}, ${COLUMNS.USER_ID}`)\n        .eq(COLUMNS.BUSINESS_PROFILE_ID, businessId)\n        .range(from, from + limit - 1);\n      console.log(`[likesService.fetchBusinessLikesReceived] Query returned ${likes?.length} items`);\n\n      if (likesError) {\n        throw new Error(\"Failed to fetch likes\");\n      }\n\n      if (!likes || likes.length === 0) {\n        return {\n          items: [],\n          totalCount,\n          hasMore: false,\n          currentPage: page,\n        };\n      }\n\n      // Get user IDs for the paginated results only\n      const userIds = likes.map((like) => like.user_id);\n\n      // Fetch customer and business profiles for paginated results only\n      const [customerProfiles, businessProfiles] = await Promise.all([\n        supabase\n          .from(TABLES.CUSTOMER_PROFILES)\n          .select(`${COLUMNS.ID}, ${COLUMNS.NAME}, ${COLUMNS.EMAIL}, ${COLUMNS.AVATAR_URL}`)\n          .in(COLUMNS.ID, userIds),\n        supabase\n          .from(TABLES.BUSINESS_PROFILES)\n          .select(\n            `${COLUMNS.ID}, ${COLUMNS.BUSINESS_NAME}, ${COLUMNS.BUSINESS_SLUG}, ${COLUMNS.LOGO_URL}, ${COLUMNS.CITY}, ${COLUMNS.STATE}, ${COLUMNS.PINCODE}, ${COLUMNS.ADDRESS_LINE}, ${COLUMNS.LOCALITY}`\n          )\n          .in(COLUMNS.ID, userIds),\n      ]);\n\n      // Create maps for easy lookup\n      const customerProfilesMap = new Map(\n        customerProfiles.data?.map((profile) => [profile.id, profile]) || []\n      );\n      const businessProfilesMap = new Map(\n        businessProfiles.data?.map((profile) => [profile.id, profile]) || []\n      );\n\n      // Combine likes with their corresponding profiles\n      const processedLikes = likes\n        .map((like) => {\n          const customerProfile = customerProfilesMap.get(like.user_id);\n          const businessProfile = businessProfilesMap.get(like.user_id);\n\n          if (customerProfile) {\n            return {\n              id: like.id,\n              user_id: like.user_id,\n              customer_profiles: customerProfile,\n              profile_type: \"customer\" as const,\n            };\n          } else if (businessProfile) {\n            return {\n              id: like.id,\n              user_id: like.user_id,\n              business_profiles: businessProfile,\n              profile_type: \"business\" as const,\n            };\n          }\n          return null;\n        })\n        .filter((item) => item !== null) as BusinessLikeReceived[];\n\n      const hasMore = totalCount > from + limit;\n\n      return {\n        items: processedLikes,\n        totalCount,\n        hasMore,\n        currentPage: page,\n      };\n    } catch (error) {\n      throw error;\n    }\n  },\n};\n\n/**\n * Reviews Service\n */\nexport const reviewsService = {\n  /**\n   * Fetch user reviews with pagination and sorting\n   */\n  async fetchReviews(\n    userId: string,\n    page: number = 1,\n    limit: number = 20,\n    sortBy: \"newest\" | \"oldest\" | \"rating_high\" | \"rating_low\" = \"newest\",\n    searchTerm: string = \"\"\n  ): Promise<ReviewsResult> {\n    try {\n      const supabase = await createClient();\n\n      // Get total count first with separate query to avoid count issues with joins\n      let countQuery = supabase\n        .from(TABLES.RATINGS_REVIEWS)\n        .select(\n          `\n          ${COLUMNS.ID},\n          ${TABLES.BUSINESS_PROFILES}!inner (\n            ${COLUMNS.ID},\n            ${COLUMNS.BUSINESS_NAME}\n          )\n        `,\n          { count: \"exact\", head: true }\n        )\n        .eq(COLUMNS.USER_ID, userId);\n\n      // Apply search filter to count query if provided\n      if (searchTerm && searchTerm.trim()) {\n        countQuery = countQuery.ilike(\n          `${TABLES.BUSINESS_PROFILES}.${COLUMNS.BUSINESS_NAME}`,\n          `%${searchTerm.trim()}%`\n        );\n      }\n\n      const { count: totalCount, error: countError } = await countQuery;\n\n      if (countError) {\n        throw new Error(`Failed to get reviews count: ${countError.message}`);\n      }\n\n      // If no reviews, return empty result\n      if (!totalCount || totalCount === 0) {\n        return {\n          items: [],\n          totalCount: 0,\n          hasMore: false,\n          currentPage: page,\n        };\n      }\n\n      // Build the main query for fetching data\n      let query = supabase\n        .from(TABLES.RATINGS_REVIEWS)\n        .select(\n          `\n          ${COLUMNS.ID},\n          ${COLUMNS.RATING},\n          ${COLUMNS.REVIEW_TEXT},\n          ${COLUMNS.CREATED_AT},\n          ${COLUMNS.UPDATED_AT},\n          ${COLUMNS.BUSINESS_PROFILE_ID},\n          ${COLUMNS.USER_ID},\n          ${TABLES.BUSINESS_PROFILES}!inner (\n            ${COLUMNS.ID},\n            ${COLUMNS.BUSINESS_NAME},\n            ${COLUMNS.BUSINESS_SLUG},\n            ${COLUMNS.LOGO_URL}\n          )\n        `\n        )\n        .eq(COLUMNS.USER_ID, userId);\n\n      // Apply search filter if provided\n      if (searchTerm && searchTerm.trim()) {\n        query = query.ilike(\n          `${TABLES.BUSINESS_PROFILES}.${COLUMNS.BUSINESS_NAME}`,\n          `%${searchTerm.trim()}%`\n        );\n      }\n\n      // Apply sorting\n      switch (sortBy) {\n        case \"oldest\":\n          query = query.order(COLUMNS.CREATED_AT, { ascending: true });\n          break;\n        case \"rating_high\":\n          query = query.order(COLUMNS.RATING, { ascending: false });\n          break;\n        case \"rating_low\":\n          query = query.order(COLUMNS.RATING, { ascending: true });\n          break;\n        case \"newest\":\n        default:\n          query = query.order(COLUMNS.CREATED_AT, { ascending: false });\n          break;\n      }\n\n      // Apply pagination\n      const offset = (page - 1) * limit;\n      query = query.range(offset, offset + limit - 1);\n\n      const { data: reviews, error } = await query;\n\n      if (error) {\n        throw new Error(`Failed to fetch reviews: ${error.message}`);\n      }\n\n      // Transform the data (business profiles are already joined)\n      const transformedReviews: ReviewData[] = (reviews || []).map((review: {\n        id: string;\n        rating: number;\n        review_text: string | null;\n        created_at: string;\n        updated_at: string;\n        business_profile_id: string;\n        user_id: string;\n        business_profiles: ReviewBusinessProfile | ReviewBusinessProfile[];\n      }) => ({\n        id: review.id,\n        rating: review.rating,\n        review_text: review.review_text,\n        created_at: review.created_at,\n        updated_at: review.updated_at,\n        business_profile_id: review.business_profile_id,\n        user_id: review.user_id,\n        business_profiles: Array.isArray(review.business_profiles)\n          ? review.business_profiles[0]\n          : review.business_profiles,\n      }));\n\n      const hasMore = totalCount > offset + limit;\n\n      return {\n        items: transformedReviews,\n        totalCount,\n        hasMore,\n        currentPage: page,\n      };\n    } catch (error) {\n      console.error(\"Error in fetchReviews:\", error);\n      throw error;\n    }\n  },\n\n  /**\n   * Delete a review\n   */\n  async deleteReview(reviewId: string): Promise<void> {\n    try {\n      const supabase = await createClient();\n      const { error } = await supabase\n        .from(TABLES.RATINGS_REVIEWS)\n        .delete()\n        .eq(COLUMNS.ID, reviewId);\n\n      if (error) {\n        throw new Error(`Failed to delete review: ${error.message}`);\n      }\n    } catch (error) {\n      console.error(\"Error in deleteReview:\", error);\n      throw error;\n    }\n  },\n\n  /**\n   * Update a review\n   */\n  async updateReview(\n    reviewId: string,\n    rating: number,\n    reviewText: string\n  ): Promise<void> {\n    try {\n      const supabase = await createClient();\n      const { error } = await supabase\n        .from(TABLES.RATINGS_REVIEWS)\n        .update({\n          [COLUMNS.RATING]: rating,\n          [COLUMNS.REVIEW_TEXT]: reviewText || null,\n          [COLUMNS.UPDATED_AT]: new Date().toISOString(),\n        })\n        .eq(COLUMNS.ID, reviewId);\n\n      if (error) {\n        throw new Error(`Failed to update review: ${error.message}`);\n      }\n    } catch (error) {\n      console.error(\"Error in updateReview:\", error);\n      throw error;\n    }\n  },\n\n  /**\n   * Fetch reviews received by a business (customers/businesses who reviewed this business)\n   */\n  async fetchBusinessReviewsReceived(\n    businessId: string,\n    page: number = 1,\n    limit: number = 10,\n    sortBy: \"newest\" | \"oldest\" | \"rating_high\" | \"rating_low\" = \"newest\"\n  ): Promise<{\n    items: {\n      id: string;\n      rating: number;\n      review_text: string | null;\n      created_at: string;\n      updated_at: string;\n      business_profile_id: string;\n      user_id: string;\n      customer_profiles?: {\n        id: string;\n        name: string | null;\n        email: string | null;\n        avatar_url: string | null;\n      } | null;\n      business_profiles?: {\n        id: string;\n        business_name: string | null;\n        business_slug: string | null;\n        logo_url: string | null;\n        city: string | null;\n        state: string | null;\n        pincode: string | null;\n        address_line: string | null;\n      } | null;\n      profile_type: \"customer\" | \"business\";\n    }[];\n    totalCount: number;\n    hasMore: boolean;\n    currentPage: number;\n  }> {\n    try {\n      const supabase = await createClient();\n\n      // Get total count first\n      const { count: totalCount, error: countError } = await supabase\n        .from(TABLES.RATINGS_REVIEWS)\n        .select(COLUMNS.ID, { count: \"exact\", head: true })\n        .eq(COLUMNS.BUSINESS_PROFILE_ID, businessId);\n\n      if (countError) {\n        throw new Error(\"Failed to get total count\");\n      }\n\n      if (!totalCount || totalCount === 0) {\n        return {\n          items: [],\n          totalCount: 0,\n          hasMore: false,\n          currentPage: page,\n        };\n      }\n\n      // Get reviews with pagination and sorting\n      const from = (page - 1) * limit;\n      let query = supabase\n        .from(TABLES.RATINGS_REVIEWS)\n        .select(`${COLUMNS.ID}, ${COLUMNS.RATING}, ${COLUMNS.REVIEW_TEXT}, ${COLUMNS.CREATED_AT}, ${COLUMNS.UPDATED_AT}, ${COLUMNS.BUSINESS_PROFILE_ID}, ${COLUMNS.USER_ID}`)\n        .eq(COLUMNS.BUSINESS_PROFILE_ID, businessId);\n\n      // Apply sorting\n      switch (sortBy) {\n        case \"oldest\":\n          query = query.order(COLUMNS.CREATED_AT, { ascending: true });\n          break;\n        case \"rating_high\":\n          query = query.order(COLUMNS.RATING, { ascending: false });\n          break;\n        case \"rating_low\":\n          query = query.order(COLUMNS.RATING, { ascending: true });\n          break;\n        case \"newest\":\n        default:\n          query = query.order(COLUMNS.CREATED_AT, { ascending: false });\n          break;\n      }\n\n      query = query.range(from, from + limit - 1);\n\n      const { data: reviews, error: reviewsError } = await query;\n\n      if (reviewsError) {\n        throw new Error(\"Failed to fetch reviews\");\n      }\n\n      if (!reviews || reviews.length === 0) {\n        return {\n          items: [],\n          totalCount,\n          hasMore: false,\n          currentPage: page,\n        };\n      }\n\n      // Get user IDs for the paginated results only\n      const userIds = reviews.map((review) => review.user_id);\n\n      // Fetch customer and business profiles for paginated results only\n      const [customerProfiles, businessProfiles] = await Promise.all([\n        supabase\n          .from(TABLES.CUSTOMER_PROFILES)\n          .select(`${COLUMNS.ID}, ${COLUMNS.NAME}, ${COLUMNS.EMAIL}, ${COLUMNS.AVATAR_URL}`)\n          .in(COLUMNS.ID, userIds),\n        supabase\n          .from(TABLES.BUSINESS_PROFILES)\n          .select(\n            `${COLUMNS.ID}, ${COLUMNS.BUSINESS_NAME}, ${COLUMNS.BUSINESS_SLUG}, ${COLUMNS.LOGO_URL}, ${COLUMNS.CITY}, ${COLUMNS.STATE}, ${COLUMNS.PINCODE}, ${COLUMNS.ADDRESS_LINE}`\n          )\n          .in(COLUMNS.ID, userIds),\n      ]);\n\n      // Create maps for easy lookup\n      const customerProfilesMap = new Map(\n        customerProfiles.data?.map((profile) => [profile.id, profile]) || []\n      );\n      const businessProfilesMap = new Map(\n        businessProfiles.data?.map((profile) => [profile.id, profile]) || []\n      );\n\n      // Combine reviews with their corresponding profiles\n      const processedReviews = reviews\n        .map((review) => {\n          const customerProfile = customerProfilesMap.get(review.user_id);\n          const businessProfile = businessProfilesMap.get(review.user_id);\n\n          if (customerProfile) {\n            return {\n              id: review.id,\n              rating: review.rating,\n              review_text: review.review_text,\n              created_at: review.created_at,\n              updated_at: review.updated_at,\n              business_profile_id: review.business_profile_id,\n              user_id: review.user_id,\n              customer_profiles: customerProfile,\n              profile_type: \"customer\" as const,\n            };\n          } else if (businessProfile) {\n            return {\n              id: review.id,\n              rating: review.rating,\n              review_text: review.review_text,\n              created_at: review.created_at,\n              updated_at: review.updated_at,\n              business_profile_id: review.business_profile_id,\n              user_id: review.user_id,\n              business_profiles: businessProfile,\n              profile_type: \"business\" as const,\n            };\n          }\n          return null;\n        })\n        .filter((item) => item !== null);\n\n      const hasMore = totalCount > from + limit;\n\n      return {\n        items: processedReviews,\n        totalCount,\n        hasMore,\n        currentPage: page,\n      };\n    } catch (error) {\n      console.error(\"Error in fetchBusinessReviewsReceived:\", error);\n      throw error;\n    }\n  },\n};\n\n/**\n * Get customer activity metrics\n */\nexport async function getActivityMetrics(\n  userId: string\n): Promise<ActivityMetrics | null> {\n  try {\n    const supabase = await createClient();\n\n    // Fetch fresh metrics from database\n    const [likesResult, reviewsResult, subscriptionsResult] = await Promise.all(\n      [\n        supabase\n          .from(TABLES.LIKES)\n          .select(COLUMNS.ID, { count: \"exact\" })\n          .eq(COLUMNS.USER_ID, userId),\n        supabase\n          .from(TABLES.RATINGS_REVIEWS)\n          .select(COLUMNS.ID, { count: \"exact\" })\n          .eq(COLUMNS.USER_ID, userId),\n        supabase\n          .from(TABLES.SUBSCRIPTIONS)\n          .select(COLUMNS.ID, { count: \"exact\" })\n          .eq(COLUMNS.USER_ID, userId),\n      ]\n    );\n\n    const metrics: ActivityMetrics = {\n      likesCount: likesResult.count || 0,\n      reviewCount: reviewsResult.count || 0,\n      subscriptionCount: subscriptionsResult.count || 0,\n      lastUpdated: new Date().toISOString(),\n    };\n\n    return metrics;\n  } catch (error) {\n    console.error(\"Error fetching activity metrics:\", error);\n    return null;\n  }\n}\n\n// Export a service object for compatibility with existing imports\nexport const socialService = {\n  likesService,\n  reviewsService,\n  subscriptionsService,\n  fetchActivityMetrics: getActivityMetrics,\n};\n"], "names": [], "mappings": "AAAA;;;;CAIC;;;;;;;AAED;AAEA;;;AAyHO,MAAM,uBAAuB;IAClC;;GAEC,GACD,MAAM,oBACJ,MAAc,EACd,OAAe,CAAC,EAChB,QAAgB,EAAE,EAClB,aAAqB,EAAE;QAEvB,IAAI;YACF,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD;YAElC,6EAA6E;YAC7E,IAAI,aAAa,SACd,IAAI,CAAC,4HAAA,CAAA,SAAM,CAAC,aAAa,EACzB,MAAM,CACL,CAAC;UACD,EAAE,4HAAA,CAAA,UAAO,CAAC,EAAE,CAAC;UACb,EAAE,4HAAA,CAAA,SAAM,CAAC,iBAAiB,CAAC;YACzB,EAAE,4HAAA,CAAA,UAAO,CAAC,EAAE,CAAC;YACb,EAAE,4HAAA,CAAA,UAAO,CAAC,aAAa,CAAC;;QAE5B,CAAC,EACC;gBAAE,OAAO;gBAAS,MAAM;YAAK,GAE9B,EAAE,CAAC,4HAAA,CAAA,UAAO,CAAC,OAAO,EAAE;YAEvB,iDAAiD;YACjD,IAAI,cAAc,WAAW,IAAI,IAAI;gBACnC,aAAa,WAAW,KAAK,CAC3B,GAAG,4HAAA,CAAA,SAAM,CAAC,iBAAiB,CAAC,CAAC,EAAE,4HAAA,CAAA,UAAO,CAAC,aAAa,EAAE,EACtD,CAAC,CAAC,EAAE,WAAW,IAAI,GAAG,CAAC,CAAC;YAE5B;YAEA,MAAM,EAAE,OAAO,UAAU,EAAE,OAAO,UAAU,EAAE,GAAG,MAAM;YAEvD,IAAI,YAAY;gBACd,MAAM,IAAI,MAAM,CAAC,mCAAmC,EAAE,WAAW,OAAO,EAAE;YAC5E;YAEA,2CAA2C;YAC3C,IAAI,CAAC,cAAc,eAAe,GAAG;gBACnC,OAAO;oBACL,OAAO,EAAE;oBACT,YAAY;oBACZ,SAAS;oBACT,aAAa;gBACf;YACF;YAEA,yCAAyC;YACzC,IAAI,QAAQ,SACT,IAAI,CAAC,4HAAA,CAAA,SAAM,CAAC,aAAa,EACzB,MAAM,CACL,CAAC;UACD,EAAE,4HAAA,CAAA,UAAO,CAAC,EAAE,CAAC;UACb,EAAE,4HAAA,CAAA,UAAO,CAAC,mBAAmB,CAAC;UAC9B,EAAE,4HAAA,CAAA,SAAM,CAAC,iBAAiB,CAAC;QAC7B,CAAC,EAEA,EAAE,CAAC,4HAAA,CAAA,UAAO,CAAC,OAAO,EAAE;YAEvB,kCAAkC;YAClC,IAAI,cAAc,WAAW,IAAI,IAAI;gBACnC,QAAQ,MAAM,KAAK,CACjB,GAAG,4HAAA,CAAA,SAAM,CAAC,iBAAiB,CAAC,CAAC,EAAE,4HAAA,CAAA,UAAO,CAAC,aAAa,EAAE,EACtD,CAAC,CAAC,EAAE,WAAW,IAAI,GAAG,CAAC,CAAC;YAE5B;YAEA,mBAAmB;YACnB,MAAM,SAAS,CAAC,OAAO,CAAC,IAAI;YAC5B,QAAQ,GAAG,CAAC,CAAC,sEAAsE,EAAE,OAAO,QAAQ,EAAE,OAAO;YAC7G,QAAQ,MAAM,KAAK,CAAC,QAAQ,SAAS,QAAQ;YAE7C,MAAM,EAAE,MAAM,yBAAyB,EAAE,KAAK,EAAE,GAAG,MAAM;YACzD,QAAQ,GAAG,CAAC,CAAC,yDAAyD,EAAE,2BAA2B,OAAO,MAAM,CAAC;YAEjH,IAAI,OAAO;gBACT,MAAM,IAAI,MAAM,CAAC,+BAA+B,EAAE,MAAM,OAAO,EAAE;YACnE;YAEA,qBAAqB;YACrB,MAAM,2BAAsD,CAC1D,6BAA6B,EAAE,AACjC,EAAE,GAAG,CACH,CAAC,MAGK,CAAC;oBACL,IAAI,IAAI,EAAE;oBACV,mBAAmB,MAAM,OAAO,CAAC,IAAI,iBAAiB,IAClD,IAAI,iBAAiB,CAAC,EAAE,GACxB,IAAI,iBAAiB;gBAC3B,CAAC;YAGH,MAAM,UAAU,aAAa,SAAS;YAEtC,OAAO;gBACL,OAAO;gBACP;gBACA;gBACA,aAAa;YACf;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,MAAM;QACR;IACF;IAEA;;GAEC,GACD,MAAM,aAAY,cAAsB;QACtC,IAAI;YACF,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD;YAClC,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SACrB,IAAI,CAAC,4HAAA,CAAA,SAAM,CAAC,aAAa,EACzB,MAAM,GACN,EAAE,CAAC,4HAAA,CAAA,UAAO,CAAC,EAAE,EAAE;YAElB,IAAI,OAAO;gBACT,MAAM,IAAI,MAAM,CAAC,uBAAuB,EAAE,MAAM,OAAO,EAAE;YAC3D;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,MAAM;QACR;IACF;IAEA;;GAEC,GACD,MAAM,wBACJ,UAAkB,EAClB,OAAe,CAAC,EAChB,QAAgB,EAAE;QAElB,IAAI;YACF,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD;YAElC,kCAAkC;YAClC,MAAM,SAAS,CAAC,OAAO,CAAC,IAAI;YAE5B,wBAAwB;YACxB,MAAM,EAAE,OAAO,UAAU,EAAE,OAAO,UAAU,EAAE,GAAG,MAAM,SACpD,IAAI,CAAC,4HAAA,CAAA,SAAM,CAAC,aAAa,EACzB,MAAM,CAAC,KAAK;gBAAE,OAAO;gBAAS,MAAM;YAAK,GACzC,EAAE,CAAC,4HAAA,CAAA,UAAO,CAAC,mBAAmB,EAAE;YAEnC,IAAI,YAAY;gBACd,MAAM,IAAI,MAAM;YAClB;YAEA,IAAI,CAAC,cAAc,eAAe,GAAG;gBACnC,OAAO;oBACL,OAAO,EAAE;oBACT,YAAY;oBACZ,SAAS;oBACT,aAAa;gBACf;YACF;YAEA,0DAA0D;YAC1D,MAAM,EAAE,MAAM,aAAa,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,SACrD,IAAI,CAAC,4HAAA,CAAA,SAAM,CAAC,aAAa,EACzB,MAAM,CAAC,GAAG,4HAAA,CAAA,UAAO,CAAC,EAAE,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,OAAO,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,UAAU,EAAE,EACjE,EAAE,CAAC,4HAAA,CAAA,UAAO,CAAC,mBAAmB,EAAE,YAChC,KAAK,CAAC,4HAAA,CAAA,UAAO,CAAC,UAAU,EAAE;gBAAE,WAAW;YAAM,GAC7C,KAAK,CAAC,QAAQ,SAAS,QAAQ;YAElC,IAAI,WAAW;gBACb,MAAM,IAAI,MAAM;YAClB;YAEA,IAAI,CAAC,iBAAiB,cAAc,MAAM,KAAK,GAAG;gBAChD,OAAO;oBACL,OAAO,EAAE;oBACT,YAAY,cAAc;oBAC1B,SAAS;oBACT,aAAa;gBACf;YACF;YAEA,qDAAqD;YACrD,MAAM,UAAU,cAAc,GAAG,CAAC,CAAC,MAAQ,IAAI,OAAO;YAEtD,iEAAiE;YACjE,MAAM,CAAC,kBAAkB,iBAAiB,GAAG,MAAM,QAAQ,GAAG,CAAC;gBAC7D,SACG,IAAI,CAAC,4HAAA,CAAA,SAAM,CAAC,iBAAiB,EAC7B,MAAM,CAAC,GAAG,4HAAA,CAAA,UAAO,CAAC,EAAE,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,IAAI,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,UAAU,EAAE,EAC9D,EAAE,CAAC,4HAAA,CAAA,UAAO,CAAC,EAAE,EAAE;gBAClB,SACG,IAAI,CAAC,4HAAA,CAAA,SAAM,CAAC,iBAAiB,EAC7B,MAAM,CACL,GAAG,4HAAA,CAAA,UAAO,CAAC,EAAE,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,aAAa,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,aAAa,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,QAAQ,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,IAAI,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,KAAK,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,OAAO,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,YAAY,EAAE,EAEzK,EAAE,CAAC,4HAAA,CAAA,UAAO,CAAC,EAAE,EAAE;aACnB;YAED,IAAI,iBAAiB,KAAK,EAAE;gBAC1B,MAAM,IAAI,MAAM;YAClB;YAEA,IAAI,iBAAiB,KAAK,EAAE;gBAC1B,MAAM,IAAI,MAAM;YAClB;YAEA,2CAA2C;YAC3C,MAAM,cAAc,IAAI,IACtB,iBAAiB,IAAI,EAAE,IAAI,CAAC,IAAM;oBAAC,EAAE,EAAE;oBAAE;iBAAE,KAAK,EAAE;YAEpD,MAAM,cAAc,IAAI,IACtB,iBAAiB,IAAI,EAAE,IAAI,CAAC,IAAM;oBAAC,EAAE,EAAE;oBAAE;iBAAE,KAAK,EAAE;YAGpD,kDAAkD;YAClD,MAAM,eAAsC,cACzC,GAAG,CAAC,CAAC;gBACJ,MAAM,kBAAkB,YAAY,GAAG,CAAC,IAAI,OAAO;gBACnD,MAAM,kBAAkB,YAAY,GAAG,CAAC,IAAI,OAAO;gBAEnD,IAAI,iBAAiB;oBACnB,OAAO;wBACL,IAAI,IAAI,EAAE;wBACV,SAAS;4BACP,IAAI,gBAAgB,EAAE;4BACtB,MAAM,gBAAgB,IAAI;4BAC1B,MAAM;4BACN,YAAY,gBAAgB,UAAU;4BACtC,MAAM;4BACN,OAAO;4BACP,SAAS;4BACT,cAAc;4BACd,MAAM;wBACR;oBACF;gBACF,OAAO,IAAI,iBAAiB;oBAC1B,OAAO;wBACL,IAAI,IAAI,EAAE;wBACV,SAAS;4BACP,IAAI,gBAAgB,EAAE;4BACtB,MAAM,gBAAgB,aAAa;4BACnC,MAAM,gBAAgB,aAAa;4BACnC,UAAU,gBAAgB,QAAQ;4BAClC,MAAM,gBAAgB,IAAI;4BAC1B,OAAO,gBAAgB,KAAK;4BAC5B,SAAS,gBAAgB,OAAO;4BAChC,cAAc,gBAAgB,YAAY;4BAC1C,MAAM;wBACR;oBACF;gBACF;gBACA,OAAO;YACT,GACC,MAAM,CAAC,CAAC,MAAQ,QAAQ;YAE3B,uDAAuD;YACvD,MAAM,UAAU,aAAa,SAAS;YAEtC,OAAO;gBACL,OAAO;gBACP,YAAY,cAAc;gBAC1B;gBACA,aAAa;YACf;QACF,EAAE,OAAO,OAAO;YACd,MAAM;QACR;IACF;AACF;AAKO,MAAM,eAAe;IAC1B;;GAEC,GACD,MAAM,YACJ,MAAc,EACd,OAAe,CAAC,EAChB,QAAgB,EAAE,EAClB,aAAqB,EAAE;QAEvB,IAAI;YACF,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD;YAElC,8CAA8C;YAC9C,IAAI,QAAQ,SACT,IAAI,CAAC,4HAAA,CAAA,SAAM,CAAC,KAAK,EACjB,MAAM,CACL,CAAC;UACD,EAAE,4HAAA,CAAA,UAAO,CAAC,EAAE,CAAC;UACb,EAAE,4HAAA,CAAA,SAAM,CAAC,iBAAiB,CAAC;QAC7B,CAAC,EAEA,EAAE,CAAC,4HAAA,CAAA,UAAO,CAAC,OAAO,EAAE;YAEvB,kCAAkC;YAClC,IAAI,cAAc,WAAW,IAAI,IAAI;gBACnC,QAAQ,MAAM,KAAK,CACjB,GAAG,4HAAA,CAAA,SAAM,CAAC,iBAAiB,CAAC,CAAC,EAAE,4HAAA,CAAA,UAAO,CAAC,aAAa,EAAE,EACtD,CAAC,CAAC,EAAE,WAAW,IAAI,GAAG,CAAC,CAAC;YAE5B;YAEA,iCAAiC;YACjC,IAAI,aAAa,SACd,IAAI,CAAC,4HAAA,CAAA,SAAM,CAAC,KAAK,EACjB,MAAM,CACL,CAAC;UACD,EAAE,4HAAA,CAAA,UAAO,CAAC,EAAE,CAAC;UACb,EAAE,4HAAA,CAAA,SAAM,CAAC,iBAAiB,CAAC;YACzB,EAAE,4HAAA,CAAA,UAAO,CAAC,EAAE,CAAC;YACb,EAAE,4HAAA,CAAA,UAAO,CAAC,aAAa,CAAC;;QAE5B,CAAC,EACC;gBAAE,OAAO;gBAAS,MAAM;YAAK,GAE9B,EAAE,CAAC,4HAAA,CAAA,UAAO,CAAC,OAAO,EAAE;YAEvB,IAAI,cAAc,WAAW,IAAI,IAAI;gBACnC,aAAa,WAAW,KAAK,CAC3B,GAAG,4HAAA,CAAA,SAAM,CAAC,iBAAiB,CAAC,CAAC,EAAE,4HAAA,CAAA,UAAO,CAAC,aAAa,EAAE,EACtD,CAAC,CAAC,EAAE,WAAW,IAAI,GAAG,CAAC,CAAC;YAE5B;YAEA,MAAM,EAAE,OAAO,UAAU,EAAE,OAAO,UAAU,EAAE,GAAG,MAAM;YAEvD,IAAI,YAAY;gBACd,MAAM,IAAI,MAAM,CAAC,2BAA2B,EAAE,WAAW,OAAO,EAAE;YACpE;YAEA,mCAAmC;YACnC,IAAI,CAAC,cAAc,eAAe,GAAG;gBACnC,OAAO;oBACL,OAAO,EAAE;oBACT,YAAY;oBACZ,SAAS;oBACT,aAAa;gBACf;YACF;YAEA,qCAAqC;YACrC,MAAM,SAAS,CAAC,OAAO,CAAC,IAAI;YAC5B,QAAQ,GAAG,CAAC,CAAC,sDAAsD,EAAE,OAAO,QAAQ,EAAE,OAAO;YAC7F,QAAQ,MAAM,KAAK,CAAC,QAAQ,SAAS,QAAQ;YAE7C,MAAM,EAAE,MAAM,iBAAiB,EAAE,KAAK,EAAE,GAAG,MAAM;YACjD,QAAQ,GAAG,CAAC,CAAC,yCAAyC,EAAE,mBAAmB,OAAO,MAAM,CAAC;YAEzF,IAAI,OAAO;gBACT,MAAM,IAAI,MAAM,CAAC,uBAAuB,EAAE,MAAM,OAAO,EAAE;YAC3D;YAEA,qBAAqB;YACrB,MAAM,mBAAsC,CAAC,qBAAqB,EAAE,EAAE,GAAG,CACvE,CAAC,OAGK,CAAC;oBACL,IAAI,KAAK,EAAE;oBACX,mBAAmB,MAAM,OAAO,CAAC,KAAK,iBAAiB,IACnD,KAAK,iBAAiB,CAAC,EAAE,GACzB,KAAK,iBAAiB;gBAC5B,CAAC;YAGH,MAAM,UAAU,aAAa,SAAS;YAEtC,OAAO;gBACL,OAAO;gBACP;gBACA;gBACA,aAAa;YACf;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;YACtC,MAAM;QACR;IACF;IAEA;;GAEC,GACD,MAAM,QAAO,MAAc;QACzB,IAAI;YACF,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD;YAClC,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,4HAAA,CAAA,SAAM,CAAC,KAAK,EAAE,MAAM,GAAG,EAAE,CAAC,4HAAA,CAAA,UAAO,CAAC,EAAE,EAAE;YAE5E,IAAI,OAAO;gBACT,MAAM,IAAI,MAAM,CAAC,kBAAkB,EAAE,MAAM,OAAO,EAAE;YACtD;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,oBAAoB;YAClC,MAAM;QACR;IACF;IAEA;;GAEC,GACD,MAAM,4BACJ,UAAkB,EAClB,OAAe,CAAC,EAChB,QAAgB,EAAE;QAElB,IAAI;YACF,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD;YAElC,wBAAwB;YACxB,MAAM,EAAE,OAAO,UAAU,EAAE,OAAO,UAAU,EAAE,GAAG,MAAM,SACpD,IAAI,CAAC,4HAAA,CAAA,SAAM,CAAC,KAAK,EACjB,MAAM,CAAC,4HAAA,CAAA,UAAO,CAAC,EAAE,EAAE;gBAAE,OAAO;gBAAS,MAAM;YAAK,GAChD,EAAE,CAAC,4HAAA,CAAA,UAAO,CAAC,mBAAmB,EAAE;YAEnC,IAAI,YAAY;gBACd,MAAM,IAAI,MAAM;YAClB;YAEA,IAAI,CAAC,cAAc,eAAe,GAAG;gBACnC,OAAO;oBACL,OAAO,EAAE;oBACT,YAAY;oBACZ,SAAS;oBACT,aAAa;gBACf;YACF;YAEA,wDAAwD;YACxD,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI;YAC1B,QAAQ,GAAG,CAAC,CAAC,oEAAoE,EAAE,KAAK,QAAQ,EAAE,OAAO;YACzG,MAAM,EAAE,MAAM,KAAK,EAAE,OAAO,UAAU,EAAE,GAAG,MAAM,SAC9C,IAAI,CAAC,4HAAA,CAAA,SAAM,CAAC,KAAK,EACjB,MAAM,CAAC,GAAG,4HAAA,CAAA,UAAO,CAAC,EAAE,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,OAAO,EAAE,EAC1C,EAAE,CAAC,4HAAA,CAAA,UAAO,CAAC,mBAAmB,EAAE,YAChC,KAAK,CAAC,MAAM,OAAO,QAAQ;YAC9B,QAAQ,GAAG,CAAC,CAAC,yDAAyD,EAAE,OAAO,OAAO,MAAM,CAAC;YAE7F,IAAI,YAAY;gBACd,MAAM,IAAI,MAAM;YAClB;YAEA,IAAI,CAAC,SAAS,MAAM,MAAM,KAAK,GAAG;gBAChC,OAAO;oBACL,OAAO,EAAE;oBACT;oBACA,SAAS;oBACT,aAAa;gBACf;YACF;YAEA,8CAA8C;YAC9C,MAAM,UAAU,MAAM,GAAG,CAAC,CAAC,OAAS,KAAK,OAAO;YAEhD,kEAAkE;YAClE,MAAM,CAAC,kBAAkB,iBAAiB,GAAG,MAAM,QAAQ,GAAG,CAAC;gBAC7D,SACG,IAAI,CAAC,4HAAA,CAAA,SAAM,CAAC,iBAAiB,EAC7B,MAAM,CAAC,GAAG,4HAAA,CAAA,UAAO,CAAC,EAAE,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,IAAI,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,KAAK,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,UAAU,EAAE,EAChF,EAAE,CAAC,4HAAA,CAAA,UAAO,CAAC,EAAE,EAAE;gBAClB,SACG,IAAI,CAAC,4HAAA,CAAA,SAAM,CAAC,iBAAiB,EAC7B,MAAM,CACL,GAAG,4HAAA,CAAA,UAAO,CAAC,EAAE,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,aAAa,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,aAAa,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,QAAQ,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,IAAI,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,KAAK,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,OAAO,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,YAAY,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,QAAQ,EAAE,EAE9L,EAAE,CAAC,4HAAA,CAAA,UAAO,CAAC,EAAE,EAAE;aACnB;YAED,8BAA8B;YAC9B,MAAM,sBAAsB,IAAI,IAC9B,iBAAiB,IAAI,EAAE,IAAI,CAAC,UAAY;oBAAC,QAAQ,EAAE;oBAAE;iBAAQ,KAAK,EAAE;YAEtE,MAAM,sBAAsB,IAAI,IAC9B,iBAAiB,IAAI,EAAE,IAAI,CAAC,UAAY;oBAAC,QAAQ,EAAE;oBAAE;iBAAQ,KAAK,EAAE;YAGtE,kDAAkD;YAClD,MAAM,iBAAiB,MACpB,GAAG,CAAC,CAAC;gBACJ,MAAM,kBAAkB,oBAAoB,GAAG,CAAC,KAAK,OAAO;gBAC5D,MAAM,kBAAkB,oBAAoB,GAAG,CAAC,KAAK,OAAO;gBAE5D,IAAI,iBAAiB;oBACnB,OAAO;wBACL,IAAI,KAAK,EAAE;wBACX,SAAS,KAAK,OAAO;wBACrB,mBAAmB;wBACnB,cAAc;oBAChB;gBACF,OAAO,IAAI,iBAAiB;oBAC1B,OAAO;wBACL,IAAI,KAAK,EAAE;wBACX,SAAS,KAAK,OAAO;wBACrB,mBAAmB;wBACnB,cAAc;oBAChB;gBACF;gBACA,OAAO;YACT,GACC,MAAM,CAAC,CAAC,OAAS,SAAS;YAE7B,MAAM,UAAU,aAAa,OAAO;YAEpC,OAAO;gBACL,OAAO;gBACP;gBACA;gBACA,aAAa;YACf;QACF,EAAE,OAAO,OAAO;YACd,MAAM;QACR;IACF;AACF;AAKO,MAAM,iBAAiB;IAC5B;;GAEC,GACD,MAAM,cACJ,MAAc,EACd,OAAe,CAAC,EAChB,QAAgB,EAAE,EAClB,SAA6D,QAAQ,EACrE,aAAqB,EAAE;QAEvB,IAAI;YACF,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD;YAElC,6EAA6E;YAC7E,IAAI,aAAa,SACd,IAAI,CAAC,4HAAA,CAAA,SAAM,CAAC,eAAe,EAC3B,MAAM,CACL,CAAC;UACD,EAAE,4HAAA,CAAA,UAAO,CAAC,EAAE,CAAC;UACb,EAAE,4HAAA,CAAA,SAAM,CAAC,iBAAiB,CAAC;YACzB,EAAE,4HAAA,CAAA,UAAO,CAAC,EAAE,CAAC;YACb,EAAE,4HAAA,CAAA,UAAO,CAAC,aAAa,CAAC;;QAE5B,CAAC,EACC;gBAAE,OAAO;gBAAS,MAAM;YAAK,GAE9B,EAAE,CAAC,4HAAA,CAAA,UAAO,CAAC,OAAO,EAAE;YAEvB,iDAAiD;YACjD,IAAI,cAAc,WAAW,IAAI,IAAI;gBACnC,aAAa,WAAW,KAAK,CAC3B,GAAG,4HAAA,CAAA,SAAM,CAAC,iBAAiB,CAAC,CAAC,EAAE,4HAAA,CAAA,UAAO,CAAC,aAAa,EAAE,EACtD,CAAC,CAAC,EAAE,WAAW,IAAI,GAAG,CAAC,CAAC;YAE5B;YAEA,MAAM,EAAE,OAAO,UAAU,EAAE,OAAO,UAAU,EAAE,GAAG,MAAM;YAEvD,IAAI,YAAY;gBACd,MAAM,IAAI,MAAM,CAAC,6BAA6B,EAAE,WAAW,OAAO,EAAE;YACtE;YAEA,qCAAqC;YACrC,IAAI,CAAC,cAAc,eAAe,GAAG;gBACnC,OAAO;oBACL,OAAO,EAAE;oBACT,YAAY;oBACZ,SAAS;oBACT,aAAa;gBACf;YACF;YAEA,yCAAyC;YACzC,IAAI,QAAQ,SACT,IAAI,CAAC,4HAAA,CAAA,SAAM,CAAC,eAAe,EAC3B,MAAM,CACL,CAAC;UACD,EAAE,4HAAA,CAAA,UAAO,CAAC,EAAE,CAAC;UACb,EAAE,4HAAA,CAAA,UAAO,CAAC,MAAM,CAAC;UACjB,EAAE,4HAAA,CAAA,UAAO,CAAC,WAAW,CAAC;UACtB,EAAE,4HAAA,CAAA,UAAO,CAAC,UAAU,CAAC;UACrB,EAAE,4HAAA,CAAA,UAAO,CAAC,UAAU,CAAC;UACrB,EAAE,4HAAA,CAAA,UAAO,CAAC,mBAAmB,CAAC;UAC9B,EAAE,4HAAA,CAAA,UAAO,CAAC,OAAO,CAAC;UAClB,EAAE,4HAAA,CAAA,SAAM,CAAC,iBAAiB,CAAC;YACzB,EAAE,4HAAA,CAAA,UAAO,CAAC,EAAE,CAAC;YACb,EAAE,4HAAA,CAAA,UAAO,CAAC,aAAa,CAAC;YACxB,EAAE,4HAAA,CAAA,UAAO,CAAC,aAAa,CAAC;YACxB,EAAE,4HAAA,CAAA,UAAO,CAAC,QAAQ,CAAC;;QAEvB,CAAC,EAEA,EAAE,CAAC,4HAAA,CAAA,UAAO,CAAC,OAAO,EAAE;YAEvB,kCAAkC;YAClC,IAAI,cAAc,WAAW,IAAI,IAAI;gBACnC,QAAQ,MAAM,KAAK,CACjB,GAAG,4HAAA,CAAA,SAAM,CAAC,iBAAiB,CAAC,CAAC,EAAE,4HAAA,CAAA,UAAO,CAAC,aAAa,EAAE,EACtD,CAAC,CAAC,EAAE,WAAW,IAAI,GAAG,CAAC,CAAC;YAE5B;YAEA,gBAAgB;YAChB,OAAQ;gBACN,KAAK;oBACH,QAAQ,MAAM,KAAK,CAAC,4HAAA,CAAA,UAAO,CAAC,UAAU,EAAE;wBAAE,WAAW;oBAAK;oBAC1D;gBACF,KAAK;oBACH,QAAQ,MAAM,KAAK,CAAC,4HAAA,CAAA,UAAO,CAAC,MAAM,EAAE;wBAAE,WAAW;oBAAM;oBACvD;gBACF,KAAK;oBACH,QAAQ,MAAM,KAAK,CAAC,4HAAA,CAAA,UAAO,CAAC,MAAM,EAAE;wBAAE,WAAW;oBAAK;oBACtD;gBACF,KAAK;gBACL;oBACE,QAAQ,MAAM,KAAK,CAAC,4HAAA,CAAA,UAAO,CAAC,UAAU,EAAE;wBAAE,WAAW;oBAAM;oBAC3D;YACJ;YAEA,mBAAmB;YACnB,MAAM,SAAS,CAAC,OAAO,CAAC,IAAI;YAC5B,QAAQ,MAAM,KAAK,CAAC,QAAQ,SAAS,QAAQ;YAE7C,MAAM,EAAE,MAAM,OAAO,EAAE,KAAK,EAAE,GAAG,MAAM;YAEvC,IAAI,OAAO;gBACT,MAAM,IAAI,MAAM,CAAC,yBAAyB,EAAE,MAAM,OAAO,EAAE;YAC7D;YAEA,4DAA4D;YAC5D,MAAM,qBAAmC,CAAC,WAAW,EAAE,EAAE,GAAG,CAAC,CAAC,SASxD,CAAC;oBACL,IAAI,OAAO,EAAE;oBACb,QAAQ,OAAO,MAAM;oBACrB,aAAa,OAAO,WAAW;oBAC/B,YAAY,OAAO,UAAU;oBAC7B,YAAY,OAAO,UAAU;oBAC7B,qBAAqB,OAAO,mBAAmB;oBAC/C,SAAS,OAAO,OAAO;oBACvB,mBAAmB,MAAM,OAAO,CAAC,OAAO,iBAAiB,IACrD,OAAO,iBAAiB,CAAC,EAAE,GAC3B,OAAO,iBAAiB;gBAC9B,CAAC;YAED,MAAM,UAAU,aAAa,SAAS;YAEtC,OAAO;gBACL,OAAO;gBACP;gBACA;gBACA,aAAa;YACf;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,MAAM;QACR;IACF;IAEA;;GAEC,GACD,MAAM,cAAa,QAAgB;QACjC,IAAI;YACF,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD;YAClC,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SACrB,IAAI,CAAC,4HAAA,CAAA,SAAM,CAAC,eAAe,EAC3B,MAAM,GACN,EAAE,CAAC,4HAAA,CAAA,UAAO,CAAC,EAAE,EAAE;YAElB,IAAI,OAAO;gBACT,MAAM,IAAI,MAAM,CAAC,yBAAyB,EAAE,MAAM,OAAO,EAAE;YAC7D;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,MAAM;QACR;IACF;IAEA;;GAEC,GACD,MAAM,cACJ,QAAgB,EAChB,MAAc,EACd,UAAkB;QAElB,IAAI;YACF,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD;YAClC,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SACrB,IAAI,CAAC,4HAAA,CAAA,SAAM,CAAC,eAAe,EAC3B,MAAM,CAAC;gBACN,CAAC,4HAAA,CAAA,UAAO,CAAC,MAAM,CAAC,EAAE;gBAClB,CAAC,4HAAA,CAAA,UAAO,CAAC,WAAW,CAAC,EAAE,cAAc;gBACrC,CAAC,4HAAA,CAAA,UAAO,CAAC,UAAU,CAAC,EAAE,IAAI,OAAO,WAAW;YAC9C,GACC,EAAE,CAAC,4HAAA,CAAA,UAAO,CAAC,EAAE,EAAE;YAElB,IAAI,OAAO;gBACT,MAAM,IAAI,MAAM,CAAC,yBAAyB,EAAE,MAAM,OAAO,EAAE;YAC7D;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,MAAM;QACR;IACF;IAEA;;GAEC,GACD,MAAM,8BACJ,UAAkB,EAClB,OAAe,CAAC,EAChB,QAAgB,EAAE,EAClB,SAA6D,QAAQ;QAgCrE,IAAI;YACF,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD;YAElC,wBAAwB;YACxB,MAAM,EAAE,OAAO,UAAU,EAAE,OAAO,UAAU,EAAE,GAAG,MAAM,SACpD,IAAI,CAAC,4HAAA,CAAA,SAAM,CAAC,eAAe,EAC3B,MAAM,CAAC,4HAAA,CAAA,UAAO,CAAC,EAAE,EAAE;gBAAE,OAAO;gBAAS,MAAM;YAAK,GAChD,EAAE,CAAC,4HAAA,CAAA,UAAO,CAAC,mBAAmB,EAAE;YAEnC,IAAI,YAAY;gBACd,MAAM,IAAI,MAAM;YAClB;YAEA,IAAI,CAAC,cAAc,eAAe,GAAG;gBACnC,OAAO;oBACL,OAAO,EAAE;oBACT,YAAY;oBACZ,SAAS;oBACT,aAAa;gBACf;YACF;YAEA,0CAA0C;YAC1C,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI;YAC1B,IAAI,QAAQ,SACT,IAAI,CAAC,4HAAA,CAAA,SAAM,CAAC,eAAe,EAC3B,MAAM,CAAC,GAAG,4HAAA,CAAA,UAAO,CAAC,EAAE,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,MAAM,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,WAAW,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,UAAU,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,UAAU,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,mBAAmB,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,OAAO,EAAE,EACnK,EAAE,CAAC,4HAAA,CAAA,UAAO,CAAC,mBAAmB,EAAE;YAEnC,gBAAgB;YAChB,OAAQ;gBACN,KAAK;oBACH,QAAQ,MAAM,KAAK,CAAC,4HAAA,CAAA,UAAO,CAAC,UAAU,EAAE;wBAAE,WAAW;oBAAK;oBAC1D;gBACF,KAAK;oBACH,QAAQ,MAAM,KAAK,CAAC,4HAAA,CAAA,UAAO,CAAC,MAAM,EAAE;wBAAE,WAAW;oBAAM;oBACvD;gBACF,KAAK;oBACH,QAAQ,MAAM,KAAK,CAAC,4HAAA,CAAA,UAAO,CAAC,MAAM,EAAE;wBAAE,WAAW;oBAAK;oBACtD;gBACF,KAAK;gBACL;oBACE,QAAQ,MAAM,KAAK,CAAC,4HAAA,CAAA,UAAO,CAAC,UAAU,EAAE;wBAAE,WAAW;oBAAM;oBAC3D;YACJ;YAEA,QAAQ,MAAM,KAAK,CAAC,MAAM,OAAO,QAAQ;YAEzC,MAAM,EAAE,MAAM,OAAO,EAAE,OAAO,YAAY,EAAE,GAAG,MAAM;YAErD,IAAI,cAAc;gBAChB,MAAM,IAAI,MAAM;YAClB;YAEA,IAAI,CAAC,WAAW,QAAQ,MAAM,KAAK,GAAG;gBACpC,OAAO;oBACL,OAAO,EAAE;oBACT;oBACA,SAAS;oBACT,aAAa;gBACf;YACF;YAEA,8CAA8C;YAC9C,MAAM,UAAU,QAAQ,GAAG,CAAC,CAAC,SAAW,OAAO,OAAO;YAEtD,kEAAkE;YAClE,MAAM,CAAC,kBAAkB,iBAAiB,GAAG,MAAM,QAAQ,GAAG,CAAC;gBAC7D,SACG,IAAI,CAAC,4HAAA,CAAA,SAAM,CAAC,iBAAiB,EAC7B,MAAM,CAAC,GAAG,4HAAA,CAAA,UAAO,CAAC,EAAE,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,IAAI,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,KAAK,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,UAAU,EAAE,EAChF,EAAE,CAAC,4HAAA,CAAA,UAAO,CAAC,EAAE,EAAE;gBAClB,SACG,IAAI,CAAC,4HAAA,CAAA,SAAM,CAAC,iBAAiB,EAC7B,MAAM,CACL,GAAG,4HAAA,CAAA,UAAO,CAAC,EAAE,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,aAAa,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,aAAa,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,QAAQ,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,IAAI,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,KAAK,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,OAAO,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,YAAY,EAAE,EAEzK,EAAE,CAAC,4HAAA,CAAA,UAAO,CAAC,EAAE,EAAE;aACnB;YAED,8BAA8B;YAC9B,MAAM,sBAAsB,IAAI,IAC9B,iBAAiB,IAAI,EAAE,IAAI,CAAC,UAAY;oBAAC,QAAQ,EAAE;oBAAE;iBAAQ,KAAK,EAAE;YAEtE,MAAM,sBAAsB,IAAI,IAC9B,iBAAiB,IAAI,EAAE,IAAI,CAAC,UAAY;oBAAC,QAAQ,EAAE;oBAAE;iBAAQ,KAAK,EAAE;YAGtE,oDAAoD;YACpD,MAAM,mBAAmB,QACtB,GAAG,CAAC,CAAC;gBACJ,MAAM,kBAAkB,oBAAoB,GAAG,CAAC,OAAO,OAAO;gBAC9D,MAAM,kBAAkB,oBAAoB,GAAG,CAAC,OAAO,OAAO;gBAE9D,IAAI,iBAAiB;oBACnB,OAAO;wBACL,IAAI,OAAO,EAAE;wBACb,QAAQ,OAAO,MAAM;wBACrB,aAAa,OAAO,WAAW;wBAC/B,YAAY,OAAO,UAAU;wBAC7B,YAAY,OAAO,UAAU;wBAC7B,qBAAqB,OAAO,mBAAmB;wBAC/C,SAAS,OAAO,OAAO;wBACvB,mBAAmB;wBACnB,cAAc;oBAChB;gBACF,OAAO,IAAI,iBAAiB;oBAC1B,OAAO;wBACL,IAAI,OAAO,EAAE;wBACb,QAAQ,OAAO,MAAM;wBACrB,aAAa,OAAO,WAAW;wBAC/B,YAAY,OAAO,UAAU;wBAC7B,YAAY,OAAO,UAAU;wBAC7B,qBAAqB,OAAO,mBAAmB;wBAC/C,SAAS,OAAO,OAAO;wBACvB,mBAAmB;wBACnB,cAAc;oBAChB;gBACF;gBACA,OAAO;YACT,GACC,MAAM,CAAC,CAAC,OAAS,SAAS;YAE7B,MAAM,UAAU,aAAa,OAAO;YAEpC,OAAO;gBACL,OAAO;gBACP;gBACA;gBACA,aAAa;YACf;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0CAA0C;YACxD,MAAM;QACR;IACF;AACF;AAKO,eAAe,mBACpB,MAAc;IAEd,IAAI;QACF,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD;QAElC,oCAAoC;QACpC,MAAM,CAAC,aAAa,eAAe,oBAAoB,GAAG,MAAM,QAAQ,GAAG,CACzE;YACE,SACG,IAAI,CAAC,4HAAA,CAAA,SAAM,CAAC,KAAK,EACjB,MAAM,CAAC,4HAAA,CAAA,UAAO,CAAC,EAAE,EAAE;gBAAE,OAAO;YAAQ,GACpC,EAAE,CAAC,4HAAA,CAAA,UAAO,CAAC,OAAO,EAAE;YACvB,SACG,IAAI,CAAC,4HAAA,CAAA,SAAM,CAAC,eAAe,EAC3B,MAAM,CAAC,4HAAA,CAAA,UAAO,CAAC,EAAE,EAAE;gBAAE,OAAO;YAAQ,GACpC,EAAE,CAAC,4HAAA,CAAA,UAAO,CAAC,OAAO,EAAE;YACvB,SACG,IAAI,CAAC,4HAAA,CAAA,SAAM,CAAC,aAAa,EACzB,MAAM,CAAC,4HAAA,CAAA,UAAO,CAAC,EAAE,EAAE;gBAAE,OAAO;YAAQ,GACpC,EAAE,CAAC,4HAAA,CAAA,UAAO,CAAC,OAAO,EAAE;SACxB;QAGH,MAAM,UAA2B;YAC/B,YAAY,YAAY,KAAK,IAAI;YACjC,aAAa,cAAc,KAAK,IAAI;YACpC,mBAAmB,oBAAoB,KAAK,IAAI;YAChD,aAAa,IAAI,OAAO,WAAW;QACrC;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oCAAoC;QAClD,OAAO;IACT;AACF;AAGO,MAAM,gBAAgB;IAC3B;IACA;IACA;IACA,sBAAsB;AACxB", "debugId": null}}, {"offset": {"line": 1091, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/customer/subscriptions/actions.ts"], "sourcesContent": ["\"use server\";\r\n\r\nimport { subscriptionsService, SubscriptionWithProfile, SubscriptionsResult } from '@/lib/services/socialService';\r\n\r\n// Re-export types for compatibility\r\nexport type { SubscriptionWithProfile };\r\n\r\n/**\r\n * Fetch subscriptions with pagination and search\r\n */\r\nexport async function fetchSubscriptions(\r\n  userId: string,\r\n  page: number = 1,\r\n  limit: number = 12, // Optimized for 3-column grid\r\n  searchTerm: string = \"\"\r\n): Promise<SubscriptionsResult> {\r\n  try {\r\n    return await subscriptionsService.fetchSubscriptions(userId, page, limit, searchTerm);\r\n  } catch (error) {\r\n    console.error('Error in fetchSubscriptions:', error);\r\n    throw error;\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;AAEA;;;;;AAQO,eAAe,mBACpB,MAAc,EACd,OAAe,CAAC,EAChB,QAAgB,EAAE,EAClB,aAAqB,EAAE;IAEvB,IAAI;QACF,OAAO,MAAM,gIAAA,CAAA,uBAAoB,CAAC,kBAAkB,CAAC,QAAQ,MAAM,OAAO;IAC5E,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gCAAgC;QAC9C,MAAM;IACR;AACF;;;IAZsB;;AAAA,+OAAA", "debugId": null}}, {"offset": {"line": 1120, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/actions/interactions.ts"], "sourcesContent": ["\"use server\";\r\n\r\nimport { createClient } from \"@/utils/supabase/server\";\r\nimport { revalidatePath } from \"next/cache\";\r\n// getSecureBusinessProfileBySlug is imported but not used in this file\r\n// import { getSecureBusinessProfileBySlug } from './secureBusinessProfiles';\r\n// import { cookies } from 'next/headers'; // Removed unused import\r\n\r\nexport async function subscribeToBusiness(\r\n  businessProfileId: string\r\n): Promise<{ success: boolean; error?: string }> {\r\n  // const cookieStore = cookies(); // No longer needed here\r\n  const supabase = await createClient(); // Await the async function\r\n\r\n  const {\r\n    data: { user },\r\n    error: authError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (authError || !user) {\r\n    return { success: false, error: \"User not authenticated.\" };\r\n  }\r\n\r\n  // Prevent a business from subscribing to their own business card\r\n  if (user.id === businessProfileId) {\r\n    return {\r\n      success: false,\r\n      error: \"You cannot subscribe to your own business card.\",\r\n    };\r\n  }\r\n\r\n  // Check if the current user is a business (has a business profile)\r\n  const { data: userBusinessProfile } = await supabase\r\n    .from(\"business_profiles\")\r\n    .select(\"id\")\r\n    .eq(\"id\", user.id)\r\n    .maybeSingle();\r\n\r\n  try {\r\n    // 1. Insert subscription - Use regular client with proper RLS\r\n    const { error: insertError } = await supabase\r\n      .from(\"subscriptions\")\r\n      .insert({ user_id: user.id, business_profile_id: businessProfileId });\r\n\r\n    if (insertError) {\r\n      // Handle potential unique constraint violation (already subscribed) gracefully\r\n      if (insertError.code === \"23505\") {\r\n        // unique_violation\r\n        console.log(\r\n          `User ${user.id} already subscribed to business ${businessProfileId}.`\r\n        );\r\n        // Optionally return success true if already subscribed is acceptable\r\n        return { success: true };\r\n      }\r\n      console.error(\"Error inserting subscription:\", insertError);\r\n      throw new Error(insertError.message);\r\n    }\r\n\r\n    // Note: We don't need to manually update the subscription count\r\n    // The database trigger 'update_total_subscriptions' will handle this automatically\r\n\r\n    // 3. Revalidate paths\r\n    // Revalidate the specific card page and potentially the user's dashboard\r\n    // Use regular client - business_profiles has public read access\r\n    const { data: cardData } = await supabase\r\n      .from(\"business_profiles\")\r\n      .select(\"business_slug\")\r\n      .eq(\"id\", businessProfileId)\r\n      .single();\r\n\r\n    if (cardData?.business_slug) {\r\n      revalidatePath(`/${cardData.business_slug}`);\r\n    }\r\n    revalidatePath(\"/dashboard/customer\"); // Revalidate customer dashboard\r\n\r\n    // Check if the current user is a business and revalidate business dashboard\r\n    if (userBusinessProfile) {\r\n      revalidatePath(\"/dashboard/business\"); // Revalidate business dashboard\r\n      revalidatePath(\"/dashboard/business/subscriptions\"); // Revalidate business subscriptions page\r\n    }\r\n\r\n    // Revalidate the activities page for the business\r\n    revalidatePath(`/dashboard/business/activities`);\r\n\r\n    return { success: true };\r\n  } catch (error) {\r\n    console.error(\"Unexpected error in subscribeToBusiness:\", error);\r\n    const errorMessage =\r\n      error instanceof Error ? error.message : \"An unexpected error occurred.\";\r\n    return { success: false, error: errorMessage };\r\n  }\r\n}\r\n\r\n// --- Implementation for other actions ---\r\n\r\nexport async function unsubscribeFromBusiness(\r\n  businessProfileId: string\r\n): Promise<{ success: boolean; error?: string }> {\r\n  // const cookieStore = cookies();\r\n  const supabase = await createClient(); // Await the async function\r\n\r\n  const {\r\n    data: { user },\r\n    error: authError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (authError || !user) {\r\n    return { success: false, error: \"User not authenticated.\" };\r\n  }\r\n\r\n  // Prevent a business from unsubscribing from their own business card\r\n  if (user.id === businessProfileId) {\r\n    return {\r\n      success: false,\r\n      error: \"You cannot unsubscribe from your own business card.\",\r\n    };\r\n  }\r\n\r\n  // Check if the current user is a business (has a business profile)\r\n  const { data: userBusinessProfile } = await supabase\r\n    .from(\"business_profiles\")\r\n    .select(\"id\")\r\n    .eq(\"id\", user.id)\r\n    .maybeSingle();\r\n\r\n  try {\r\n    // 1. Delete subscription - Use regular client with proper RLS\r\n    const { error: deleteError } = await supabase\r\n      .from(\"subscriptions\")\r\n      .delete()\r\n      .match({ user_id: user.id, business_profile_id: businessProfileId });\r\n\r\n    if (deleteError) {\r\n      console.error(\"Error deleting subscription:\", deleteError);\r\n      throw new Error(deleteError.message);\r\n    }\r\n\r\n    // Note: We don't need to manually update the subscription count\r\n    // The database trigger 'update_total_subscriptions' will handle this automatically\r\n\r\n    // 3. Revalidate paths\r\n    // Use regular client - business_profiles has public read access\r\n    const { data: cardData } = await supabase\r\n      .from(\"business_profiles\")\r\n      .select(\"business_slug\")\r\n      .eq(\"id\", businessProfileId)\r\n      .single();\r\n\r\n    if (cardData?.business_slug) {\r\n      revalidatePath(`/${cardData.business_slug}`);\r\n    }\r\n    revalidatePath(\"/dashboard/customer\");\r\n\r\n    // Check if the current user is a business and revalidate business dashboard\r\n    if (userBusinessProfile) {\r\n      revalidatePath(\"/dashboard/business\"); // Revalidate business dashboard\r\n      revalidatePath(\"/dashboard/business/subscriptions\"); // Revalidate business subscriptions page\r\n    }\r\n\r\n    // Revalidate the activities page for the business\r\n    revalidatePath(`/dashboard/business/activities`);\r\n\r\n    return { success: true };\r\n  } catch (error) {\r\n    console.error(\"Unexpected error in unsubscribeFromBusiness:\", error);\r\n    const errorMessage =\r\n      error instanceof Error ? error.message : \"An unexpected error occurred.\";\r\n    return { success: false, error: errorMessage };\r\n  }\r\n}\r\n\r\nexport async function submitReview(\r\n  businessProfileId: string,\r\n  rating: number,\r\n  reviewText?: string | null // Allow null for review text\r\n): Promise<{ success: boolean; error?: string }> {\r\n  // const cookieStore = cookies();\r\n  const supabase = await createClient(); // Await the async function\r\n\r\n  const {\r\n    data: { user },\r\n    error: authError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (authError || !user) {\r\n    return { success: false, error: \"User not authenticated.\" };\r\n  }\r\n\r\n  // Prevent a business from reviewing their own business card\r\n  if (user.id === businessProfileId) {\r\n    return { success: false, error: \"You cannot review your own business card.\" };\r\n  }\r\n\r\n  if (rating < 1 || rating > 5) {\r\n    return { success: false, error: \"Rating must be between 1 and 5.\" };\r\n  }\r\n\r\n  try {\r\n    // Upsert the review: insert if not exists, update if exists - Use regular client with proper RLS\r\n    const { error: upsertError } = await supabase\r\n      .from(\"ratings_reviews\")\r\n      .upsert(\r\n        {\r\n          user_id: user.id,\r\n          business_profile_id: businessProfileId,\r\n          rating: rating,\r\n          review_text: reviewText, // Pass reviewText directly\r\n          updated_at: new Date().toISOString(), // Explicitly set updated_at on upsert\r\n        },\r\n        {\r\n          onConflict: \"user_id, business_profile_id\", // Specify conflict target\r\n        }\r\n      );\r\n\r\n    if (upsertError) {\r\n      console.error(\"Error submitting review:\", upsertError);\r\n      throw new Error(upsertError.message);\r\n    }\r\n\r\n    // Average rating is handled by the database trigger\r\n\r\n    // Revalidate paths\r\n    // Use regular client - business_profiles has public read access\r\n    const { data: cardData } = await supabase\r\n      .from(\"business_profiles\")\r\n      .select(\"business_slug\")\r\n      .eq(\"id\", businessProfileId)\r\n      .single();\r\n\r\n    if (cardData?.business_slug) {\r\n      revalidatePath(`/${cardData.business_slug}`);\r\n    }\r\n    revalidatePath(\"/dashboard/customer\"); // Revalidate customer dashboard where reviews might be shown\r\n\r\n    // Revalidate the activities page for the business\r\n    revalidatePath(`/dashboard/business/activities`);\r\n\r\n    return { success: true };\r\n  } catch (error) {\r\n    console.error(\"Unexpected error in submitReview:\", error);\r\n    const errorMessage =\r\n      error instanceof Error ? error.message : \"An unexpected error occurred.\";\r\n    return { success: false, error: errorMessage };\r\n  }\r\n}\r\n\r\nexport async function deleteReview(\r\n  businessProfileId: string\r\n): Promise<{ success: boolean; error?: string }> {\r\n  // const cookieStore = cookies();\r\n  const supabase = await createClient(); // Await the async function\r\n\r\n  const {\r\n    data: { user },\r\n    error: authError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (authError || !user) {\r\n    return { success: false, error: \"User not authenticated.\" };\r\n  }\r\n\r\n  try {\r\n    // Use regular client with proper RLS\r\n    const { error: deleteError } = await supabase\r\n      .from(\"ratings_reviews\")\r\n      .delete()\r\n      .match({ user_id: user.id, business_profile_id: businessProfileId });\r\n\r\n    if (deleteError) {\r\n      console.error(\"Error deleting review:\", deleteError);\r\n      throw new Error(deleteError.message);\r\n    }\r\n\r\n    // Average rating is handled by the database trigger\r\n\r\n    // Revalidate paths\r\n    // Use regular client - business_profiles has public read access\r\n    const { data: cardData } = await supabase\r\n      .from(\"business_profiles\")\r\n      .select(\"business_slug\")\r\n      .eq(\"id\", businessProfileId)\r\n      .single();\r\n\r\n    if (cardData?.business_slug) {\r\n      revalidatePath(`/${cardData.business_slug}`);\r\n    }\r\n    revalidatePath(\"/dashboard/customer\");\r\n\r\n    // Revalidate the activities page for the business\r\n    revalidatePath(`/dashboard/business/activities`);\r\n\r\n    return { success: true };\r\n  } catch (error) {\r\n    console.error(\"Unexpected error in deleteReview:\", error);\r\n    const errorMessage =\r\n      error instanceof Error ? error.message : \"An unexpected error occurred.\";\r\n    return { success: false, error: errorMessage };\r\n  }\r\n}\r\n\r\nexport async function likeBusiness(\r\n  businessProfileId: string\r\n): Promise<{ success: boolean; error?: string }> {\r\n  // const cookieStore = cookies();\r\n  const supabase = await createClient(); // Await the async function\r\n\r\n  const {\r\n    data: { user },\r\n    error: authError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (authError || !user) {\r\n    return { success: false, error: \"User not authenticated.\" };\r\n  }\r\n\r\n  // Prevent a business from liking their own business card\r\n  if (user.id === businessProfileId) {\r\n    return { success: false, error: \"You cannot like your own business card.\" };\r\n  }\r\n\r\n  try {\r\n    // 1. Insert like - Use regular client with proper RLS\r\n    const { error: insertError } = await supabase\r\n      .from(\"likes\")\r\n      .insert({ user_id: user.id, business_profile_id: businessProfileId });\r\n\r\n    if (insertError) {\r\n      // Handle potential unique constraint violation (already liked) gracefully\r\n      if (insertError.code === \"23505\") {\r\n        // unique_violation\r\n        console.log(\r\n          `User ${user.id} already liked business ${businessProfileId}.`\r\n        );\r\n        return { success: true }; // Consider it success if already liked\r\n      }\r\n      console.error(\"Error inserting like:\", insertError);\r\n      throw new Error(insertError.message);\r\n    }\r\n\r\n    // Note: We don't need to manually update the like count\r\n    // The database trigger 'update_total_likes' will handle this automatically\r\n\r\n    // 3. Revalidate paths\r\n    // Use regular client - business_profiles has public read access\r\n    const { data: cardData } = await supabase\r\n      .from(\"business_profiles\")\r\n      .select(\"business_slug\")\r\n      .eq(\"id\", businessProfileId)\r\n      .single();\r\n\r\n    if (cardData?.business_slug) {\r\n      revalidatePath(`/${cardData.business_slug}`);\r\n    }\r\n\r\n    // Check if the current user is a business and revalidate business dashboard\r\n    const { data: userBusinessProfile } = await supabase\r\n      .from(\"business_profiles\")\r\n      .select(\"id\")\r\n      .eq(\"id\", user.id)\r\n      .maybeSingle();\r\n\r\n    if (userBusinessProfile) {\r\n      revalidatePath(\"/dashboard/business\"); // Revalidate business dashboard\r\n      revalidatePath(\"/dashboard/business/likes\"); // Revalidate business likes page\r\n    }\r\n\r\n    // Revalidate the activities page for the business\r\n    revalidatePath(`/dashboard/business/activities`);\r\n\r\n    return { success: true };\r\n  } catch (error) {\r\n    console.error(\"Unexpected error in likeBusiness:\", error);\r\n    const errorMessage =\r\n      error instanceof Error ? error.message : \"An unexpected error occurred.\";\r\n    return { success: false, error: errorMessage };\r\n  }\r\n}\r\n\r\nexport async function unlikeBusiness(\r\n  businessProfileId: string\r\n): Promise<{ success: boolean; error?: string }> {\r\n  // const cookieStore = cookies();\r\n  const supabase = await createClient(); // Await the async function\r\n\r\n  const {\r\n    data: { user },\r\n    error: authError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (authError || !user) {\r\n    return { success: false, error: \"User not authenticated.\" };\r\n  }\r\n\r\n  // Prevent a business from unliking their own business card\r\n  if (user.id === businessProfileId) {\r\n    return {\r\n      success: false,\r\n      error: \"You cannot unlike your own business card.\",\r\n    };\r\n  }\r\n\r\n  try {\r\n    // 1. Delete like - Use regular client with proper RLS\r\n    const { error: deleteError } = await supabase\r\n      .from(\"likes\")\r\n      .delete()\r\n      .match({ user_id: user.id, business_profile_id: businessProfileId });\r\n\r\n    if (deleteError) {\r\n      console.error(\"Error deleting like:\", deleteError);\r\n      throw new Error(deleteError.message);\r\n    }\r\n\r\n    // Note: We don't need to manually update the like count\r\n    // The database trigger 'update_total_likes' will handle this automatically\r\n\r\n    // 3. Revalidate paths\r\n    // Use regular client - business_profiles has public read access\r\n    const { data: cardData } = await supabase\r\n      .from(\"business_profiles\")\r\n      .select(\"business_slug\")\r\n      .eq(\"id\", businessProfileId)\r\n      .single();\r\n\r\n    if (cardData?.business_slug) {\r\n      revalidatePath(`/${cardData.business_slug}`);\r\n    }\r\n\r\n    // Check if the current user is a business and revalidate business dashboard\r\n    const { data: userBusinessProfile } = await supabase\r\n      .from(\"business_profiles\")\r\n      .select(\"id\")\r\n      .eq(\"id\", user.id)\r\n      .maybeSingle();\r\n\r\n    if (userBusinessProfile) {\r\n      revalidatePath(\"/dashboard/business\"); // Revalidate business dashboard\r\n      revalidatePath(\"/dashboard/business/likes\"); // Revalidate business likes page\r\n    }\r\n\r\n    // Revalidate the activities page for the business\r\n    revalidatePath(`/dashboard/business/activities`);\r\n\r\n    return { success: true };\r\n  } catch (error) {\r\n    console.error(\"Unexpected error in unlikeBusiness:\", error);\r\n    const errorMessage =\r\n      error instanceof Error ? error.message : \"An unexpected error occurred.\";\r\n    return { success: false, error: errorMessage };\r\n  }\r\n}\r\n\r\nexport async function getInteractionStatus(businessProfileId: string): Promise<{\r\n  isSubscribed: boolean;\r\n  hasLiked: boolean;\r\n  userRating: number | null;\r\n  userReview: string | null;\r\n  error?: string;\r\n}> {\r\n  // const cookieStore = cookies();\r\n  const supabase = await createClient(); // Await the async function\r\n  let userId: string | null = null;\r\n\r\n  // Try to get authenticated user, but proceed even if not logged in\r\n  const {\r\n    data: { user },\r\n  } = await supabase.auth.getUser();\r\n  if (user) {\r\n    userId = user.id;\r\n  }\r\n\r\n  // Default status for anonymous users\r\n  const defaultStatus = {\r\n    isSubscribed: false,\r\n    hasLiked: false,\r\n    userRating: null,\r\n    userReview: null,\r\n  };\r\n\r\n  if (!userId) {\r\n    return defaultStatus; // Return default if no user is logged in\r\n  }\r\n\r\n  try {\r\n    // Use regular client - all these tables have public read access\r\n    // Fetch all statuses in parallel\r\n    const [subscriptionRes, likeRes, reviewRes] = await Promise.all([\r\n      supabase\r\n        .from(\"subscriptions\")\r\n        .select(\"id\", { count: \"exact\", head: true }) // Just check existence\r\n        .match({ user_id: userId, business_profile_id: businessProfileId }),\r\n      supabase\r\n        .from(\"likes\")\r\n        .select(\"id\", { count: \"exact\", head: true }) // Just check existence\r\n        .match({ user_id: userId, business_profile_id: businessProfileId }),\r\n      supabase\r\n        .from(\"ratings_reviews\")\r\n        .select(\"rating, review_text\")\r\n        .match({ user_id: userId, business_profile_id: businessProfileId })\r\n        .maybeSingle(), // Use maybeSingle as user might not have reviewed\r\n    ]);\r\n\r\n    // Check for errors in parallel fetches\r\n    if (subscriptionRes.error)\r\n      throw new Error(\r\n        `Subscription fetch error: ${subscriptionRes.error.message}`\r\n      );\r\n    if (likeRes.error)\r\n      throw new Error(`Like fetch error: ${likeRes.error.message}`);\r\n    if (reviewRes.error)\r\n      throw new Error(`Review fetch error: ${reviewRes.error.message}`);\r\n\r\n    const reviewData = reviewRes.data;\r\n\r\n    return {\r\n      isSubscribed: (subscriptionRes.count ?? 0) > 0,\r\n      hasLiked: (likeRes.count ?? 0) > 0,\r\n      userRating: reviewData?.rating ?? null,\r\n      userReview: reviewData?.review_text ?? null,\r\n    };\r\n  } catch (error) {\r\n    console.error(\"Error fetching interaction status:\", error);\r\n    const errorMessage =\r\n      error instanceof Error ? error.message : \"An unexpected error occurred.\";\r\n    // Return default status but include the error message\r\n    return { ...defaultStatus, error: errorMessage };\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;AAEA;AACA;;;;;;AAKO,eAAe,oBACpB,iBAAyB;IAEzB,0DAA0D;IAC1D,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD,KAAK,2BAA2B;IAElE,MAAM,EACJ,MAAM,EAAE,IAAI,EAAE,EACd,OAAO,SAAS,EACjB,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;IAE/B,IAAI,aAAa,CAAC,MAAM;QACtB,OAAO;YAAE,SAAS;YAAO,OAAO;QAA0B;IAC5D;IAEA,iEAAiE;IACjE,IAAI,KAAK,EAAE,KAAK,mBAAmB;QACjC,OAAO;YACL,SAAS;YACT,OAAO;QACT;IACF;IAEA,mEAAmE;IACnE,MAAM,EAAE,MAAM,mBAAmB,EAAE,GAAG,MAAM,SACzC,IAAI,CAAC,qBACL,MAAM,CAAC,MACP,EAAE,CAAC,MAAM,KAAK,EAAE,EAChB,WAAW;IAEd,IAAI;QACF,8DAA8D;QAC9D,MAAM,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,SAClC,IAAI,CAAC,iBACL,MAAM,CAAC;YAAE,SAAS,KAAK,EAAE;YAAE,qBAAqB;QAAkB;QAErE,IAAI,aAAa;YACf,+EAA+E;YAC/E,IAAI,YAAY,IAAI,KAAK,SAAS;gBAChC,mBAAmB;gBACnB,QAAQ,GAAG,CACT,CAAC,KAAK,EAAE,KAAK,EAAE,CAAC,gCAAgC,EAAE,kBAAkB,CAAC,CAAC;gBAExE,qEAAqE;gBACrE,OAAO;oBAAE,SAAS;gBAAK;YACzB;YACA,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,MAAM,IAAI,MAAM,YAAY,OAAO;QACrC;QAEA,gEAAgE;QAChE,mFAAmF;QAEnF,sBAAsB;QACtB,yEAAyE;QACzE,gEAAgE;QAChE,MAAM,EAAE,MAAM,QAAQ,EAAE,GAAG,MAAM,SAC9B,IAAI,CAAC,qBACL,MAAM,CAAC,iBACP,EAAE,CAAC,MAAM,mBACT,MAAM;QAET,IAAI,UAAU,eAAe;YAC3B,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE,CAAC,CAAC,EAAE,SAAS,aAAa,EAAE;QAC7C;QACA,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE,wBAAwB,gCAAgC;QAEvE,4EAA4E;QAC5E,IAAI,qBAAqB;YACvB,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE,wBAAwB,gCAAgC;YACvE,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE,sCAAsC,yCAAyC;QAChG;QAEA,kDAAkD;QAClD,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE,CAAC,8BAA8B,CAAC;QAE/C,OAAO;YAAE,SAAS;QAAK;IACzB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4CAA4C;QAC1D,MAAM,eACJ,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAC3C,OAAO;YAAE,SAAS;YAAO,OAAO;QAAa;IAC/C;AACF;AAIO,eAAe,wBACpB,iBAAyB;IAEzB,iCAAiC;IACjC,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD,KAAK,2BAA2B;IAElE,MAAM,EACJ,MAAM,EAAE,IAAI,EAAE,EACd,OAAO,SAAS,EACjB,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;IAE/B,IAAI,aAAa,CAAC,MAAM;QACtB,OAAO;YAAE,SAAS;YAAO,OAAO;QAA0B;IAC5D;IAEA,qEAAqE;IACrE,IAAI,KAAK,EAAE,KAAK,mBAAmB;QACjC,OAAO;YACL,SAAS;YACT,OAAO;QACT;IACF;IAEA,mEAAmE;IACnE,MAAM,EAAE,MAAM,mBAAmB,EAAE,GAAG,MAAM,SACzC,IAAI,CAAC,qBACL,MAAM,CAAC,MACP,EAAE,CAAC,MAAM,KAAK,EAAE,EAChB,WAAW;IAEd,IAAI;QACF,8DAA8D;QAC9D,MAAM,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,SAClC,IAAI,CAAC,iBACL,MAAM,GACN,KAAK,CAAC;YAAE,SAAS,KAAK,EAAE;YAAE,qBAAqB;QAAkB;QAEpE,IAAI,aAAa;YACf,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,MAAM,IAAI,MAAM,YAAY,OAAO;QACrC;QAEA,gEAAgE;QAChE,mFAAmF;QAEnF,sBAAsB;QACtB,gEAAgE;QAChE,MAAM,EAAE,MAAM,QAAQ,EAAE,GAAG,MAAM,SAC9B,IAAI,CAAC,qBACL,MAAM,CAAC,iBACP,EAAE,CAAC,MAAM,mBACT,MAAM;QAET,IAAI,UAAU,eAAe;YAC3B,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE,CAAC,CAAC,EAAE,SAAS,aAAa,EAAE;QAC7C;QACA,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE;QAEf,4EAA4E;QAC5E,IAAI,qBAAqB;YACvB,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE,wBAAwB,gCAAgC;YACvE,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE,sCAAsC,yCAAyC;QAChG;QAEA,kDAAkD;QAClD,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE,CAAC,8BAA8B,CAAC;QAE/C,OAAO;YAAE,SAAS;QAAK;IACzB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gDAAgD;QAC9D,MAAM,eACJ,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAC3C,OAAO;YAAE,SAAS;YAAO,OAAO;QAAa;IAC/C;AACF;AAEO,eAAe,aACpB,iBAAyB,EACzB,MAAc,EACd,UAA0B,AAAC,6BAA6B;;IAExD,iCAAiC;IACjC,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD,KAAK,2BAA2B;IAElE,MAAM,EACJ,MAAM,EAAE,IAAI,EAAE,EACd,OAAO,SAAS,EACjB,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;IAE/B,IAAI,aAAa,CAAC,MAAM;QACtB,OAAO;YAAE,SAAS;YAAO,OAAO;QAA0B;IAC5D;IAEA,4DAA4D;IAC5D,IAAI,KAAK,EAAE,KAAK,mBAAmB;QACjC,OAAO;YAAE,SAAS;YAAO,OAAO;QAA4C;IAC9E;IAEA,IAAI,SAAS,KAAK,SAAS,GAAG;QAC5B,OAAO;YAAE,SAAS;YAAO,OAAO;QAAkC;IACpE;IAEA,IAAI;QACF,iGAAiG;QACjG,MAAM,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,SAClC,IAAI,CAAC,mBACL,MAAM,CACL;YACE,SAAS,KAAK,EAAE;YAChB,qBAAqB;YACrB,QAAQ;YACR,aAAa;YACb,YAAY,IAAI,OAAO,WAAW;QACpC,GACA;YACE,YAAY;QACd;QAGJ,IAAI,aAAa;YACf,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,MAAM,IAAI,MAAM,YAAY,OAAO;QACrC;QAEA,oDAAoD;QAEpD,mBAAmB;QACnB,gEAAgE;QAChE,MAAM,EAAE,MAAM,QAAQ,EAAE,GAAG,MAAM,SAC9B,IAAI,CAAC,qBACL,MAAM,CAAC,iBACP,EAAE,CAAC,MAAM,mBACT,MAAM;QAET,IAAI,UAAU,eAAe;YAC3B,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE,CAAC,CAAC,EAAE,SAAS,aAAa,EAAE;QAC7C;QACA,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE,wBAAwB,6DAA6D;QAEpG,kDAAkD;QAClD,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE,CAAC,8BAA8B,CAAC;QAE/C,OAAO;YAAE,SAAS;QAAK;IACzB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qCAAqC;QACnD,MAAM,eACJ,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAC3C,OAAO;YAAE,SAAS;YAAO,OAAO;QAAa;IAC/C;AACF;AAEO,eAAe,aACpB,iBAAyB;IAEzB,iCAAiC;IACjC,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD,KAAK,2BAA2B;IAElE,MAAM,EACJ,MAAM,EAAE,IAAI,EAAE,EACd,OAAO,SAAS,EACjB,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;IAE/B,IAAI,aAAa,CAAC,MAAM;QACtB,OAAO;YAAE,SAAS;YAAO,OAAO;QAA0B;IAC5D;IAEA,IAAI;QACF,qCAAqC;QACrC,MAAM,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,SAClC,IAAI,CAAC,mBACL,MAAM,GACN,KAAK,CAAC;YAAE,SAAS,KAAK,EAAE;YAAE,qBAAqB;QAAkB;QAEpE,IAAI,aAAa;YACf,QAAQ,KAAK,CAAC,0BAA0B;YACxC,MAAM,IAAI,MAAM,YAAY,OAAO;QACrC;QAEA,oDAAoD;QAEpD,mBAAmB;QACnB,gEAAgE;QAChE,MAAM,EAAE,MAAM,QAAQ,EAAE,GAAG,MAAM,SAC9B,IAAI,CAAC,qBACL,MAAM,CAAC,iBACP,EAAE,CAAC,MAAM,mBACT,MAAM;QAET,IAAI,UAAU,eAAe;YAC3B,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE,CAAC,CAAC,EAAE,SAAS,aAAa,EAAE;QAC7C;QACA,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE;QAEf,kDAAkD;QAClD,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE,CAAC,8BAA8B,CAAC;QAE/C,OAAO;YAAE,SAAS;QAAK;IACzB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qCAAqC;QACnD,MAAM,eACJ,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAC3C,OAAO;YAAE,SAAS;YAAO,OAAO;QAAa;IAC/C;AACF;AAEO,eAAe,aACpB,iBAAyB;IAEzB,iCAAiC;IACjC,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD,KAAK,2BAA2B;IAElE,MAAM,EACJ,MAAM,EAAE,IAAI,EAAE,EACd,OAAO,SAAS,EACjB,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;IAE/B,IAAI,aAAa,CAAC,MAAM;QACtB,OAAO;YAAE,SAAS;YAAO,OAAO;QAA0B;IAC5D;IAEA,yDAAyD;IACzD,IAAI,KAAK,EAAE,KAAK,mBAAmB;QACjC,OAAO;YAAE,SAAS;YAAO,OAAO;QAA0C;IAC5E;IAEA,IAAI;QACF,sDAAsD;QACtD,MAAM,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,SAClC,IAAI,CAAC,SACL,MAAM,CAAC;YAAE,SAAS,KAAK,EAAE;YAAE,qBAAqB;QAAkB;QAErE,IAAI,aAAa;YACf,0EAA0E;YAC1E,IAAI,YAAY,IAAI,KAAK,SAAS;gBAChC,mBAAmB;gBACnB,QAAQ,GAAG,CACT,CAAC,KAAK,EAAE,KAAK,EAAE,CAAC,wBAAwB,EAAE,kBAAkB,CAAC,CAAC;gBAEhE,OAAO;oBAAE,SAAS;gBAAK,GAAG,uCAAuC;YACnE;YACA,QAAQ,KAAK,CAAC,yBAAyB;YACvC,MAAM,IAAI,MAAM,YAAY,OAAO;QACrC;QAEA,wDAAwD;QACxD,2EAA2E;QAE3E,sBAAsB;QACtB,gEAAgE;QAChE,MAAM,EAAE,MAAM,QAAQ,EAAE,GAAG,MAAM,SAC9B,IAAI,CAAC,qBACL,MAAM,CAAC,iBACP,EAAE,CAAC,MAAM,mBACT,MAAM;QAET,IAAI,UAAU,eAAe;YAC3B,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE,CAAC,CAAC,EAAE,SAAS,aAAa,EAAE;QAC7C;QAEA,4EAA4E;QAC5E,MAAM,EAAE,MAAM,mBAAmB,EAAE,GAAG,MAAM,SACzC,IAAI,CAAC,qBACL,MAAM,CAAC,MACP,EAAE,CAAC,MAAM,KAAK,EAAE,EAChB,WAAW;QAEd,IAAI,qBAAqB;YACvB,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE,wBAAwB,gCAAgC;YACvE,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE,8BAA8B,iCAAiC;QAChF;QAEA,kDAAkD;QAClD,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE,CAAC,8BAA8B,CAAC;QAE/C,OAAO;YAAE,SAAS;QAAK;IACzB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qCAAqC;QACnD,MAAM,eACJ,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAC3C,OAAO;YAAE,SAAS;YAAO,OAAO;QAAa;IAC/C;AACF;AAEO,eAAe,eACpB,iBAAyB;IAEzB,iCAAiC;IACjC,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD,KAAK,2BAA2B;IAElE,MAAM,EACJ,MAAM,EAAE,IAAI,EAAE,EACd,OAAO,SAAS,EACjB,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;IAE/B,IAAI,aAAa,CAAC,MAAM;QACtB,OAAO;YAAE,SAAS;YAAO,OAAO;QAA0B;IAC5D;IAEA,2DAA2D;IAC3D,IAAI,KAAK,EAAE,KAAK,mBAAmB;QACjC,OAAO;YACL,SAAS;YACT,OAAO;QACT;IACF;IAEA,IAAI;QACF,sDAAsD;QACtD,MAAM,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,SAClC,IAAI,CAAC,SACL,MAAM,GACN,KAAK,CAAC;YAAE,SAAS,KAAK,EAAE;YAAE,qBAAqB;QAAkB;QAEpE,IAAI,aAAa;YACf,QAAQ,KAAK,CAAC,wBAAwB;YACtC,MAAM,IAAI,MAAM,YAAY,OAAO;QACrC;QAEA,wDAAwD;QACxD,2EAA2E;QAE3E,sBAAsB;QACtB,gEAAgE;QAChE,MAAM,EAAE,MAAM,QAAQ,EAAE,GAAG,MAAM,SAC9B,IAAI,CAAC,qBACL,MAAM,CAAC,iBACP,EAAE,CAAC,MAAM,mBACT,MAAM;QAET,IAAI,UAAU,eAAe;YAC3B,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE,CAAC,CAAC,EAAE,SAAS,aAAa,EAAE;QAC7C;QAEA,4EAA4E;QAC5E,MAAM,EAAE,MAAM,mBAAmB,EAAE,GAAG,MAAM,SACzC,IAAI,CAAC,qBACL,MAAM,CAAC,MACP,EAAE,CAAC,MAAM,KAAK,EAAE,EAChB,WAAW;QAEd,IAAI,qBAAqB;YACvB,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE,wBAAwB,gCAAgC;YACvE,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE,8BAA8B,iCAAiC;QAChF;QAEA,kDAAkD;QAClD,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE,CAAC,8BAA8B,CAAC;QAE/C,OAAO;YAAE,SAAS;QAAK;IACzB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,uCAAuC;QACrD,MAAM,eACJ,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAC3C,OAAO;YAAE,SAAS;YAAO,OAAO;QAAa;IAC/C;AACF;AAEO,eAAe,qBAAqB,iBAAyB;IAOlE,iCAAiC;IACjC,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD,KAAK,2BAA2B;IAClE,IAAI,SAAwB;IAE5B,mEAAmE;IACnE,MAAM,EACJ,MAAM,EAAE,IAAI,EAAE,EACf,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;IAC/B,IAAI,MAAM;QACR,SAAS,KAAK,EAAE;IAClB;IAEA,qCAAqC;IACrC,MAAM,gBAAgB;QACpB,cAAc;QACd,UAAU;QACV,YAAY;QACZ,YAAY;IACd;IAEA,IAAI,CAAC,QAAQ;QACX,OAAO,eAAe,yCAAyC;IACjE;IAEA,IAAI;QACF,gEAAgE;QAChE,iCAAiC;QACjC,MAAM,CAAC,iBAAiB,SAAS,UAAU,GAAG,MAAM,QAAQ,GAAG,CAAC;YAC9D,SACG,IAAI,CAAC,iBACL,MAAM,CAAC,MAAM;gBAAE,OAAO;gBAAS,MAAM;YAAK,GAAG,uBAAuB;aACpE,KAAK,CAAC;gBAAE,SAAS;gBAAQ,qBAAqB;YAAkB;YACnE,SACG,IAAI,CAAC,SACL,MAAM,CAAC,MAAM;gBAAE,OAAO;gBAAS,MAAM;YAAK,GAAG,uBAAuB;aACpE,KAAK,CAAC;gBAAE,SAAS;gBAAQ,qBAAqB;YAAkB;YACnE,SACG,IAAI,CAAC,mBACL,MAAM,CAAC,uBACP,KAAK,CAAC;gBAAE,SAAS;gBAAQ,qBAAqB;YAAkB,GAChE,WAAW;SACf;QAED,uCAAuC;QACvC,IAAI,gBAAgB,KAAK,EACvB,MAAM,IAAI,MACR,CAAC,0BAA0B,EAAE,gBAAgB,KAAK,CAAC,OAAO,EAAE;QAEhE,IAAI,QAAQ,KAAK,EACf,MAAM,IAAI,MAAM,CAAC,kBAAkB,EAAE,QAAQ,KAAK,CAAC,OAAO,EAAE;QAC9D,IAAI,UAAU,KAAK,EACjB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,UAAU,KAAK,CAAC,OAAO,EAAE;QAElE,MAAM,aAAa,UAAU,IAAI;QAEjC,OAAO;YACL,cAAc,CAAC,gBAAgB,KAAK,IAAI,CAAC,IAAI;YAC7C,UAAU,CAAC,QAAQ,KAAK,IAAI,CAAC,IAAI;YACjC,YAAY,YAAY,UAAU;YAClC,YAAY,YAAY,eAAe;QACzC;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,sCAAsC;QACpD,MAAM,eACJ,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAC3C,sDAAsD;QACtD,OAAO;YAAE,GAAG,aAAa;YAAE,OAAO;QAAa;IACjD;AACF;;;IAvgBsB;IAuFA;IA4EA;IA2EA;IAsDA;IA8EA;IA0EA;;AA5bA,+OAAA;AAuFA,+OAAA;AA4EA,+OAAA;AA2EA,+OAAA;AAsDA,+OAAA;AA8EA,+OAAA;AA0EA,+OAAA", "debugId": null}}, {"offset": {"line": 1571, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/.next-internal/server/app/%28dashboard%29/dashboard/customer/subscriptions/page/actions.js%20%28server%20actions%20loader%29"], "sourcesContent": ["export {signOutUser as '0027aa970a57d5cbd11af21933b24fca73bbd99631'} from 'ACTIONS_MODULE0'\nexport {requireCompleteName as '400df42a7734311425e09521ebcc4d895b5368c63a'} from 'ACTIONS_MODULE1'\nexport {getCustomerAddressData as '40746d48fecf14ef44441f42ba6d4270f14c867a68'} from 'ACTIONS_MODULE1'\nexport {validateCustomerName as '407770ce7befdbdc280da755c304531d3f7ea6b5dd'} from 'ACTIONS_MODULE1'\nexport {requireCompleteAddress as '40c05b55f995304a112e3e7557409d074df1c34bf2'} from 'ACTIONS_MODULE1'\nexport {validateCustomerAddress as '40d8cab922554d01cea4f763d816fb4734270ab290'} from 'ACTIONS_MODULE1'\nexport {requireCompleteProfile as '60510e8a33e98dfcb8dbb5099888e59125201d5a12'} from 'ACTIONS_MODULE1'\nexport {fetchSubscriptions as '785a06efde5fbb3a7070604f7529a9aebe50fc71b5'} from 'ACTIONS_MODULE2'\nexport {unsubscribeFromBusiness as '40a7020d8c9a90386aa2f0bbb96a95966205d638a2'} from 'ACTIONS_MODULE3'\n"], "names": [], "mappings": ";AAAA;AACA;AAMA;AACA", "debugId": null}}, {"offset": {"line": 1668, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\";\r\nimport { twMerge } from \"tailwind-merge\";\r\nimport { BusinessCardData } from \"@/app/(dashboard)/dashboard/business/card/schema\";\r\n\r\nexport function cn(...inputs: ClassValue[]) {\r\n  return twMerge(clsx(inputs));\r\n}\r\n\r\n/**\r\n * Cleans and formats phone number from Supabase auth.users table format\r\n * Handles various formats: +918458060663, 918458060663, 8458060663\r\n * Returns clean 10-digit phone number or null if invalid\r\n *\r\n * @param phone - Phone number from Supabase auth.users table\r\n * @returns Clean 10-digit phone number or null if invalid\r\n */\r\nexport function cleanPhoneFromAuth(phone: string | null | undefined): string | null {\r\n  if (!phone) return null;\r\n\r\n  let processedPhone = phone.trim();\r\n\r\n  // Remove +91 prefix if present\r\n  if (processedPhone.startsWith('+91')) {\r\n    processedPhone = processedPhone.substring(3);\r\n  }\r\n  // Remove 91 prefix if it's a 12-digit number starting with 91\r\n  else if (processedPhone.length === 12 && processedPhone.startsWith('91')) {\r\n    processedPhone = processedPhone.substring(2);\r\n  }\r\n\r\n  // Validate it's a 10-digit number\r\n  if (/^\\d{10}$/.test(processedPhone)) {\r\n    return processedPhone;\r\n  }\r\n\r\n  return null; // Invalid format\r\n}\r\n\r\n/**\r\n * Masks a phone number, showing first and last two digits.\r\n * Example: 9123456789 -> 91******89\r\n * Handles null/undefined/empty strings.\r\n */\r\nexport function maskPhoneNumber(phone: string | null | undefined): string {\r\n  if (!phone || phone.length < 4) {\r\n    return \"Invalid Phone\"; // Or return empty string or original if preferred\r\n  }\r\n  const firstTwo = phone.substring(0, 2);\r\n  const lastTwo = phone.substring(phone.length - 2);\r\n  const maskedPart = \"*\".repeat(phone.length - 4);\r\n  return `${firstTwo}${maskedPart}${lastTwo}`;\r\n}\r\n\r\n/**\r\n * Masks an email address.\r\n * Example: <EMAIL> -> ex****@do****.com\r\n * Handles null/undefined/empty strings.\r\n */\r\nexport function maskEmail(email: string | null | undefined): string {\r\n  if (!email || !email.includes(\"@\")) {\r\n    return \"Invalid Email\"; // Or return empty string or original\r\n  }\r\n  const parts = email.split(\"@\");\r\n  const username = parts[0];\r\n  const domain = parts[1];\r\n\r\n  if (username.length <= 2 || domain.length <= 2 || !domain.includes(\".\")) {\r\n    return \"Email Hidden\"; // Simple mask for very short/invalid emails\r\n  }\r\n\r\n  const maskedUsername =\r\n    username.substring(0, 2) + \"*\".repeat(username.length - 2);\r\n\r\n  const domainParts = domain.split(\".\");\r\n  const domainName = domainParts[0];\r\n  const domainTld = domainParts.slice(1).join(\".\"); // Handle multiple parts like .co.uk\r\n\r\n  const maskedDomainName =\r\n    domainName.substring(0, 2) + \"*\".repeat(domainName.length - 2);\r\n\r\n  return `${maskedUsername}@${maskedDomainName}.${domainTld}`;\r\n}\r\n\r\n/**\r\n * Formats a number using the Indian numbering system with short notations.\r\n * Supports: K (Thousand), L (Lakh), Cr (Crore), Ar (Arab), Khar (Kharab), Neel, Padma, Shankh, etc.\r\n * Examples:\r\n *   1_200 -> \"1.2K\"\r\n *   1_20_000 -> \"1.2L\"\r\n *   1_20_00_000 -> \"1.2Cr\"\r\n *   1_20_00_00_000 -> \"1.2Ar\"\r\n *   1_20_00_00_00_000 -> \"1.2Khar\"\r\n *   1_20_00_00_00_00_000 -> \"1.2Neel\"\r\n *   1_20_00_00_00_00_00_000 -> \"1.2Padma\"\r\n *   1_20_00_00_00_00_00_00_000 -> \"1.2Shankh\"\r\n */\r\nexport function formatIndianNumberShort(num: number): string {\r\n  if (num === null || num === undefined || isNaN(num)) return \"0\";\r\n  const absNum = Math.abs(num);\r\n\r\n  // Indian units and their values\r\n  const units = [\r\n    { value: 1e5, symbol: \"L\" }, // Lakh\r\n    { value: 1e7, symbol: \"Cr\" }, // Crore\r\n    { value: 1e9, symbol: \"Ar\" }, // Arab\r\n    { value: 1e11, symbol: \"Khar\" }, // Kharab\r\n    { value: 1e13, symbol: \"Neel\" }, // Neel\r\n    { value: 1e15, symbol: \"Padma\" }, // Padma\r\n    { value: 1e17, symbol: \"Shankh\" }, // Shankh\r\n  ];\r\n\r\n  // For thousands (K), use western style for sub-lakh\r\n  if (absNum < 1e5) {\r\n    if (absNum >= 1e3) {\r\n      return (num / 1e3).toFixed(1).replace(/\\.0$/, \"\") + \"K\";\r\n    }\r\n    return num.toString();\r\n  }\r\n\r\n  // Find the largest unit that fits\r\n  for (let i = units.length - 1; i >= 0; i--) {\r\n    if (absNum >= units[i].value) {\r\n      return (\r\n        (num / units[i].value).toFixed(1).replace(/\\.0$/, \"\") + units[i].symbol\r\n      );\r\n    }\r\n  }\r\n\r\n  // Fallback (should not reach here)\r\n  return num.toString();\r\n}\r\n\r\n/**\r\n * Formats an address from BusinessCardData into a single string\r\n */\r\nexport function formatAddress(data: BusinessCardData): string {\r\n  const addressParts = [\r\n    data.address_line,\r\n    data.locality,\r\n    data.city,\r\n    data.state,\r\n    data.pincode,\r\n  ].filter(Boolean);\r\n\r\n  return addressParts.join(\", \") || \"Address not available\";\r\n}\r\n\r\n/**\r\n * Formats a date in a user-friendly format with Indian Standard Time (IST)\r\n * @param date The date to format\r\n * @param includeTime Whether to include time in the formatted string\r\n * @returns Formatted date string in IST\r\n */\r\nexport function formatDate(date: Date, includeTime: boolean = false): string {\r\n  if (!date || !(date instanceof Date) || isNaN(date.getTime())) {\r\n    return \"Invalid date\";\r\n  }\r\n\r\n  const options: Intl.DateTimeFormatOptions = {\r\n    year: \"numeric\",\r\n    month: \"long\",\r\n    day: \"numeric\",\r\n    timeZone: \"Asia/Kolkata\", // Explicitly set timezone to IST\r\n  };\r\n\r\n  if (includeTime) {\r\n    options.hour = \"2-digit\";\r\n    options.minute = \"2-digit\";\r\n    options.hour12 = true;\r\n  }\r\n\r\n  return date.toLocaleString(\"en-IN\", options);\r\n}\r\n\r\n/**\r\n * Formats a currency amount with the appropriate currency symbol\r\n * @param amount The amount to format\r\n * @param currency The currency code (e.g., INR, USD)\r\n * @returns Formatted currency string\r\n */\r\nexport function formatCurrency(\r\n  amount: number,\r\n  currency: string = \"INR\"\r\n): string {\r\n  if (amount === null || amount === undefined || isNaN(amount)) {\r\n    return \"Invalid amount\";\r\n  }\r\n\r\n  try {\r\n    return new Intl.NumberFormat(\"en-IN\", {\r\n      style: \"currency\",\r\n      currency: currency,\r\n      minimumFractionDigits: 0,\r\n      maximumFractionDigits: 2,\r\n    }).format(amount);\r\n  } catch {\r\n    // Catch any error without using the error variable\r\n    // Fallback in case of invalid currency code\r\n    return `${currency} ${amount.toFixed(2)}`;\r\n  }\r\n}\r\n\r\n/**\r\n * Formats a string to title case (first letter of each word capitalized)\r\n * @param text The text to format\r\n * @returns The text in title case\r\n */\r\nexport function toTitleCase(text: string): string {\r\n  if (!text) return \"\";\r\n\r\n  return text\r\n    .toLowerCase()\r\n    .replace(/\\b\\w/g, (char) => char.toUpperCase());\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;AAAA;AACA;;;AAGO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAUO,SAAS,mBAAmB,KAAgC;IACjE,IAAI,CAAC,OAAO,OAAO;IAEnB,IAAI,iBAAiB,MAAM,IAAI;IAE/B,+BAA+B;IAC/B,IAAI,eAAe,UAAU,CAAC,QAAQ;QACpC,iBAAiB,eAAe,SAAS,CAAC;IAC5C,OAEK,IAAI,eAAe,MAAM,KAAK,MAAM,eAAe,UAAU,CAAC,OAAO;QACxE,iBAAiB,eAAe,SAAS,CAAC;IAC5C;IAEA,kCAAkC;IAClC,IAAI,WAAW,IAAI,CAAC,iBAAiB;QACnC,OAAO;IACT;IAEA,OAAO,MAAM,iBAAiB;AAChC;AAOO,SAAS,gBAAgB,KAAgC;IAC9D,IAAI,CAAC,SAAS,MAAM,MAAM,GAAG,GAAG;QAC9B,OAAO,iBAAiB,kDAAkD;IAC5E;IACA,MAAM,WAAW,MAAM,SAAS,CAAC,GAAG;IACpC,MAAM,UAAU,MAAM,SAAS,CAAC,MAAM,MAAM,GAAG;IAC/C,MAAM,aAAa,IAAI,MAAM,CAAC,MAAM,MAAM,GAAG;IAC7C,OAAO,GAAG,WAAW,aAAa,SAAS;AAC7C;AAOO,SAAS,UAAU,KAAgC;IACxD,IAAI,CAAC,SAAS,CAAC,MAAM,QAAQ,CAAC,MAAM;QAClC,OAAO,iBAAiB,qCAAqC;IAC/D;IACA,MAAM,QAAQ,MAAM,KAAK,CAAC;IAC1B,MAAM,WAAW,KAAK,CAAC,EAAE;IACzB,MAAM,SAAS,KAAK,CAAC,EAAE;IAEvB,IAAI,SAAS,MAAM,IAAI,KAAK,OAAO,MAAM,IAAI,KAAK,CAAC,OAAO,QAAQ,CAAC,MAAM;QACvE,OAAO,gBAAgB,4CAA4C;IACrE;IAEA,MAAM,iBACJ,SAAS,SAAS,CAAC,GAAG,KAAK,IAAI,MAAM,CAAC,SAAS,MAAM,GAAG;IAE1D,MAAM,cAAc,OAAO,KAAK,CAAC;IACjC,MAAM,aAAa,WAAW,CAAC,EAAE;IACjC,MAAM,YAAY,YAAY,KAAK,CAAC,GAAG,IAAI,CAAC,MAAM,oCAAoC;IAEtF,MAAM,mBACJ,WAAW,SAAS,CAAC,GAAG,KAAK,IAAI,MAAM,CAAC,WAAW,MAAM,GAAG;IAE9D,OAAO,GAAG,eAAe,CAAC,EAAE,iBAAiB,CAAC,EAAE,WAAW;AAC7D;AAeO,SAAS,wBAAwB,GAAW;IACjD,IAAI,QAAQ,QAAQ,QAAQ,aAAa,MAAM,MAAM,OAAO;IAC5D,MAAM,SAAS,KAAK,GAAG,CAAC;IAExB,gCAAgC;IAChC,MAAM,QAAQ;QACZ;YAAE,OAAO;YAAK,QAAQ;QAAI;QAC1B;YAAE,OAAO;YAAK,QAAQ;QAAK;QAC3B;YAAE,OAAO;YAAK,QAAQ;QAAK;QAC3B;YAAE,OAAO;YAAM,QAAQ;QAAO;QAC9B;YAAE,OAAO;YAAM,QAAQ;QAAO;QAC9B;YAAE,OAAO;YAAM,QAAQ;QAAQ;QAC/B;YAAE,OAAO;YAAM,QAAQ;QAAS;KACjC;IAED,oDAAoD;IACpD,IAAI,SAAS,KAAK;QAChB,IAAI,UAAU,KAAK;YACjB,OAAO,CAAC,MAAM,GAAG,EAAE,OAAO,CAAC,GAAG,OAAO,CAAC,QAAQ,MAAM;QACtD;QACA,OAAO,IAAI,QAAQ;IACrB;IAEA,kCAAkC;IAClC,IAAK,IAAI,IAAI,MAAM,MAAM,GAAG,GAAG,KAAK,GAAG,IAAK;QAC1C,IAAI,UAAU,KAAK,CAAC,EAAE,CAAC,KAAK,EAAE;YAC5B,OACE,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC,KAAK,EAAE,OAAO,CAAC,GAAG,OAAO,CAAC,QAAQ,MAAM,KAAK,CAAC,EAAE,CAAC,MAAM;QAE3E;IACF;IAEA,mCAAmC;IACnC,OAAO,IAAI,QAAQ;AACrB;AAKO,SAAS,cAAc,IAAsB;IAClD,MAAM,eAAe;QACnB,KAAK,YAAY;QACjB,KAAK,QAAQ;QACb,KAAK,IAAI;QACT,KAAK,KAAK;QACV,KAAK,OAAO;KACb,CAAC,MAAM,CAAC;IAET,OAAO,aAAa,IAAI,CAAC,SAAS;AACpC;AAQO,SAAS,WAAW,IAAU,EAAE,cAAuB,KAAK;IACjE,IAAI,CAAC,QAAQ,CAAC,CAAC,gBAAgB,IAAI,KAAK,MAAM,KAAK,OAAO,KAAK;QAC7D,OAAO;IACT;IAEA,MAAM,UAAsC;QAC1C,MAAM;QACN,OAAO;QACP,KAAK;QACL,UAAU;IACZ;IAEA,IAAI,aAAa;QACf,QAAQ,IAAI,GAAG;QACf,QAAQ,MAAM,GAAG;QACjB,QAAQ,MAAM,GAAG;IACnB;IAEA,OAAO,KAAK,cAAc,CAAC,SAAS;AACtC;AAQO,SAAS,eACd,MAAc,EACd,WAAmB,KAAK;IAExB,IAAI,WAAW,QAAQ,WAAW,aAAa,MAAM,SAAS;QAC5D,OAAO;IACT;IAEA,IAAI;QACF,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;YACpC,OAAO;YACP,UAAU;YACV,uBAAuB;YACvB,uBAAuB;QACzB,GAAG,MAAM,CAAC;IACZ,EAAE,OAAM;QACN,mDAAmD;QACnD,4CAA4C;QAC5C,OAAO,GAAG,SAAS,CAAC,EAAE,OAAO,OAAO,CAAC,IAAI;IAC3C;AACF;AAOO,SAAS,YAAY,IAAY;IACtC,IAAI,CAAC,MAAM,OAAO;IAElB,OAAO,KACJ,WAAW,GACX,OAAO,CAAC,SAAS,CAAC,OAAS,KAAK,WAAW;AAChD", "debugId": null}}, {"offset": {"line": 1831, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/components/ui/alert.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst alertVariants = cva(\r\n  \"relative w-full rounded-lg border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default: \"bg-card text-card-foreground\",\r\n        destructive:\r\n          \"text-destructive bg-card [&>svg]:text-current *:data-[slot=alert-description]:text-destructive/90\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nfunction Alert({\r\n  className,\r\n  variant,\r\n  ...props\r\n}: React.ComponentProps<\"div\"> & VariantProps<typeof alertVariants>) {\r\n  return (\r\n    <div\r\n      data-slot=\"alert\"\r\n      role=\"alert\"\r\n      className={cn(alertVariants({ variant }), className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AlertTitle({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"alert-title\"\r\n      className={cn(\r\n        \"col-start-2 line-clamp-1 min-h-4 font-medium tracking-tight\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AlertDescription({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"alert-description\"\r\n      className={cn(\r\n        \"text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Alert, AlertTitle, AlertDescription }\r\n"], "names": [], "mappings": ";;;;;;AACA;AAEA;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,qOACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,GAAG,OAC8D;IACjE,qBACE,8OAAC;QACC,aAAU;QACV,MAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,+DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,GAAG,OACyB;IAC5B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,kGACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1896, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/components/ui/skeleton.tsx"], "sourcesContent": ["import { cn } from \"@/lib/utils\"\r\n\r\nfunction Skeleton({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"skeleton\"\r\n      className={cn(\"bg-accent animate-pulse rounded-md\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Skeleton }\r\n"], "names": [], "mappings": ";;;;AAAA;;;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAoC;IACpE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1921, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/customer/subscriptions/components/SubscriptionsPageClient.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/app/(dashboard)/dashboard/customer/subscriptions/components/SubscriptionsPageClient.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/app/(dashboard)/dashboard/customer/subscriptions/components/SubscriptionsPageClient.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA2V,GACxX,yHACA", "debugId": null}}, {"offset": {"line": 1935, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/customer/subscriptions/components/SubscriptionsPageClient.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/app/(dashboard)/dashboard/customer/subscriptions/components/SubscriptionsPageClient.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/app/(dashboard)/dashboard/customer/subscriptions/components/SubscriptionsPageClient.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAuU,GACpW,qGACA", "debugId": null}}, {"offset": {"line": 1949, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 1959, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/components/shared/subscriptions/SubscriptionCard.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/app/components/shared/subscriptions/SubscriptionCard.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/app/components/shared/subscriptions/SubscriptionCard.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA4T,GACzV,0FACA", "debugId": null}}, {"offset": {"line": 1973, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/components/shared/subscriptions/SubscriptionCard.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/app/components/shared/subscriptions/SubscriptionCard.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/app/components/shared/subscriptions/SubscriptionCard.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAwS,GACrU,sEACA", "debugId": null}}, {"offset": {"line": 1987, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 1997, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/components/shared/subscriptions/SubscriptionCardSkeleton.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const SubscriptionListSkeleton = registerClientReference(\n    function() { throw new Error(\"Attempted to call SubscriptionListSkeleton() from the server but SubscriptionListSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/app/components/shared/subscriptions/SubscriptionCardSkeleton.tsx <module evaluation>\",\n    \"SubscriptionListSkeleton\",\n);\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/app/components/shared/subscriptions/SubscriptionCardSkeleton.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/app/components/shared/subscriptions/SubscriptionCardSkeleton.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,2BAA2B,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC1D;IAAa,MAAM,IAAI,MAAM;AAAgQ,GAC7R,kGACA;uCAEW,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAoU,GACjW,kGACA", "debugId": null}}, {"offset": {"line": 2015, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/components/shared/subscriptions/SubscriptionCardSkeleton.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const SubscriptionListSkeleton = registerClientReference(\n    function() { throw new Error(\"Attempted to call SubscriptionListSkeleton() from the server but SubscriptionListSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/app/components/shared/subscriptions/SubscriptionCardSkeleton.tsx\",\n    \"SubscriptionListSkeleton\",\n);\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/app/components/shared/subscriptions/SubscriptionCardSkeleton.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/app/components/shared/subscriptions/SubscriptionCardSkeleton.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,2BAA2B,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC1D;IAAa,MAAM,IAAI,MAAM;AAAgQ,GAC7R,8EACA;uCAEW,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAgT,GAC7U,8EACA", "debugId": null}}, {"offset": {"line": 2033, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 2043, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/components/shared/subscriptions/SubscriptionSearch.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/app/components/shared/subscriptions/SubscriptionSearch.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/app/components/shared/subscriptions/SubscriptionSearch.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA8T,GAC3V,4FACA", "debugId": null}}, {"offset": {"line": 2057, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/components/shared/subscriptions/SubscriptionSearch.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/app/components/shared/subscriptions/SubscriptionSearch.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/app/components/shared/subscriptions/SubscriptionSearch.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA0S,GACvU,wEACA", "debugId": null}}, {"offset": {"line": 2071, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 2081, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/components/shared/subscriptions/SubscriptionPagination.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/app/components/shared/subscriptions/SubscriptionPagination.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/app/components/shared/subscriptions/SubscriptionPagination.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAkU,GAC/V,gGACA", "debugId": null}}, {"offset": {"line": 2095, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/components/shared/subscriptions/SubscriptionPagination.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/app/components/shared/subscriptions/SubscriptionPagination.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/app/components/shared/subscriptions/SubscriptionPagination.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA8S,GAC3U,4EACA", "debugId": null}}, {"offset": {"line": 2109, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 2119, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/components/shared/subscriptions/SubscriptionList.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/app/components/shared/subscriptions/SubscriptionList.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/app/components/shared/subscriptions/SubscriptionList.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA4T,GACzV,0FACA", "debugId": null}}, {"offset": {"line": 2133, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/components/shared/subscriptions/SubscriptionList.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/app/components/shared/subscriptions/SubscriptionList.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/app/components/shared/subscriptions/SubscriptionList.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAwS,GACrU,sEACA", "debugId": null}}, {"offset": {"line": 2147, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 2157, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/components/shared/subscriptions/index.ts"], "sourcesContent": ["// Export all shared subscription components\r\nexport { default as SubscriptionCard } from './SubscriptionCard';\r\nexport { default as SubscriptionCardSkeleton, SubscriptionListSkeleton } from './SubscriptionCardSkeleton';\r\nexport { default as SubscriptionSearch } from './SubscriptionSearch';\r\nexport { default as SubscriptionPagination } from './SubscriptionPagination';\r\nexport { default as SubscriptionList } from './SubscriptionList';\r\n\r\n// Export types\r\nexport type { ProfileData, SubscriptionData } from './SubscriptionCard';\r\n"], "names": [], "mappings": "AAAA,4CAA4C;;AAC5C;AACA;AACA;AACA;AACA", "debugId": null}}, {"offset": {"line": 2188, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/customer/subscriptions/page.tsx"], "sourcesContent": ["import { createClient } from '@/utils/supabase/server';\r\nimport { redirect } from 'next/navigation';\r\nimport { Metadata } from 'next';\r\nimport { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';\r\n\r\nimport { Skeleton } from '@/components/ui/skeleton';\r\nimport { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON> } from 'lucide-react';\r\nimport SubscriptionsPageClient from './components/SubscriptionsPageClient';\r\nimport { Suspense } from 'react';\r\nimport { SubscriptionListSkeleton } from '@/app/components/shared/subscriptions';\r\nimport { requireCompleteProfile } from '@/lib/actions/customerProfiles/addressValidation';\r\n\r\n// Import the fetchSubscriptions function\r\nimport { fetchSubscriptions } from './actions';\r\n\r\nexport const metadata: Metadata = {\r\n  title: \"My Subscriptions - Dukancard\",\r\n  robots: \"noindex, nofollow\",\r\n};\r\n\r\nexport default async function CustomerSubscriptionsPage({\r\n  searchParams\r\n}: {\r\n  searchParams: Promise<{ search?: string; page?: string }>\r\n}) {\r\n  // Properly await searchParams to fix the error\r\n  const { search, page: pageParam } = await searchParams;\r\n  const supabase = await createClient();\r\n  const page = pageParam ? parseInt(pageParam) : 1;\r\n  const searchTerm = search || \"\";\r\n\r\n  const {\r\n    data: { user },\r\n    error: authError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (authError || !user) {\r\n    redirect('/login?message=Please log in to view your subscriptions.');\r\n  }\r\n\r\n  // Check if customer has complete address\r\n  await requireCompleteProfile(user.id);\r\n\r\n  try {\r\n    // Fetch subscriptions with pagination and search\r\n    const subscriptionsResult = await fetchSubscriptions(\r\n      user.id,\r\n      page,\r\n      12, // 12 items per page - optimized for 3-column grid\r\n      searchTerm\r\n    );\r\n\r\n    // Full width layout without card wrapper\r\n    return (\r\n      <div className=\"space-y-6\">\r\n        <Suspense fallback={\r\n          <div className=\"space-y-6\">\r\n            {/* Header Section with proper layout */}\r\n            <div className=\"flex flex-col gap-6\">\r\n              <div className=\"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4\">\r\n                <div className=\"flex items-center gap-4\">\r\n                  <div className=\"p-3 rounded-xl bg-muted hidden sm:block\">\r\n                    <Bell className=\"w-6 h-6 text-blue-600 dark:text-blue-400\" />\r\n                  </div>\r\n                  <div>\r\n                    <h1 className=\"text-2xl font-bold text-foreground\">\r\n                      Subscribed Businesses\r\n                    </h1>\r\n                    <p className=\"text-muted-foreground mt-1\">\r\n                      Businesses you&apos;re following for updates\r\n                    </p>\r\n                  </div>\r\n                </div>\r\n\r\n                {/* Search skeleton - aligned to the right on desktop */}\r\n                <div className=\"w-full sm:w-80\">\r\n                  <Skeleton className=\"h-10 w-full rounded-md\" />\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            {/* Content skeleton */}\r\n            <SubscriptionListSkeleton />\r\n          </div>\r\n        }>\r\n          <SubscriptionsPageClient\r\n            initialSubscriptions={subscriptionsResult.items}\r\n            totalCount={subscriptionsResult.totalCount}\r\n            currentPage={subscriptionsResult.currentPage}\r\n            searchTerm={searchTerm}\r\n          />\r\n        </Suspense>\r\n      </div>\r\n    );\r\n  } catch (_error) {\r\n    return (\r\n      <Alert variant=\"destructive\">\r\n        <AlertTriangle className=\"h-4 w-4\" />\r\n        <AlertTitle>Error</AlertTitle>\r\n        <AlertDescription>\r\n          Could not load your subscriptions. Please try again later.\r\n        </AlertDescription>\r\n      </Alert>\r\n    );\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AAAA;AAEA;AAEA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AAEA,yCAAyC;AACzC;;;;;;;;;;;;AAEO,MAAM,WAAqB;IAChC,OAAO;IACP,QAAQ;AACV;AAEe,eAAe,0BAA0B,EACtD,YAAY,EAGb;IACC,+CAA+C;IAC/C,MAAM,EAAE,MAAM,EAAE,MAAM,SAAS,EAAE,GAAG,MAAM;IAC1C,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD;IAClC,MAAM,OAAO,YAAY,SAAS,aAAa;IAC/C,MAAM,aAAa,UAAU;IAE7B,MAAM,EACJ,MAAM,EAAE,IAAI,EAAE,EACd,OAAO,SAAS,EACjB,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;IAE/B,IAAI,aAAa,CAAC,MAAM;QACtB,CAAA,GAAA,qLAAA,CAAA,WAAQ,AAAD,EAAE;IACX;IAEA,yCAAyC;IACzC,MAAM,CAAA,GAAA,uJAAA,CAAA,yBAAsB,AAAD,EAAE,KAAK,EAAE;IAEpC,IAAI;QACF,iDAAiD;QACjD,MAAM,sBAAsB,MAAM,CAAA,GAAA,yKAAA,CAAA,qBAAkB,AAAD,EACjD,KAAK,EAAE,EACP,MACA,IACA;QAGF,yCAAyC;QACzC,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC,qMAAA,CAAA,WAAQ;gBAAC,wBACR,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;;;;;;0DAElB,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAAqC;;;;;;kEAGnD,8OAAC;wDAAE,WAAU;kEAA6B;;;;;;;;;;;;;;;;;;kDAO9C,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,6HAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;sCAM1B,8OAAC,yKAAA,CAAA,2BAAwB;;;;;;;;;;;0BAG3B,cAAA,8OAAC,wMAAA,CAAA,UAAuB;oBACtB,sBAAsB,oBAAoB,KAAK;oBAC/C,YAAY,oBAAoB,UAAU;oBAC1C,aAAa,oBAAoB,WAAW;oBAC5C,YAAY;;;;;;;;;;;;;;;;IAKtB,EAAE,OAAO,QAAQ;QACf,qBACE,8OAAC,0HAAA,CAAA,QAAK;YAAC,SAAQ;;8BACb,8OAAC,wNAAA,CAAA,gBAAa;oBAAC,WAAU;;;;;;8BACzB,8OAAC,0HAAA,CAAA,aAAU;8BAAC;;;;;;8BACZ,8OAAC,0HAAA,CAAA,mBAAgB;8BAAC;;;;;;;;;;;;IAKxB;AACF", "debugId": null}}]}