{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_9dd07395._.js", "server/edge/chunks/node_modules_@supabase_auth-js_dist_module_17bbb6b5._.js", "server/edge/chunks/node_modules_@upstash_redis_b3b75fae._.js", "server/edge/chunks/node_modules_a5b8fa46._.js", "server/edge/chunks/[root-of-the-server]__c2258e89._.js", "server/edge/chunks/edge-wrapper_3918d6b0.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*){(\\\\.json)}?", "originalSource": "/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "LQe1NbuqwtgvamA3sJfyGyki6D/xlrCTDJmYSjuVnIM=", "__NEXT_PREVIEW_MODE_ID": "bd2e9cf69745d380eba9f10874e3a5a5", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "8cc4027a5bc5819e1dc1ef0b05d7c6da013c6f77d8bd64956178121f139c270b", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "2b0b63ac47277b96297f18aa8ddf54958937b23e50de01881352770c29108156"}}}, "instrumentation": null, "functions": {}}