---
Status: Draft
Story: |
  As a developer, I need to implement comprehensive unit tests for the `actions.ts` file within the `dukancard` project's customer subscriptions module. This file contains the server action `fetchSubscriptions`, which is responsible for fetching subscription data from the `subscriptionsService`. The tests should ensure this action correctly interacts with the `subscriptionsService`, handles various data scenarios (including pagination and search), and gracefully manages service-level errors.

Acceptance Criteria:
- **`fetchSubscriptions` Functionality:**
    - Given a valid `userId`, `page`, `limit`, and `searchTerm`, when `fetchSubscriptions` is called, then `subscriptionsService.fetchSubscriptions` is invoked with the correct parameters.
    - Given `subscriptionsService.fetchSubscriptions` returns data, then `fetchSubscriptions` returns the data correctly, including `items`, `totalCount`, `hasMore`, and `currentPage`.
    - Given `subscriptionsService.fetchSubscriptions` returns an empty array, then `fetchSubscriptions` returns an empty `items` array, `totalCount` of 0, `hasMore` as `false`, and `currentPage` as the requested page.
    - Given `subscriptionsService.fetchSubscriptions` throws an error, then `fetchSubscriptions` catches the error and re-throws it.
    - Given `searchTerm` is provided, then `subscriptionsService.fetchSubscriptions` is called with the correct `searchTerm`.
    - Given `page` and `limit` are provided, then `subscriptionsService.fetchSubscriptions` is called with the correct pagination parameters.

Tasks:
1. Create a new test file: `C:\web-app\dukancard\__tests__\app\(dashboard)\dashboard\customer\subscriptions\actions.test.ts`.
2. Set up a testing environment that can mock `subscriptionsService`.
3. Write unit tests for `fetchSubscriptions` covering:
    - Successful data retrieval.
    - Error handling when `subscriptionsService.fetchSubscriptions` throws an error.
    - Correct parameter passing (userId, page, limit, searchTerm).
    - Empty data responses from `subscriptionsService`.

File: `C:\web-app\dukancard\app\(dashboard)\dashboard\customer\subscriptions\actions.ts`
Platform: dukancard
---