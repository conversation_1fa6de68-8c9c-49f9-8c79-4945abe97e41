{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/utils/client-image-compression.ts"], "sourcesContent": ["/**\r\n * Client-side image compression using Canvas API\r\n * This avoids memory issues in serverless environments like Google Cloud Run\r\n */\r\n\r\nexport interface CompressionOptions {\r\n  targetSizeKB?: number;\r\n  maxDimension?: number;\r\n  quality?: number;\r\n  format?: \"webp\" | \"jpeg\" | \"png\";\r\n}\r\n\r\nexport interface CompressionResult {\r\n  blob: Blob;\r\n  finalSizeKB: number;\r\n  compressionRatio: number;\r\n  dimensions: { width: number; height: number };\r\n}\r\n\r\n/**\r\n * Compress image on client-side using Canvas API\r\n */\r\nexport async function compressImageClientSide(\r\n  file: File,\r\n  options: CompressionOptions = {}\r\n): Promise<CompressionResult> {\r\n  const {\r\n    format = \"webp\",\r\n    targetSizeKB = 100,\r\n    maxDimension = 800,\r\n    quality: initialQuality = 0.8,\r\n  } = options;\r\n\r\n  return new Promise((resolve, reject) => {\r\n    const img = new Image();\r\n    img.onload = () => {\r\n      try {\r\n        const canvas = document.createElement(\"canvas\");\r\n        const ctx = canvas.getContext(\"2d\");\r\n\r\n        if (!ctx) {\r\n          reject(new Error(\"Could not get canvas context\"));\r\n          return;\r\n        }\r\n\r\n        // Calculate new dimensions\r\n        let { width, height } = img;\r\n\r\n        if (width > maxDimension || height > maxDimension) {\r\n          if (width > height) {\r\n            height = (height * maxDimension) / width;\r\n            width = maxDimension;\r\n          } else {\r\n            width = (width * maxDimension) / height;\r\n            height = maxDimension;\r\n          }\r\n        }\r\n\r\n        canvas.width = width;\r\n        canvas.height = height;\r\n\r\n        // Draw and compress\r\n        ctx.drawImage(img, 0, 0, width, height);\r\n\r\n        // Try different quality levels until we hit target size\r\n        let quality = initialQuality;\r\n        let attempts = 0;\r\n        const maxAttempts = 5;\r\n\r\n        const tryCompress = () => {\r\n          canvas.toBlob(\r\n            (blob) => {\r\n              if (!blob) {\r\n                reject(new Error(\"Failed to create blob\"));\r\n                return;\r\n              }\r\n\r\n              const sizeKB = blob.size / 1024;\r\n\r\n              if (\r\n                sizeKB <= targetSizeKB ||\r\n                attempts >= maxAttempts ||\r\n                quality <= 0.1\r\n              ) {\r\n                // Success or max attempts reached\r\n                const compressionRatio = file.size / blob.size;\r\n                resolve({\r\n                  blob,\r\n                  finalSizeKB: Math.round(sizeKB * 100) / 100,\r\n                  compressionRatio: Math.round(compressionRatio * 100) / 100,\r\n                  dimensions: { width, height },\r\n                });\r\n              } else {\r\n                // Try again with lower quality\r\n                attempts++;\r\n                quality = Math.max(0.1, quality - 0.15);\r\n                tryCompress();\r\n              }\r\n            },\r\n            `image/${format}`,\r\n            quality\r\n          );\r\n        };\r\n\r\n        tryCompress();\r\n      } catch (error) {\r\n        reject(error);\r\n      }\r\n    };\r\n\r\n    img.onerror = () => reject(new Error(\"Failed to load image\"));\r\n    img.src = URL.createObjectURL(file);\r\n  });\r\n}\r\n\r\n/**\r\n * Ultra-aggressive client-side compression\r\n */\r\nexport async function compressImageUltraAggressiveClient(\r\n  file: File,\r\n  options: CompressionOptions = {}\r\n): Promise<CompressionResult> {\r\n  const originalSizeMB = file.size / (1024 * 1024);\r\n\r\n  // Auto-determine settings based on file size\r\n  let targetSizeKB = 100;\r\n  let maxDimension = 800;\r\n  let quality = 0.7;\r\n\r\n  if (originalSizeMB <= 2) {\r\n    quality = 0.7;\r\n    maxDimension = 800;\r\n    targetSizeKB = 90;\r\n  } else if (originalSizeMB <= 5) {\r\n    quality = 0.55;\r\n    maxDimension = 700;\r\n    targetSizeKB = 80;\r\n  } else if (originalSizeMB <= 10) {\r\n    quality = 0.45;\r\n    maxDimension = 600;\r\n    targetSizeKB = 70;\r\n  } else {\r\n    quality = 0.35;\r\n    maxDimension = 550;\r\n    targetSizeKB = 60;\r\n  }\r\n\r\n  return compressImageClientSide(file, {\r\n    ...options,\r\n    targetSizeKB: options.targetSizeKB || targetSizeKB,\r\n    maxDimension: options.maxDimension || maxDimension,\r\n    quality: options.quality || quality,\r\n  });\r\n}\r\n\r\n/**\r\n * Moderate client-side compression\r\n * Default targets 50KB for avatars, can be overridden via options\r\n */\r\nexport async function compressImageModerateClient(\r\n  file: File,\r\n  options: CompressionOptions = {}\r\n): Promise<CompressionResult> {\r\n  return compressImageClientSide(file, {\r\n    targetSizeKB: 50, // Default to 50KB for avatars\r\n    maxDimension: 400, // Smaller default for avatars\r\n    quality: 0.7, // More aggressive default quality\r\n    ...options,\r\n  });\r\n}\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;AAmBM,eAAe,wBACpB,IAAU,EACV,UAA8B,CAAC,CAAC;IAEhC,MAAM,EACJ,SAAS,MAAM,EACf,eAAe,GAAG,EAClB,eAAe,GAAG,EAClB,SAAS,iBAAiB,GAAG,EAC9B,GAAG;IAEJ,OAAO,IAAI,QAAQ,CAAC,SAAS;QAC3B,MAAM,MAAM,IAAI;QAChB,IAAI,MAAM,GAAG;YACX,IAAI;gBACF,MAAM,SAAS,SAAS,aAAa,CAAC;gBACtC,MAAM,MAAM,OAAO,UAAU,CAAC;gBAE9B,IAAI,CAAC,KAAK;oBACR,OAAO,IAAI,MAAM;oBACjB;gBACF;gBAEA,2BAA2B;gBAC3B,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG;gBAExB,IAAI,QAAQ,gBAAgB,SAAS,cAAc;oBACjD,IAAI,QAAQ,QAAQ;wBAClB,SAAS,AAAC,SAAS,eAAgB;wBACnC,QAAQ;oBACV,OAAO;wBACL,QAAQ,AAAC,QAAQ,eAAgB;wBACjC,SAAS;oBACX;gBACF;gBAEA,OAAO,KAAK,GAAG;gBACf,OAAO,MAAM,GAAG;gBAEhB,oBAAoB;gBACpB,IAAI,SAAS,CAAC,KAAK,GAAG,GAAG,OAAO;gBAEhC,wDAAwD;gBACxD,IAAI,UAAU;gBACd,IAAI,WAAW;gBACf,MAAM,cAAc;gBAEpB,MAAM,cAAc;oBAClB,OAAO,MAAM,CACX,CAAC;wBACC,IAAI,CAAC,MAAM;4BACT,OAAO,IAAI,MAAM;4BACjB;wBACF;wBAEA,MAAM,SAAS,KAAK,IAAI,GAAG;wBAE3B,IACE,UAAU,gBACV,YAAY,eACZ,WAAW,KACX;4BACA,kCAAkC;4BAClC,MAAM,mBAAmB,KAAK,IAAI,GAAG,KAAK,IAAI;4BAC9C,QAAQ;gCACN;gCACA,aAAa,KAAK,KAAK,CAAC,SAAS,OAAO;gCACxC,kBAAkB,KAAK,KAAK,CAAC,mBAAmB,OAAO;gCACvD,YAAY;oCAAE;oCAAO;gCAAO;4BAC9B;wBACF,OAAO;4BACL,+BAA+B;4BAC/B;4BACA,UAAU,KAAK,GAAG,CAAC,KAAK,UAAU;4BAClC;wBACF;oBACF,GACA,CAAC,MAAM,EAAE,QAAQ,EACjB;gBAEJ;gBAEA;YACF,EAAE,OAAO,OAAO;gBACd,OAAO;YACT;QACF;QAEA,IAAI,OAAO,GAAG,IAAM,OAAO,IAAI,MAAM;QACrC,IAAI,GAAG,GAAG,IAAI,eAAe,CAAC;IAChC;AACF;AAKO,eAAe,mCACpB,IAAU,EACV,UAA8B,CAAC,CAAC;IAEhC,MAAM,iBAAiB,KAAK,IAAI,GAAG,CAAC,OAAO,IAAI;IAE/C,6CAA6C;IAC7C,IAAI,eAAe;IACnB,IAAI,eAAe;IACnB,IAAI,UAAU;IAEd,IAAI,kBAAkB,GAAG;QACvB,UAAU;QACV,eAAe;QACf,eAAe;IACjB,OAAO,IAAI,kBAAkB,GAAG;QAC9B,UAAU;QACV,eAAe;QACf,eAAe;IACjB,OAAO,IAAI,kBAAkB,IAAI;QAC/B,UAAU;QACV,eAAe;QACf,eAAe;IACjB,OAAO;QACL,UAAU;QACV,eAAe;QACf,eAAe;IACjB;IAEA,OAAO,wBAAwB,MAAM;QACnC,GAAG,OAAO;QACV,cAAc,QAAQ,YAAY,IAAI;QACtC,cAAc,QAAQ,YAAY,IAAI;QACtC,SAAS,QAAQ,OAAO,IAAI;IAC9B;AACF;AAMO,eAAe,4BACpB,IAAU,EACV,UAA8B,CAAC,CAAC;IAEhC,OAAO,wBAAwB,MAAM;QACnC,cAAc;QACd,cAAc;QACd,SAAS;QACT,GAAG,OAAO;IACZ;AACF", "debugId": null}}]}