module.exports = {

"[project]/app/auth/actions.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ [{"0027aa970a57d5cbd11af21933b24fca73bbd99631":"signOutUser"},"",""] */ __turbopack_context__.s({
    "signOutUser": (()=>signOutUser)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/server-reference.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$app$2d$render$2f$encryption$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/app-render/encryption.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/supabase/server.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$api$2f$navigation$2e$react$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/next/dist/api/navigation.react-server.js [app-rsc] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$components$2f$navigation$2e$react$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/client/components/navigation.react-server.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-validate.js [app-rsc] (ecmascript)");
;
;
;
;
async function signOutUser() {
    const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])();
    try {
        const { error: _error } = await supabase.auth.signOut();
        // Note: Sign out errors are typically not critical for user experience
        // The user will be redirected to login regardless
        // Explicitly clear auth cookies to ensure logout
        const cookieStore = await __turbopack_context__.r("[project]/node_modules/next/headers.js [app-rsc] (ecmascript, async loader)")(__turbopack_context__.i).then((m)=>m.cookies());
        const cookiesToClear = [
            "sb-access-token",
            "sb-refresh-token"
        ];
        for (const cookieName of cookiesToClear){
            try {
                cookieStore.set(cookieName, "", {
                    expires: new Date(0),
                    maxAge: -1
                });
            } catch  {
            // Cookie clearing errors are not critical for sign out
            // Continue with the sign out process
            }
        }
    } catch  {
    // Even if sign out fails, redirect to login for security
    // User will be treated as logged out
    }
    // Redirect to login with a flag to prevent middleware redirect loop
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$components$2f$navigation$2e$react$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["redirect"])("/login?logged_out=true");
}
;
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ensureServerEntryExports"])([
    signOutUser
]);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(signOutUser, "0027aa970a57d5cbd11af21933b24fca73bbd99631", null);
}}),
"[project]/lib/supabase/constants.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// lib/supabase/constants.ts
__turbopack_context__.s({
    "BUCKETS": (()=>BUCKETS),
    "COLUMNS": (()=>COLUMNS),
    "TABLES": (()=>TABLES)
});
const TABLES = {
    BLOGS: "blogs",
    BUSINESS_ACTIVITIES: "business_activities",
    BUSINESS_PROFILES: "business_profiles",
    CARD_VISITS: "card_visits",
    CUSTOMER_POSTS: "customer_posts",
    CUSTOMER_PROFILES: "customer_profiles",
    LIKES: "likes",
    PAYMENT_SUBSCRIPTIONS: "payment_subscriptions",
    PINCODES: "pincodes",
    PRODUCTS_SERVICES: "products_services",
    PRODUCT_VARIANTS: "product_variants",
    STORAGE_CLEANUP_CONFIG: "storage_cleanup_config",
    STORAGE_CLEANUP_PROGRESS: "storage_cleanup_progress",
    SUBSCRIPTIONS: "subscriptions",
    SYSTEM_ALERTS: "system_alerts",
    RATINGS_REVIEWS: "ratings_reviews"
};
const BUCKETS = {
    BUSINESS: "business",
    CUSTOMERS: "customers"
};
const COLUMNS = {
    ID: "id",
    CREATED_AT: "created_at",
    UPDATED_AT: "updated_at",
    NAME: "name",
    EMAIL: "email",
    PHONE: "phone",
    CITY: "city",
    STATE: "state",
    PINCODE: "pincode",
    PLAN_ID: "plan_id",
    LOCALITY: "locality",
    CITY_SLUG: "city_slug",
    STATE_SLUG: "state_slug",
    LOCALITY_SLUG: "locality_slug",
    LOGO_URL: "logo_url",
    IMAGE_URL: "image_url",
    IMAGES: "images",
    SLUG: "slug",
    STATUS: "status",
    CONTENT: "content",
    GALLERY: "gallery",
    DESCRIPTION: "description",
    TITLE: "title",
    USER_ID: "user_id",
    BUSINESS_ID: "business_id",
    BUSINESS_NAME: "business_name",
    BUSINESS_SLUG: "business_slug",
    PRODUCT_ID: "product_id",
    PRODUCT_TYPE: "product_type",
    BUSINESS_PROFILE_ID: "business_profile_id",
    RAZORPAY_SUBSCRIPTION_ID: "razorpay_subscription_id",
    SUBSCRIPTION_STATUS: "subscription_status",
    RATING: "rating",
    REVIEW_TEXT: "review_text",
    AVATAR_URL: "avatar_url",
    ADDRESS_LINE: "address_line"
};
}}),
"[project]/lib/services/socialService.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Unified Social Service for Next.js
 * Handles subscriptions, likes, and reviews data fetching and management
 * Supports both customer and business contexts with identical backend functionality to React Native
 */ __turbopack_context__.s({
    "getActivityMetrics": (()=>getActivityMetrics),
    "likesService": (()=>likesService),
    "reviewsService": (()=>reviewsService),
    "socialService": (()=>socialService),
    "subscriptionsService": (()=>subscriptionsService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/supabase/server.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/supabase/constants.ts [app-rsc] (ecmascript)");
;
;
const subscriptionsService = {
    /**
   * Fetch user subscriptions with pagination and search
   */ async fetchSubscriptions (userId, page = 1, limit = 20, searchTerm = "") {
        try {
            const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])();
            // Get total count first with separate query to avoid count issues with joins
            let countQuery = supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].SUBSCRIPTIONS).select(`
          ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID},
          ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].BUSINESS_PROFILES}!inner (
            ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID},
            ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_NAME}
          )
        `, {
                count: "exact",
                head: true
            }).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].USER_ID, userId);
            // Apply search filter to count query if provided
            if (searchTerm && searchTerm.trim()) {
                countQuery = countQuery.ilike(`${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].BUSINESS_PROFILES}.${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_NAME}`, `%${searchTerm.trim()}%`);
            }
            const { count: totalCount, error: countError } = await countQuery;
            if (countError) {
                throw new Error(`Failed to get subscriptions count: ${countError.message}`);
            }
            // If no subscriptions, return empty result
            if (!totalCount || totalCount === 0) {
                return {
                    items: [],
                    totalCount: 0,
                    hasMore: false,
                    currentPage: page
                };
            }
            // Build the main query for fetching data
            let query = supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].SUBSCRIPTIONS).select(`
          ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID},
          ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_PROFILE_ID},
          ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].BUSINESS_PROFILES}!inner (*)
        `).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].USER_ID, userId);
            // Apply search filter if provided
            if (searchTerm && searchTerm.trim()) {
                query = query.ilike(`${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].BUSINESS_PROFILES}.${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_NAME}`, `%${searchTerm.trim()}%`);
            }
            // Apply pagination
            const offset = (page - 1) * limit;
            console.log(`[subscriptionsService.fetchSubscriptions] Applying pagination: offset=${offset}, limit=${limit}`);
            query = query.range(offset, offset + limit - 1);
            const { data: subscriptionsWithProfiles, error } = await query;
            console.log(`[subscriptionsService.fetchSubscriptions] Query returned ${subscriptionsWithProfiles?.length} items`);
            if (error) {
                throw new Error(`Failed to fetch subscriptions: ${error.message}`);
            }
            // Transform the data
            const transformedSubscriptions = (subscriptionsWithProfiles || []).map((sub)=>({
                    id: sub.id,
                    business_profiles: Array.isArray(sub.business_profiles) ? sub.business_profiles[0] : sub.business_profiles
                }));
            const hasMore = totalCount > offset + limit;
            return {
                items: transformedSubscriptions,
                totalCount,
                hasMore,
                currentPage: page
            };
        } catch (error) {
            console.error("Error in fetchSubscriptions:", error);
            throw error;
        }
    },
    /**
   * Unsubscribe from a business
   */ async unsubscribe (subscriptionId) {
        try {
            const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])();
            const { error } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].SUBSCRIPTIONS).delete().eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID, subscriptionId);
            if (error) {
                throw new Error(`Failed to unsubscribe: ${error.message}`);
            }
        } catch (error) {
            console.error("Error in unsubscribe:", error);
            throw error;
        }
    },
    /**
   * Fetch followers to a business (both customers and other businesses)
   */ async fetchBusinessFollowers (businessId, page = 1, limit = 10) {
        try {
            const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])();
            // Calculate offset for pagination
            const offset = (page - 1) * limit;
            // Get total count first
            const { count: totalCount, error: countError } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].SUBSCRIPTIONS).select("*", {
                count: "exact",
                head: true
            }).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_PROFILE_ID, businessId);
            if (countError) {
                throw new Error("Failed to fetch subscription count");
            }
            if (!totalCount || totalCount === 0) {
                return {
                    items: [],
                    totalCount: 0,
                    hasMore: false,
                    currentPage: page
                };
            }
            // Get paginated subscriptions (database-level pagination)
            const { data: subscriptions, error: subsError } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].SUBSCRIPTIONS).select(`${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].USER_ID}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].CREATED_AT}`).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_PROFILE_ID, businessId).order(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].CREATED_AT, {
                ascending: false
            }).range(offset, offset + limit - 1);
            if (subsError) {
                throw new Error("Failed to fetch subscriptions");
            }
            if (!subscriptions || subscriptions.length === 0) {
                return {
                    items: [],
                    totalCount: totalCount || 0,
                    hasMore: false,
                    currentPage: page
                };
            }
            // Get user IDs from the paginated subscriptions only
            const userIds = subscriptions.map((sub)=>sub.user_id);
            // Fetch profiles for only the paginated user IDs (not all users)
            const [customerProfiles, businessProfiles] = await Promise.all([
                supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].CUSTOMER_PROFILES).select(`${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].NAME}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].AVATAR_URL}`).in(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID, userIds),
                supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].BUSINESS_PROFILES).select(`${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_NAME}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_SLUG}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].LOGO_URL}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].CITY}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].STATE}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].PINCODE}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ADDRESS_LINE}`).in(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID, userIds)
            ]);
            if (customerProfiles.error) {
                throw new Error("Failed to fetch customer profiles");
            }
            if (businessProfiles.error) {
                throw new Error("Failed to fetch business profiles");
            }
            // Create profile maps for efficient lookup
            const customerMap = new Map(customerProfiles.data?.map((p)=>[
                    p.id,
                    p
                ]) || []);
            const businessMap = new Map(businessProfiles.data?.map((p)=>[
                    p.id,
                    p
                ]) || []);
            // Transform subscriptions to include profile data
            const allFollowers = subscriptions.map((sub)=>{
                const customerProfile = customerMap.get(sub.user_id);
                const businessProfile = businessMap.get(sub.user_id);
                if (customerProfile) {
                    return {
                        id: sub.id,
                        profile: {
                            id: customerProfile.id,
                            name: customerProfile.name,
                            slug: null,
                            avatar_url: customerProfile.avatar_url,
                            city: null,
                            state: null,
                            pincode: null,
                            address_line: null,
                            type: "customer"
                        }
                    };
                } else if (businessProfile) {
                    return {
                        id: sub.id,
                        profile: {
                            id: businessProfile.id,
                            name: businessProfile.business_name,
                            slug: businessProfile.business_slug,
                            logo_url: businessProfile.logo_url,
                            city: businessProfile.city,
                            state: businessProfile.state,
                            pincode: businessProfile.pincode,
                            address_line: businessProfile.address_line,
                            type: "business"
                        }
                    };
                }
                return null;
            }).filter((sub)=>sub !== null);
            // Calculate hasMore based on database-level pagination
            const hasMore = totalCount > offset + limit;
            return {
                items: allFollowers,
                totalCount: totalCount || 0,
                hasMore,
                currentPage: page
            };
        } catch (error) {
            throw error;
        }
    }
};
const likesService = {
    /**
   * Fetch user likes with pagination and search
   */ async fetchLikes (userId, page = 1, limit = 20, searchTerm = "") {
        try {
            const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])();
            // Build query with proper joins and filtering
            let query = supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].LIKES).select(`
          ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID},
          ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].BUSINESS_PROFILES}!inner (*)
        `).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].USER_ID, userId);
            // Apply search filter if provided
            if (searchTerm && searchTerm.trim()) {
                query = query.ilike(`${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].BUSINESS_PROFILES}.${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_NAME}`, `%${searchTerm.trim()}%`);
            }
            // Get total count for pagination
            let countQuery = supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].LIKES).select(`
          ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID},
          ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].BUSINESS_PROFILES}!inner (
            ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID},
            ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_NAME}
          )
        `, {
                count: "exact",
                head: true
            }).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].USER_ID, userId);
            if (searchTerm && searchTerm.trim()) {
                countQuery = countQuery.ilike(`${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].BUSINESS_PROFILES}.${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_NAME}`, `%${searchTerm.trim()}%`);
            }
            const { count: totalCount, error: countError } = await countQuery;
            if (countError) {
                throw new Error(`Failed to get likes count: ${countError.message}`);
            }
            // If no likes, return empty result
            if (!totalCount || totalCount === 0) {
                return {
                    items: [],
                    totalCount: 0,
                    hasMore: false,
                    currentPage: page
                };
            }
            // Apply pagination to the main query
            const offset = (page - 1) * limit;
            console.log(`[likesService.fetchLikes] Applying pagination: offset=${offset}, limit=${limit}`);
            query = query.range(offset, offset + limit - 1);
            const { data: likesWithProfiles, error } = await query;
            console.log(`[likesService.fetchLikes] Query returned ${likesWithProfiles?.length} items`);
            if (error) {
                throw new Error(`Failed to fetch likes: ${error.message}`);
            }
            // Transform the data
            const transformedLikes = (likesWithProfiles || []).map((like)=>({
                    id: like.id,
                    business_profiles: Array.isArray(like.business_profiles) ? like.business_profiles[0] : like.business_profiles
                }));
            const hasMore = totalCount > offset + limit;
            return {
                items: transformedLikes,
                totalCount,
                hasMore,
                currentPage: page
            };
        } catch (error) {
            console.error("Error in fetchLikes:", error);
            throw error;
        }
    },
    /**
   * Unlike a business
   */ async unlike (likeId) {
        try {
            const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])();
            const { error } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].LIKES).delete().eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID, likeId);
            if (error) {
                throw new Error(`Failed to unlike: ${error.message}`);
            }
        } catch (error) {
            console.error("Error in unlike:", error);
            throw error;
        }
    },
    /**
   * Fetch likes received by a business (customers/businesses who liked this business)
   */ async fetchBusinessLikesReceived (businessId, page = 1, limit = 10) {
        try {
            const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])();
            // Get total count first
            const { count: totalCount, error: countError } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].LIKES).select(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID, {
                count: "exact",
                head: true
            }).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_PROFILE_ID, businessId);
            if (countError) {
                throw new Error("Failed to get total count");
            }
            if (!totalCount || totalCount === 0) {
                return {
                    items: [],
                    totalCount: 0,
                    hasMore: false,
                    currentPage: page
                };
            }
            // Get likes with pagination (database-level pagination)
            const from = (page - 1) * limit;
            console.log(`[likesService.fetchBusinessLikesReceived] Applying pagination: from=${from}, limit=${limit}`);
            const { data: likes, error: likesError } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].LIKES).select(`${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].USER_ID}`).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_PROFILE_ID, businessId).range(from, from + limit - 1);
            console.log(`[likesService.fetchBusinessLikesReceived] Query returned ${likes?.length} items`);
            if (likesError) {
                throw new Error("Failed to fetch likes");
            }
            if (!likes || likes.length === 0) {
                return {
                    items: [],
                    totalCount,
                    hasMore: false,
                    currentPage: page
                };
            }
            // Get user IDs for the paginated results only
            const userIds = likes.map((like)=>like.user_id);
            // Fetch customer and business profiles for paginated results only
            const [customerProfiles, businessProfiles] = await Promise.all([
                supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].CUSTOMER_PROFILES).select(`${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].NAME}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].EMAIL}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].AVATAR_URL}`).in(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID, userIds),
                supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].BUSINESS_PROFILES).select(`${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_NAME}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_SLUG}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].LOGO_URL}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].CITY}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].STATE}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].PINCODE}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ADDRESS_LINE}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].LOCALITY}`).in(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID, userIds)
            ]);
            // Create maps for easy lookup
            const customerProfilesMap = new Map(customerProfiles.data?.map((profile)=>[
                    profile.id,
                    profile
                ]) || []);
            const businessProfilesMap = new Map(businessProfiles.data?.map((profile)=>[
                    profile.id,
                    profile
                ]) || []);
            // Combine likes with their corresponding profiles
            const processedLikes = likes.map((like)=>{
                const customerProfile = customerProfilesMap.get(like.user_id);
                const businessProfile = businessProfilesMap.get(like.user_id);
                if (customerProfile) {
                    return {
                        id: like.id,
                        user_id: like.user_id,
                        customer_profiles: customerProfile,
                        profile_type: "customer"
                    };
                } else if (businessProfile) {
                    return {
                        id: like.id,
                        user_id: like.user_id,
                        business_profiles: businessProfile,
                        profile_type: "business"
                    };
                }
                return null;
            }).filter((item)=>item !== null);
            const hasMore = totalCount > from + limit;
            return {
                items: processedLikes,
                totalCount,
                hasMore,
                currentPage: page
            };
        } catch (error) {
            throw error;
        }
    }
};
const reviewsService = {
    /**
   * Fetch user reviews with pagination and sorting
   */ async fetchReviews (userId, page = 1, limit = 20, sortBy = "newest", searchTerm = "") {
        try {
            const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])();
            // Get total count first with separate query to avoid count issues with joins
            let countQuery = supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].RATINGS_REVIEWS).select(`
          ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID},
          ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].BUSINESS_PROFILES}!inner (
            ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID},
            ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_NAME}
          )
        `, {
                count: "exact",
                head: true
            }).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].USER_ID, userId);
            // Apply search filter to count query if provided
            if (searchTerm && searchTerm.trim()) {
                countQuery = countQuery.ilike(`${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].BUSINESS_PROFILES}.${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_NAME}`, `%${searchTerm.trim()}%`);
            }
            const { count: totalCount, error: countError } = await countQuery;
            if (countError) {
                throw new Error(`Failed to get reviews count: ${countError.message}`);
            }
            // If no reviews, return empty result
            if (!totalCount || totalCount === 0) {
                return {
                    items: [],
                    totalCount: 0,
                    hasMore: false,
                    currentPage: page
                };
            }
            // Build the main query for fetching data
            let query = supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].RATINGS_REVIEWS).select(`
          ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID},
          ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].RATING},
          ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].REVIEW_TEXT},
          ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].CREATED_AT},
          ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].UPDATED_AT},
          ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_PROFILE_ID},
          ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].USER_ID},
          ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].BUSINESS_PROFILES}!inner (
            ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID},
            ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_NAME},
            ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_SLUG},
            ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].LOGO_URL}
          )
        `).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].USER_ID, userId);
            // Apply search filter if provided
            if (searchTerm && searchTerm.trim()) {
                query = query.ilike(`${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].BUSINESS_PROFILES}.${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_NAME}`, `%${searchTerm.trim()}%`);
            }
            // Apply sorting
            switch(sortBy){
                case "oldest":
                    query = query.order(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].CREATED_AT, {
                        ascending: true
                    });
                    break;
                case "rating_high":
                    query = query.order(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].RATING, {
                        ascending: false
                    });
                    break;
                case "rating_low":
                    query = query.order(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].RATING, {
                        ascending: true
                    });
                    break;
                case "newest":
                default:
                    query = query.order(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].CREATED_AT, {
                        ascending: false
                    });
                    break;
            }
            // Apply pagination
            const offset = (page - 1) * limit;
            query = query.range(offset, offset + limit - 1);
            const { data: reviews, error } = await query;
            if (error) {
                throw new Error(`Failed to fetch reviews: ${error.message}`);
            }
            // Transform the data (business profiles are already joined)
            const transformedReviews = (reviews || []).map((review)=>({
                    id: review.id,
                    rating: review.rating,
                    review_text: review.review_text,
                    created_at: review.created_at,
                    updated_at: review.updated_at,
                    business_profile_id: review.business_profile_id,
                    user_id: review.user_id,
                    business_profiles: Array.isArray(review.business_profiles) ? review.business_profiles[0] : review.business_profiles
                }));
            const hasMore = totalCount > offset + limit;
            return {
                items: transformedReviews,
                totalCount,
                hasMore,
                currentPage: page
            };
        } catch (error) {
            console.error("Error in fetchReviews:", error);
            throw error;
        }
    },
    /**
   * Delete a review
   */ async deleteReview (reviewId) {
        try {
            const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])();
            const { error } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].RATINGS_REVIEWS).delete().eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID, reviewId);
            if (error) {
                throw new Error(`Failed to delete review: ${error.message}`);
            }
        } catch (error) {
            console.error("Error in deleteReview:", error);
            throw error;
        }
    },
    /**
   * Update a review
   */ async updateReview (reviewId, rating, reviewText) {
        try {
            const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])();
            const { error } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].RATINGS_REVIEWS).update({
                [__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].RATING]: rating,
                [__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].REVIEW_TEXT]: reviewText || null,
                [__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].UPDATED_AT]: new Date().toISOString()
            }).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID, reviewId);
            if (error) {
                throw new Error(`Failed to update review: ${error.message}`);
            }
        } catch (error) {
            console.error("Error in updateReview:", error);
            throw error;
        }
    },
    /**
   * Fetch reviews received by a business (customers/businesses who reviewed this business)
   */ async fetchBusinessReviewsReceived (businessId, page = 1, limit = 10, sortBy = "newest") {
        try {
            const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])();
            // Get total count first
            const { count: totalCount, error: countError } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].RATINGS_REVIEWS).select(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID, {
                count: "exact",
                head: true
            }).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_PROFILE_ID, businessId);
            if (countError) {
                throw new Error("Failed to get total count");
            }
            if (!totalCount || totalCount === 0) {
                return {
                    items: [],
                    totalCount: 0,
                    hasMore: false,
                    currentPage: page
                };
            }
            // Get reviews with pagination and sorting
            const from = (page - 1) * limit;
            let query = supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].RATINGS_REVIEWS).select(`${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].RATING}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].REVIEW_TEXT}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].CREATED_AT}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].UPDATED_AT}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_PROFILE_ID}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].USER_ID}`).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_PROFILE_ID, businessId);
            // Apply sorting
            switch(sortBy){
                case "oldest":
                    query = query.order(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].CREATED_AT, {
                        ascending: true
                    });
                    break;
                case "rating_high":
                    query = query.order(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].RATING, {
                        ascending: false
                    });
                    break;
                case "rating_low":
                    query = query.order(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].RATING, {
                        ascending: true
                    });
                    break;
                case "newest":
                default:
                    query = query.order(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].CREATED_AT, {
                        ascending: false
                    });
                    break;
            }
            query = query.range(from, from + limit - 1);
            const { data: reviews, error: reviewsError } = await query;
            if (reviewsError) {
                throw new Error("Failed to fetch reviews");
            }
            if (!reviews || reviews.length === 0) {
                return {
                    items: [],
                    totalCount,
                    hasMore: false,
                    currentPage: page
                };
            }
            // Get user IDs for the paginated results only
            const userIds = reviews.map((review)=>review.user_id);
            // Fetch customer and business profiles for paginated results only
            const [customerProfiles, businessProfiles] = await Promise.all([
                supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].CUSTOMER_PROFILES).select(`${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].NAME}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].EMAIL}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].AVATAR_URL}`).in(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID, userIds),
                supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].BUSINESS_PROFILES).select(`${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_NAME}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_SLUG}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].LOGO_URL}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].CITY}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].STATE}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].PINCODE}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ADDRESS_LINE}`).in(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID, userIds)
            ]);
            // Create maps for easy lookup
            const customerProfilesMap = new Map(customerProfiles.data?.map((profile)=>[
                    profile.id,
                    profile
                ]) || []);
            const businessProfilesMap = new Map(businessProfiles.data?.map((profile)=>[
                    profile.id,
                    profile
                ]) || []);
            // Combine reviews with their corresponding profiles
            const processedReviews = reviews.map((review)=>{
                const customerProfile = customerProfilesMap.get(review.user_id);
                const businessProfile = businessProfilesMap.get(review.user_id);
                if (customerProfile) {
                    return {
                        id: review.id,
                        rating: review.rating,
                        review_text: review.review_text,
                        created_at: review.created_at,
                        updated_at: review.updated_at,
                        business_profile_id: review.business_profile_id,
                        user_id: review.user_id,
                        customer_profiles: customerProfile,
                        profile_type: "customer"
                    };
                } else if (businessProfile) {
                    return {
                        id: review.id,
                        rating: review.rating,
                        review_text: review.review_text,
                        created_at: review.created_at,
                        updated_at: review.updated_at,
                        business_profile_id: review.business_profile_id,
                        user_id: review.user_id,
                        business_profiles: businessProfile,
                        profile_type: "business"
                    };
                }
                return null;
            }).filter((item)=>item !== null);
            const hasMore = totalCount > from + limit;
            return {
                items: processedReviews,
                totalCount,
                hasMore,
                currentPage: page
            };
        } catch (error) {
            console.error("Error in fetchBusinessReviewsReceived:", error);
            throw error;
        }
    }
};
async function getActivityMetrics(userId) {
    try {
        const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])();
        // Fetch fresh metrics from database
        const [likesResult, reviewsResult, subscriptionsResult] = await Promise.all([
            supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].LIKES).select(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID, {
                count: "exact"
            }).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].USER_ID, userId),
            supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].RATINGS_REVIEWS).select(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID, {
                count: "exact"
            }).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].USER_ID, userId),
            supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].SUBSCRIPTIONS).select(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID, {
                count: "exact"
            }).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].USER_ID, userId)
        ]);
        const metrics = {
            likesCount: likesResult.count || 0,
            reviewCount: reviewsResult.count || 0,
            subscriptionCount: subscriptionsResult.count || 0,
            lastUpdated: new Date().toISOString()
        };
        return metrics;
    } catch (error) {
        console.error("Error fetching activity metrics:", error);
        return null;
    }
}
const socialService = {
    likesService,
    reviewsService,
    subscriptionsService,
    fetchActivityMetrics: getActivityMetrics
};
}}),
"[project]/app/(dashboard)/dashboard/business/subscriptions/actions.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ [{"70767352568f65bd61d8ef5bec6696d5922a916821":"fetchBusinessSubscribers","78b4084bd29743d172a768039fcf677bd4e57a62b6":"fetchBusinessFollowing"},"",""] */ __turbopack_context__.s({
    "fetchBusinessFollowing": (()=>fetchBusinessFollowing),
    "fetchBusinessSubscribers": (()=>fetchBusinessSubscribers)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/server-reference.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$app$2d$render$2f$encryption$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/app-render/encryption.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$services$2f$socialService$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/services/socialService.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-validate.js [app-rsc] (ecmascript)");
;
;
;
async function fetchBusinessSubscribers(businessId, page = 1, limit = 10) {
    try {
        console.log(`[fetchBusinessSubscribers] Fetching for businessId: ${businessId}, page: ${page}, limit: ${limit}`);
        const result = await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$services$2f$socialService$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["subscriptionsService"].fetchBusinessFollowers(businessId, page, limit);
        console.log(`[fetchBusinessSubscribers] Received ${result.items.length} items, totalCount: ${result.totalCount}`);
        // Transform FollowersResult to SubscribersResult for compatibility
        return {
            items: result.items.map((item)=>({
                    id: item.id,
                    profile: item.profile
                })),
            totalCount: result.totalCount,
            hasMore: result.hasMore,
            currentPage: result.currentPage
        };
    } catch (error) {
        console.error('Error in fetchBusinessSubscribers:', error);
        throw error;
    }
}
async function fetchBusinessFollowing(businessId, page = 1, limit = 10, searchTerm = "") {
    try {
        console.log(`[fetchBusinessFollowing] Fetching for businessId: ${businessId}, page: ${page}, limit: ${limit}, searchTerm: ${searchTerm}`);
        const result = await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$services$2f$socialService$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["subscriptionsService"].fetchSubscriptions(businessId, page, limit, searchTerm);
        console.log(`[fetchBusinessFollowing] Received ${result.items.length} items, totalCount: ${result.totalCount}`);
        return result;
    } catch (error) {
        console.error('Error in fetchBusinessFollowing:', error);
        throw error;
    }
}
;
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ensureServerEntryExports"])([
    fetchBusinessSubscribers,
    fetchBusinessFollowing
]);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(fetchBusinessSubscribers, "70767352568f65bd61d8ef5bec6696d5922a916821", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(fetchBusinessFollowing, "78b4084bd29743d172a768039fcf677bd4e57a62b6", null);
}}),
"[project]/lib/actions/interactions.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ [{"400ce0f8ec196401806c382d978258789a6bbfa033":"subscribeToBusiness","4033eb63cd44d3475d2ca68b3f4b016d200c4437d3":"likeBusiness","408cb5935c4efd4f2f4d799de6b096e273c44dd623":"deleteReview","40a7020d8c9a90386aa2f0bbb96a95966205d638a2":"unsubscribeFromBusiness","40d1f5ebd794530a88fce0e678594532730f376d2b":"unlikeBusiness","40f52e3a8eb887839216c227dbd4ba542612e18734":"getInteractionStatus","7065ae2952c1cff81bb2794b8cbc5227dbebc9f2ab":"submitReview"},"",""] */ __turbopack_context__.s({
    "deleteReview": (()=>deleteReview),
    "getInteractionStatus": (()=>getInteractionStatus),
    "likeBusiness": (()=>likeBusiness),
    "submitReview": (()=>submitReview),
    "subscribeToBusiness": (()=>subscribeToBusiness),
    "unlikeBusiness": (()=>unlikeBusiness),
    "unsubscribeFromBusiness": (()=>unsubscribeFromBusiness)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/server-reference.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$app$2d$render$2f$encryption$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/app-render/encryption.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/supabase/server.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/cache.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-validate.js [app-rsc] (ecmascript)");
;
;
;
;
async function subscribeToBusiness(businessProfileId) {
    // const cookieStore = cookies(); // No longer needed here
    const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])(); // Await the async function
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
        return {
            success: false,
            error: "User not authenticated."
        };
    }
    // Prevent a business from subscribing to their own business card
    if (user.id === businessProfileId) {
        return {
            success: false,
            error: "You cannot subscribe to your own business card."
        };
    }
    // Check if the current user is a business (has a business profile)
    const { data: userBusinessProfile } = await supabase.from("business_profiles").select("id").eq("id", user.id).maybeSingle();
    try {
        // 1. Insert subscription - Use regular client with proper RLS
        const { error: insertError } = await supabase.from("subscriptions").insert({
            user_id: user.id,
            business_profile_id: businessProfileId
        });
        if (insertError) {
            // Handle potential unique constraint violation (already subscribed) gracefully
            if (insertError.code === "23505") {
                // unique_violation
                console.log(`User ${user.id} already subscribed to business ${businessProfileId}.`);
                // Optionally return success true if already subscribed is acceptable
                return {
                    success: true
                };
            }
            console.error("Error inserting subscription:", insertError);
            throw new Error(insertError.message);
        }
        // Note: We don't need to manually update the subscription count
        // The database trigger 'update_total_subscriptions' will handle this automatically
        // 3. Revalidate paths
        // Revalidate the specific card page and potentially the user's dashboard
        // Use regular client - business_profiles has public read access
        const { data: cardData } = await supabase.from("business_profiles").select("business_slug").eq("id", businessProfileId).single();
        if (cardData?.business_slug) {
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revalidatePath"])(`/${cardData.business_slug}`);
        }
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revalidatePath"])("/dashboard/customer"); // Revalidate customer dashboard
        // Check if the current user is a business and revalidate business dashboard
        if (userBusinessProfile) {
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revalidatePath"])("/dashboard/business"); // Revalidate business dashboard
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revalidatePath"])("/dashboard/business/subscriptions"); // Revalidate business subscriptions page
        }
        // Revalidate the activities page for the business
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revalidatePath"])(`/dashboard/business/activities`);
        return {
            success: true
        };
    } catch (error) {
        console.error("Unexpected error in subscribeToBusiness:", error);
        const errorMessage = error instanceof Error ? error.message : "An unexpected error occurred.";
        return {
            success: false,
            error: errorMessage
        };
    }
}
async function unsubscribeFromBusiness(businessProfileId) {
    // const cookieStore = cookies();
    const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])(); // Await the async function
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
        return {
            success: false,
            error: "User not authenticated."
        };
    }
    // Prevent a business from unsubscribing from their own business card
    if (user.id === businessProfileId) {
        return {
            success: false,
            error: "You cannot unsubscribe from your own business card."
        };
    }
    // Check if the current user is a business (has a business profile)
    const { data: userBusinessProfile } = await supabase.from("business_profiles").select("id").eq("id", user.id).maybeSingle();
    try {
        // 1. Delete subscription - Use regular client with proper RLS
        const { error: deleteError } = await supabase.from("subscriptions").delete().match({
            user_id: user.id,
            business_profile_id: businessProfileId
        });
        if (deleteError) {
            console.error("Error deleting subscription:", deleteError);
            throw new Error(deleteError.message);
        }
        // Note: We don't need to manually update the subscription count
        // The database trigger 'update_total_subscriptions' will handle this automatically
        // 3. Revalidate paths
        // Use regular client - business_profiles has public read access
        const { data: cardData } = await supabase.from("business_profiles").select("business_slug").eq("id", businessProfileId).single();
        if (cardData?.business_slug) {
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revalidatePath"])(`/${cardData.business_slug}`);
        }
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revalidatePath"])("/dashboard/customer");
        // Check if the current user is a business and revalidate business dashboard
        if (userBusinessProfile) {
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revalidatePath"])("/dashboard/business"); // Revalidate business dashboard
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revalidatePath"])("/dashboard/business/subscriptions"); // Revalidate business subscriptions page
        }
        // Revalidate the activities page for the business
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revalidatePath"])(`/dashboard/business/activities`);
        return {
            success: true
        };
    } catch (error) {
        console.error("Unexpected error in unsubscribeFromBusiness:", error);
        const errorMessage = error instanceof Error ? error.message : "An unexpected error occurred.";
        return {
            success: false,
            error: errorMessage
        };
    }
}
async function submitReview(businessProfileId, rating, reviewText// Allow null for review text
) {
    // const cookieStore = cookies();
    const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])(); // Await the async function
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
        return {
            success: false,
            error: "User not authenticated."
        };
    }
    // Prevent a business from reviewing their own business card
    if (user.id === businessProfileId) {
        return {
            success: false,
            error: "You cannot review your own business card."
        };
    }
    if (rating < 1 || rating > 5) {
        return {
            success: false,
            error: "Rating must be between 1 and 5."
        };
    }
    try {
        // Upsert the review: insert if not exists, update if exists - Use regular client with proper RLS
        const { error: upsertError } = await supabase.from("ratings_reviews").upsert({
            user_id: user.id,
            business_profile_id: businessProfileId,
            rating: rating,
            review_text: reviewText,
            updated_at: new Date().toISOString()
        }, {
            onConflict: "user_id, business_profile_id"
        });
        if (upsertError) {
            console.error("Error submitting review:", upsertError);
            throw new Error(upsertError.message);
        }
        // Average rating is handled by the database trigger
        // Revalidate paths
        // Use regular client - business_profiles has public read access
        const { data: cardData } = await supabase.from("business_profiles").select("business_slug").eq("id", businessProfileId).single();
        if (cardData?.business_slug) {
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revalidatePath"])(`/${cardData.business_slug}`);
        }
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revalidatePath"])("/dashboard/customer"); // Revalidate customer dashboard where reviews might be shown
        // Revalidate the activities page for the business
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revalidatePath"])(`/dashboard/business/activities`);
        return {
            success: true
        };
    } catch (error) {
        console.error("Unexpected error in submitReview:", error);
        const errorMessage = error instanceof Error ? error.message : "An unexpected error occurred.";
        return {
            success: false,
            error: errorMessage
        };
    }
}
async function deleteReview(businessProfileId) {
    // const cookieStore = cookies();
    const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])(); // Await the async function
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
        return {
            success: false,
            error: "User not authenticated."
        };
    }
    try {
        // Use regular client with proper RLS
        const { error: deleteError } = await supabase.from("ratings_reviews").delete().match({
            user_id: user.id,
            business_profile_id: businessProfileId
        });
        if (deleteError) {
            console.error("Error deleting review:", deleteError);
            throw new Error(deleteError.message);
        }
        // Average rating is handled by the database trigger
        // Revalidate paths
        // Use regular client - business_profiles has public read access
        const { data: cardData } = await supabase.from("business_profiles").select("business_slug").eq("id", businessProfileId).single();
        if (cardData?.business_slug) {
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revalidatePath"])(`/${cardData.business_slug}`);
        }
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revalidatePath"])("/dashboard/customer");
        // Revalidate the activities page for the business
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revalidatePath"])(`/dashboard/business/activities`);
        return {
            success: true
        };
    } catch (error) {
        console.error("Unexpected error in deleteReview:", error);
        const errorMessage = error instanceof Error ? error.message : "An unexpected error occurred.";
        return {
            success: false,
            error: errorMessage
        };
    }
}
async function likeBusiness(businessProfileId) {
    // const cookieStore = cookies();
    const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])(); // Await the async function
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
        return {
            success: false,
            error: "User not authenticated."
        };
    }
    // Prevent a business from liking their own business card
    if (user.id === businessProfileId) {
        return {
            success: false,
            error: "You cannot like your own business card."
        };
    }
    try {
        // 1. Insert like - Use regular client with proper RLS
        const { error: insertError } = await supabase.from("likes").insert({
            user_id: user.id,
            business_profile_id: businessProfileId
        });
        if (insertError) {
            // Handle potential unique constraint violation (already liked) gracefully
            if (insertError.code === "23505") {
                // unique_violation
                console.log(`User ${user.id} already liked business ${businessProfileId}.`);
                return {
                    success: true
                }; // Consider it success if already liked
            }
            console.error("Error inserting like:", insertError);
            throw new Error(insertError.message);
        }
        // Note: We don't need to manually update the like count
        // The database trigger 'update_total_likes' will handle this automatically
        // 3. Revalidate paths
        // Use regular client - business_profiles has public read access
        const { data: cardData } = await supabase.from("business_profiles").select("business_slug").eq("id", businessProfileId).single();
        if (cardData?.business_slug) {
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revalidatePath"])(`/${cardData.business_slug}`);
        }
        // Check if the current user is a business and revalidate business dashboard
        const { data: userBusinessProfile } = await supabase.from("business_profiles").select("id").eq("id", user.id).maybeSingle();
        if (userBusinessProfile) {
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revalidatePath"])("/dashboard/business"); // Revalidate business dashboard
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revalidatePath"])("/dashboard/business/likes"); // Revalidate business likes page
        }
        // Revalidate the activities page for the business
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revalidatePath"])(`/dashboard/business/activities`);
        return {
            success: true
        };
    } catch (error) {
        console.error("Unexpected error in likeBusiness:", error);
        const errorMessage = error instanceof Error ? error.message : "An unexpected error occurred.";
        return {
            success: false,
            error: errorMessage
        };
    }
}
async function unlikeBusiness(businessProfileId) {
    // const cookieStore = cookies();
    const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])(); // Await the async function
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
        return {
            success: false,
            error: "User not authenticated."
        };
    }
    // Prevent a business from unliking their own business card
    if (user.id === businessProfileId) {
        return {
            success: false,
            error: "You cannot unlike your own business card."
        };
    }
    try {
        // 1. Delete like - Use regular client with proper RLS
        const { error: deleteError } = await supabase.from("likes").delete().match({
            user_id: user.id,
            business_profile_id: businessProfileId
        });
        if (deleteError) {
            console.error("Error deleting like:", deleteError);
            throw new Error(deleteError.message);
        }
        // Note: We don't need to manually update the like count
        // The database trigger 'update_total_likes' will handle this automatically
        // 3. Revalidate paths
        // Use regular client - business_profiles has public read access
        const { data: cardData } = await supabase.from("business_profiles").select("business_slug").eq("id", businessProfileId).single();
        if (cardData?.business_slug) {
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revalidatePath"])(`/${cardData.business_slug}`);
        }
        // Check if the current user is a business and revalidate business dashboard
        const { data: userBusinessProfile } = await supabase.from("business_profiles").select("id").eq("id", user.id).maybeSingle();
        if (userBusinessProfile) {
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revalidatePath"])("/dashboard/business"); // Revalidate business dashboard
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revalidatePath"])("/dashboard/business/likes"); // Revalidate business likes page
        }
        // Revalidate the activities page for the business
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revalidatePath"])(`/dashboard/business/activities`);
        return {
            success: true
        };
    } catch (error) {
        console.error("Unexpected error in unlikeBusiness:", error);
        const errorMessage = error instanceof Error ? error.message : "An unexpected error occurred.";
        return {
            success: false,
            error: errorMessage
        };
    }
}
async function getInteractionStatus(businessProfileId) {
    // const cookieStore = cookies();
    const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])(); // Await the async function
    let userId = null;
    // Try to get authenticated user, but proceed even if not logged in
    const { data: { user } } = await supabase.auth.getUser();
    if (user) {
        userId = user.id;
    }
    // Default status for anonymous users
    const defaultStatus = {
        isSubscribed: false,
        hasLiked: false,
        userRating: null,
        userReview: null
    };
    if (!userId) {
        return defaultStatus; // Return default if no user is logged in
    }
    try {
        // Use regular client - all these tables have public read access
        // Fetch all statuses in parallel
        const [subscriptionRes, likeRes, reviewRes] = await Promise.all([
            supabase.from("subscriptions").select("id", {
                count: "exact",
                head: true
            }) // Just check existence
            .match({
                user_id: userId,
                business_profile_id: businessProfileId
            }),
            supabase.from("likes").select("id", {
                count: "exact",
                head: true
            }) // Just check existence
            .match({
                user_id: userId,
                business_profile_id: businessProfileId
            }),
            supabase.from("ratings_reviews").select("rating, review_text").match({
                user_id: userId,
                business_profile_id: businessProfileId
            }).maybeSingle()
        ]);
        // Check for errors in parallel fetches
        if (subscriptionRes.error) throw new Error(`Subscription fetch error: ${subscriptionRes.error.message}`);
        if (likeRes.error) throw new Error(`Like fetch error: ${likeRes.error.message}`);
        if (reviewRes.error) throw new Error(`Review fetch error: ${reviewRes.error.message}`);
        const reviewData = reviewRes.data;
        return {
            isSubscribed: (subscriptionRes.count ?? 0) > 0,
            hasLiked: (likeRes.count ?? 0) > 0,
            userRating: reviewData?.rating ?? null,
            userReview: reviewData?.review_text ?? null
        };
    } catch (error) {
        console.error("Error fetching interaction status:", error);
        const errorMessage = error instanceof Error ? error.message : "An unexpected error occurred.";
        // Return default status but include the error message
        return {
            ...defaultStatus,
            error: errorMessage
        };
    }
}
;
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ensureServerEntryExports"])([
    subscribeToBusiness,
    unsubscribeFromBusiness,
    submitReview,
    deleteReview,
    likeBusiness,
    unlikeBusiness,
    getInteractionStatus
]);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(subscribeToBusiness, "400ce0f8ec196401806c382d978258789a6bbfa033", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(unsubscribeFromBusiness, "40a7020d8c9a90386aa2f0bbb96a95966205d638a2", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(submitReview, "7065ae2952c1cff81bb2794b8cbc5227dbebc9f2ab", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(deleteReview, "408cb5935c4efd4f2f4d799de6b096e273c44dd623", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(likeBusiness, "4033eb63cd44d3475d2ca68b3f4b016d200c4437d3", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(unlikeBusiness, "40d1f5ebd794530a88fce0e678594532730f376d2b", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(getInteractionStatus, "40f52e3a8eb887839216c227dbd4ba542612e18734", null);
}}),
"[project]/.next-internal/server/app/(dashboard)/dashboard/business/subscriptions/page/actions.js { ACTIONS_MODULE0 => \"[project]/app/auth/actions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/app/(dashboard)/dashboard/business/subscriptions/actions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/lib/actions/interactions.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$auth$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/auth/actions.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$dashboard$292f$dashboard$2f$business$2f$subscriptions$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/(dashboard)/dashboard/business/subscriptions/actions.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$interactions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/actions/interactions.ts [app-rsc] (ecmascript)");
;
;
;
;
}}),
"[project]/.next-internal/server/app/(dashboard)/dashboard/business/subscriptions/page/actions.js { ACTIONS_MODULE0 => \"[project]/app/auth/actions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/app/(dashboard)/dashboard/business/subscriptions/actions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/lib/actions/interactions.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$auth$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/auth/actions.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$dashboard$292f$dashboard$2f$business$2f$subscriptions$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/(dashboard)/dashboard/business/subscriptions/actions.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$interactions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/actions/interactions.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f28$dashboard$292f$dashboard$2f$business$2f$subscriptions$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$app$2f$auth$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$app$2f28$dashboard$292f$dashboard$2f$business$2f$subscriptions$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$interactions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i('[project]/.next-internal/server/app/(dashboard)/dashboard/business/subscriptions/page/actions.js { ACTIONS_MODULE0 => "[project]/app/auth/actions.ts [app-rsc] (ecmascript)", ACTIONS_MODULE1 => "[project]/app/(dashboard)/dashboard/business/subscriptions/actions.ts [app-rsc] (ecmascript)", ACTIONS_MODULE2 => "[project]/lib/actions/interactions.ts [app-rsc] (ecmascript)" } [app-rsc] (server actions loader, ecmascript) <locals>');
}}),
"[project]/.next-internal/server/app/(dashboard)/dashboard/business/subscriptions/page/actions.js { ACTIONS_MODULE0 => \"[project]/app/auth/actions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/app/(dashboard)/dashboard/business/subscriptions/actions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/lib/actions/interactions.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript) <exports>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "0027aa970a57d5cbd11af21933b24fca73bbd99631": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$auth$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["signOutUser"]),
    "40a7020d8c9a90386aa2f0bbb96a95966205d638a2": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$interactions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["unsubscribeFromBusiness"]),
    "70767352568f65bd61d8ef5bec6696d5922a916821": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$dashboard$292f$dashboard$2f$business$2f$subscriptions$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fetchBusinessSubscribers"]),
    "78b4084bd29743d172a768039fcf677bd4e57a62b6": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$dashboard$292f$dashboard$2f$business$2f$subscriptions$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fetchBusinessFollowing"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$auth$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/auth/actions.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$dashboard$292f$dashboard$2f$business$2f$subscriptions$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/(dashboard)/dashboard/business/subscriptions/actions.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$interactions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/actions/interactions.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f28$dashboard$292f$dashboard$2f$business$2f$subscriptions$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$app$2f$auth$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$app$2f28$dashboard$292f$dashboard$2f$business$2f$subscriptions$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$interactions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i('[project]/.next-internal/server/app/(dashboard)/dashboard/business/subscriptions/page/actions.js { ACTIONS_MODULE0 => "[project]/app/auth/actions.ts [app-rsc] (ecmascript)", ACTIONS_MODULE1 => "[project]/app/(dashboard)/dashboard/business/subscriptions/actions.ts [app-rsc] (ecmascript)", ACTIONS_MODULE2 => "[project]/lib/actions/interactions.ts [app-rsc] (ecmascript)" } [app-rsc] (server actions loader, ecmascript) <locals>');
}}),
"[project]/.next-internal/server/app/(dashboard)/dashboard/business/subscriptions/page/actions.js { ACTIONS_MODULE0 => \"[project]/app/auth/actions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/app/(dashboard)/dashboard/business/subscriptions/actions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/lib/actions/interactions.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "0027aa970a57d5cbd11af21933b24fca73bbd99631": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f28$dashboard$292f$dashboard$2f$business$2f$subscriptions$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$app$2f$auth$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$app$2f28$dashboard$292f$dashboard$2f$business$2f$subscriptions$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$interactions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["0027aa970a57d5cbd11af21933b24fca73bbd99631"]),
    "40a7020d8c9a90386aa2f0bbb96a95966205d638a2": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f28$dashboard$292f$dashboard$2f$business$2f$subscriptions$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$app$2f$auth$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$app$2f28$dashboard$292f$dashboard$2f$business$2f$subscriptions$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$interactions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["40a7020d8c9a90386aa2f0bbb96a95966205d638a2"]),
    "70767352568f65bd61d8ef5bec6696d5922a916821": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f28$dashboard$292f$dashboard$2f$business$2f$subscriptions$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$app$2f$auth$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$app$2f28$dashboard$292f$dashboard$2f$business$2f$subscriptions$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$interactions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["70767352568f65bd61d8ef5bec6696d5922a916821"]),
    "78b4084bd29743d172a768039fcf677bd4e57a62b6": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f28$dashboard$292f$dashboard$2f$business$2f$subscriptions$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$app$2f$auth$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$app$2f28$dashboard$292f$dashboard$2f$business$2f$subscriptions$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$interactions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["78b4084bd29743d172a768039fcf677bd4e57a62b6"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f28$dashboard$292f$dashboard$2f$business$2f$subscriptions$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$app$2f$auth$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$app$2f28$dashboard$292f$dashboard$2f$business$2f$subscriptions$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$interactions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i('[project]/.next-internal/server/app/(dashboard)/dashboard/business/subscriptions/page/actions.js { ACTIONS_MODULE0 => "[project]/app/auth/actions.ts [app-rsc] (ecmascript)", ACTIONS_MODULE1 => "[project]/app/(dashboard)/dashboard/business/subscriptions/actions.ts [app-rsc] (ecmascript)", ACTIONS_MODULE2 => "[project]/lib/actions/interactions.ts [app-rsc] (ecmascript)" } [app-rsc] (server actions loader, ecmascript) <module evaluation>');
var __TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f28$dashboard$292f$dashboard$2f$business$2f$subscriptions$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$app$2f$auth$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$app$2f28$dashboard$292f$dashboard$2f$business$2f$subscriptions$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$interactions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__ = __turbopack_context__.i('[project]/.next-internal/server/app/(dashboard)/dashboard/business/subscriptions/page/actions.js { ACTIONS_MODULE0 => "[project]/app/auth/actions.ts [app-rsc] (ecmascript)", ACTIONS_MODULE1 => "[project]/app/(dashboard)/dashboard/business/subscriptions/actions.ts [app-rsc] (ecmascript)", ACTIONS_MODULE2 => "[project]/lib/actions/interactions.ts [app-rsc] (ecmascript)" } [app-rsc] (server actions loader, ecmascript) <exports>');
}}),
"[project]/app/favicon.ico.mjs { IMAGE => \"[project]/app/favicon.ico (static in ecmascript)\" } [app-rsc] (structured image object, ecmascript, Next.js server component)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.n(__turbopack_context__.i("[project]/app/favicon.ico.mjs { IMAGE => \"[project]/app/favicon.ico (static in ecmascript)\" } [app-rsc] (structured image object, ecmascript)"));
}}),
"[project]/app/opengraph-image.png.mjs { IMAGE => \"[project]/app/opengraph-image.png (static in ecmascript)\" } [app-rsc] (structured image object, ecmascript, Next.js server component)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.n(__turbopack_context__.i("[project]/app/opengraph-image.png.mjs { IMAGE => \"[project]/app/opengraph-image.png (static in ecmascript)\" } [app-rsc] (structured image object, ecmascript)"));
}}),
"[project]/app/layout.tsx [app-rsc] (ecmascript, Next.js server component)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.n(__turbopack_context__.i("[project]/app/layout.tsx [app-rsc] (ecmascript)"));
}}),
"[project]/app/(dashboard)/dashboard/business/layout.tsx [app-rsc] (ecmascript, Next.js server component)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.n(__turbopack_context__.i("[project]/app/(dashboard)/dashboard/business/layout.tsx [app-rsc] (ecmascript)"));
}}),
"[project]/lib/utils.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "cleanPhoneFromAuth": (()=>cleanPhoneFromAuth),
    "cn": (()=>cn),
    "formatAddress": (()=>formatAddress),
    "formatCurrency": (()=>formatCurrency),
    "formatDate": (()=>formatDate),
    "formatIndianNumberShort": (()=>formatIndianNumberShort),
    "maskEmail": (()=>maskEmail),
    "maskPhoneNumber": (()=>maskPhoneNumber),
    "toTitleCase": (()=>toTitleCase)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/clsx/dist/clsx.mjs [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$tailwind$2d$merge$2f$dist$2f$bundle$2d$mjs$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/tailwind-merge/dist/bundle-mjs.mjs [app-rsc] (ecmascript)");
;
;
function cn(...inputs) {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$tailwind$2d$merge$2f$dist$2f$bundle$2d$mjs$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["twMerge"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["clsx"])(inputs));
}
function cleanPhoneFromAuth(phone) {
    if (!phone) return null;
    let processedPhone = phone.trim();
    // Remove +91 prefix if present
    if (processedPhone.startsWith('+91')) {
        processedPhone = processedPhone.substring(3);
    } else if (processedPhone.length === 12 && processedPhone.startsWith('91')) {
        processedPhone = processedPhone.substring(2);
    }
    // Validate it's a 10-digit number
    if (/^\d{10}$/.test(processedPhone)) {
        return processedPhone;
    }
    return null; // Invalid format
}
function maskPhoneNumber(phone) {
    if (!phone || phone.length < 4) {
        return "Invalid Phone"; // Or return empty string or original if preferred
    }
    const firstTwo = phone.substring(0, 2);
    const lastTwo = phone.substring(phone.length - 2);
    const maskedPart = "*".repeat(phone.length - 4);
    return `${firstTwo}${maskedPart}${lastTwo}`;
}
function maskEmail(email) {
    if (!email || !email.includes("@")) {
        return "Invalid Email"; // Or return empty string or original
    }
    const parts = email.split("@");
    const username = parts[0];
    const domain = parts[1];
    if (username.length <= 2 || domain.length <= 2 || !domain.includes(".")) {
        return "Email Hidden"; // Simple mask for very short/invalid emails
    }
    const maskedUsername = username.substring(0, 2) + "*".repeat(username.length - 2);
    const domainParts = domain.split(".");
    const domainName = domainParts[0];
    const domainTld = domainParts.slice(1).join("."); // Handle multiple parts like .co.uk
    const maskedDomainName = domainName.substring(0, 2) + "*".repeat(domainName.length - 2);
    return `${maskedUsername}@${maskedDomainName}.${domainTld}`;
}
function formatIndianNumberShort(num) {
    if (num === null || num === undefined || isNaN(num)) return "0";
    const absNum = Math.abs(num);
    // Indian units and their values
    const units = [
        {
            value: 1e5,
            symbol: "L"
        },
        {
            value: 1e7,
            symbol: "Cr"
        },
        {
            value: 1e9,
            symbol: "Ar"
        },
        {
            value: 1e11,
            symbol: "Khar"
        },
        {
            value: 1e13,
            symbol: "Neel"
        },
        {
            value: 1e15,
            symbol: "Padma"
        },
        {
            value: 1e17,
            symbol: "Shankh"
        }
    ];
    // For thousands (K), use western style for sub-lakh
    if (absNum < 1e5) {
        if (absNum >= 1e3) {
            return (num / 1e3).toFixed(1).replace(/\.0$/, "") + "K";
        }
        return num.toString();
    }
    // Find the largest unit that fits
    for(let i = units.length - 1; i >= 0; i--){
        if (absNum >= units[i].value) {
            return (num / units[i].value).toFixed(1).replace(/\.0$/, "") + units[i].symbol;
        }
    }
    // Fallback (should not reach here)
    return num.toString();
}
function formatAddress(data) {
    const addressParts = [
        data.address_line,
        data.locality,
        data.city,
        data.state,
        data.pincode
    ].filter(Boolean);
    return addressParts.join(", ") || "Address not available";
}
function formatDate(date, includeTime = false) {
    if (!date || !(date instanceof Date) || isNaN(date.getTime())) {
        return "Invalid date";
    }
    const options = {
        year: "numeric",
        month: "long",
        day: "numeric",
        timeZone: "Asia/Kolkata"
    };
    if (includeTime) {
        options.hour = "2-digit";
        options.minute = "2-digit";
        options.hour12 = true;
    }
    return date.toLocaleString("en-IN", options);
}
function formatCurrency(amount, currency = "INR") {
    if (amount === null || amount === undefined || isNaN(amount)) {
        return "Invalid amount";
    }
    try {
        return new Intl.NumberFormat("en-IN", {
            style: "currency",
            currency: currency,
            minimumFractionDigits: 0,
            maximumFractionDigits: 2
        }).format(amount);
    } catch  {
        // Catch any error without using the error variable
        // Fallback in case of invalid currency code
        return `${currency} ${amount.toFixed(2)}`;
    }
}
function toTitleCase(text) {
    if (!text) return "";
    return text.toLowerCase().replace(/\b\w/g, (char)=>char.toUpperCase());
}
}}),
"[project]/components/ui/alert.tsx [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Alert": (()=>Alert),
    "AlertDescription": (()=>AlertDescription),
    "AlertTitle": (()=>AlertTitle)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$class$2d$variance$2d$authority$2f$dist$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/class-variance-authority/dist/index.mjs [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/utils.ts [app-rsc] (ecmascript)");
;
;
;
const alertVariants = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$class$2d$variance$2d$authority$2f$dist$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["cva"])("relative w-full rounded-lg border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current", {
    variants: {
        variant: {
            default: "bg-card text-card-foreground",
            destructive: "text-destructive bg-card [&>svg]:text-current *:data-[slot=alert-description]:text-destructive/90"
        }
    },
    defaultVariants: {
        variant: "default"
    }
});
function Alert({ className, variant, ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        "data-slot": "alert",
        role: "alert",
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["cn"])(alertVariants({
            variant
        }), className),
        ...props
    }, void 0, false, {
        fileName: "[project]/components/ui/alert.tsx",
        lineNumber: 28,
        columnNumber: 5
    }, this);
}
function AlertTitle({ className, ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        "data-slot": "alert-title",
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["cn"])("col-start-2 line-clamp-1 min-h-4 font-medium tracking-tight", className),
        ...props
    }, void 0, false, {
        fileName: "[project]/components/ui/alert.tsx",
        lineNumber: 39,
        columnNumber: 5
    }, this);
}
function AlertDescription({ className, ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        "data-slot": "alert-description",
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["cn"])("text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed", className),
        ...props
    }, void 0, false, {
        fileName: "[project]/components/ui/alert.tsx",
        lineNumber: 55,
        columnNumber: 5
    }, this);
}
;
}}),
"[project]/components/ui/skeleton.tsx [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Skeleton": (()=>Skeleton)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/utils.ts [app-rsc] (ecmascript)");
;
;
function Skeleton({ className, ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        "data-slot": "skeleton",
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["cn"])("bg-accent animate-pulse rounded-md", className),
        ...props
    }, void 0, false, {
        fileName: "[project]/components/ui/skeleton.tsx",
        lineNumber: 5,
        columnNumber: 5
    }, this);
}
;
}}),
"[project]/app/(dashboard)/dashboard/business/subscriptions/components/BusinessSubscriptionsPageClient.tsx (client reference/proxy) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server-edge.js [app-rsc] (ecmascript)");
;
const __TURBOPACK__default__export__ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call the default export of [project]/app/(dashboard)/dashboard/business/subscriptions/components/BusinessSubscriptionsPageClient.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/app/(dashboard)/dashboard/business/subscriptions/components/BusinessSubscriptionsPageClient.tsx <module evaluation>", "default");
}}),
"[project]/app/(dashboard)/dashboard/business/subscriptions/components/BusinessSubscriptionsPageClient.tsx (client reference/proxy)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server-edge.js [app-rsc] (ecmascript)");
;
const __TURBOPACK__default__export__ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call the default export of [project]/app/(dashboard)/dashboard/business/subscriptions/components/BusinessSubscriptionsPageClient.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/app/(dashboard)/dashboard/business/subscriptions/components/BusinessSubscriptionsPageClient.tsx", "default");
}}),
"[project]/app/(dashboard)/dashboard/business/subscriptions/components/BusinessSubscriptionsPageClient.tsx [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$dashboard$292f$dashboard$2f$business$2f$subscriptions$2f$components$2f$BusinessSubscriptionsPageClient$2e$tsx__$28$client__reference$2f$proxy$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/app/(dashboard)/dashboard/business/subscriptions/components/BusinessSubscriptionsPageClient.tsx (client reference/proxy) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$dashboard$292f$dashboard$2f$business$2f$subscriptions$2f$components$2f$BusinessSubscriptionsPageClient$2e$tsx__$28$client__reference$2f$proxy$29$__ = __turbopack_context__.i("[project]/app/(dashboard)/dashboard/business/subscriptions/components/BusinessSubscriptionsPageClient.tsx (client reference/proxy)");
;
__turbopack_context__.n(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$dashboard$292f$dashboard$2f$business$2f$subscriptions$2f$components$2f$BusinessSubscriptionsPageClient$2e$tsx__$28$client__reference$2f$proxy$29$__);
}}),
"[project]/app/components/shared/subscriptions/SubscriptionCard.tsx (client reference/proxy) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server-edge.js [app-rsc] (ecmascript)");
;
const __TURBOPACK__default__export__ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call the default export of [project]/app/components/shared/subscriptions/SubscriptionCard.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/app/components/shared/subscriptions/SubscriptionCard.tsx <module evaluation>", "default");
}}),
"[project]/app/components/shared/subscriptions/SubscriptionCard.tsx (client reference/proxy)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server-edge.js [app-rsc] (ecmascript)");
;
const __TURBOPACK__default__export__ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call the default export of [project]/app/components/shared/subscriptions/SubscriptionCard.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/app/components/shared/subscriptions/SubscriptionCard.tsx", "default");
}}),
"[project]/app/components/shared/subscriptions/SubscriptionCard.tsx [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$components$2f$shared$2f$subscriptions$2f$SubscriptionCard$2e$tsx__$28$client__reference$2f$proxy$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/app/components/shared/subscriptions/SubscriptionCard.tsx (client reference/proxy) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$components$2f$shared$2f$subscriptions$2f$SubscriptionCard$2e$tsx__$28$client__reference$2f$proxy$29$__ = __turbopack_context__.i("[project]/app/components/shared/subscriptions/SubscriptionCard.tsx (client reference/proxy)");
;
__turbopack_context__.n(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$components$2f$shared$2f$subscriptions$2f$SubscriptionCard$2e$tsx__$28$client__reference$2f$proxy$29$__);
}}),
"[project]/app/components/shared/subscriptions/SubscriptionCardSkeleton.tsx (client reference/proxy) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "SubscriptionListSkeleton": (()=>SubscriptionListSkeleton),
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server-edge.js [app-rsc] (ecmascript)");
;
const SubscriptionListSkeleton = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call SubscriptionListSkeleton() from the server but SubscriptionListSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/app/components/shared/subscriptions/SubscriptionCardSkeleton.tsx <module evaluation>", "SubscriptionListSkeleton");
const __TURBOPACK__default__export__ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call the default export of [project]/app/components/shared/subscriptions/SubscriptionCardSkeleton.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/app/components/shared/subscriptions/SubscriptionCardSkeleton.tsx <module evaluation>", "default");
}}),
"[project]/app/components/shared/subscriptions/SubscriptionCardSkeleton.tsx (client reference/proxy)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "SubscriptionListSkeleton": (()=>SubscriptionListSkeleton),
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server-edge.js [app-rsc] (ecmascript)");
;
const SubscriptionListSkeleton = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call SubscriptionListSkeleton() from the server but SubscriptionListSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/app/components/shared/subscriptions/SubscriptionCardSkeleton.tsx", "SubscriptionListSkeleton");
const __TURBOPACK__default__export__ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call the default export of [project]/app/components/shared/subscriptions/SubscriptionCardSkeleton.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/app/components/shared/subscriptions/SubscriptionCardSkeleton.tsx", "default");
}}),
"[project]/app/components/shared/subscriptions/SubscriptionCardSkeleton.tsx [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$components$2f$shared$2f$subscriptions$2f$SubscriptionCardSkeleton$2e$tsx__$28$client__reference$2f$proxy$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/app/components/shared/subscriptions/SubscriptionCardSkeleton.tsx (client reference/proxy) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$components$2f$shared$2f$subscriptions$2f$SubscriptionCardSkeleton$2e$tsx__$28$client__reference$2f$proxy$29$__ = __turbopack_context__.i("[project]/app/components/shared/subscriptions/SubscriptionCardSkeleton.tsx (client reference/proxy)");
;
__turbopack_context__.n(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$components$2f$shared$2f$subscriptions$2f$SubscriptionCardSkeleton$2e$tsx__$28$client__reference$2f$proxy$29$__);
}}),
"[project]/app/components/shared/subscriptions/SubscriptionSearch.tsx (client reference/proxy) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server-edge.js [app-rsc] (ecmascript)");
;
const __TURBOPACK__default__export__ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call the default export of [project]/app/components/shared/subscriptions/SubscriptionSearch.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/app/components/shared/subscriptions/SubscriptionSearch.tsx <module evaluation>", "default");
}}),
"[project]/app/components/shared/subscriptions/SubscriptionSearch.tsx (client reference/proxy)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server-edge.js [app-rsc] (ecmascript)");
;
const __TURBOPACK__default__export__ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call the default export of [project]/app/components/shared/subscriptions/SubscriptionSearch.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/app/components/shared/subscriptions/SubscriptionSearch.tsx", "default");
}}),
"[project]/app/components/shared/subscriptions/SubscriptionSearch.tsx [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$components$2f$shared$2f$subscriptions$2f$SubscriptionSearch$2e$tsx__$28$client__reference$2f$proxy$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/app/components/shared/subscriptions/SubscriptionSearch.tsx (client reference/proxy) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$components$2f$shared$2f$subscriptions$2f$SubscriptionSearch$2e$tsx__$28$client__reference$2f$proxy$29$__ = __turbopack_context__.i("[project]/app/components/shared/subscriptions/SubscriptionSearch.tsx (client reference/proxy)");
;
__turbopack_context__.n(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$components$2f$shared$2f$subscriptions$2f$SubscriptionSearch$2e$tsx__$28$client__reference$2f$proxy$29$__);
}}),
"[project]/app/components/shared/subscriptions/SubscriptionPagination.tsx (client reference/proxy) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server-edge.js [app-rsc] (ecmascript)");
;
const __TURBOPACK__default__export__ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call the default export of [project]/app/components/shared/subscriptions/SubscriptionPagination.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/app/components/shared/subscriptions/SubscriptionPagination.tsx <module evaluation>", "default");
}}),
"[project]/app/components/shared/subscriptions/SubscriptionPagination.tsx (client reference/proxy)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server-edge.js [app-rsc] (ecmascript)");
;
const __TURBOPACK__default__export__ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call the default export of [project]/app/components/shared/subscriptions/SubscriptionPagination.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/app/components/shared/subscriptions/SubscriptionPagination.tsx", "default");
}}),
"[project]/app/components/shared/subscriptions/SubscriptionPagination.tsx [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$components$2f$shared$2f$subscriptions$2f$SubscriptionPagination$2e$tsx__$28$client__reference$2f$proxy$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/app/components/shared/subscriptions/SubscriptionPagination.tsx (client reference/proxy) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$components$2f$shared$2f$subscriptions$2f$SubscriptionPagination$2e$tsx__$28$client__reference$2f$proxy$29$__ = __turbopack_context__.i("[project]/app/components/shared/subscriptions/SubscriptionPagination.tsx (client reference/proxy)");
;
__turbopack_context__.n(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$components$2f$shared$2f$subscriptions$2f$SubscriptionPagination$2e$tsx__$28$client__reference$2f$proxy$29$__);
}}),
"[project]/app/components/shared/subscriptions/SubscriptionList.tsx (client reference/proxy) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server-edge.js [app-rsc] (ecmascript)");
;
const __TURBOPACK__default__export__ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call the default export of [project]/app/components/shared/subscriptions/SubscriptionList.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/app/components/shared/subscriptions/SubscriptionList.tsx <module evaluation>", "default");
}}),
"[project]/app/components/shared/subscriptions/SubscriptionList.tsx (client reference/proxy)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server-edge.js [app-rsc] (ecmascript)");
;
const __TURBOPACK__default__export__ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call the default export of [project]/app/components/shared/subscriptions/SubscriptionList.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/app/components/shared/subscriptions/SubscriptionList.tsx", "default");
}}),
"[project]/app/components/shared/subscriptions/SubscriptionList.tsx [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$components$2f$shared$2f$subscriptions$2f$SubscriptionList$2e$tsx__$28$client__reference$2f$proxy$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/app/components/shared/subscriptions/SubscriptionList.tsx (client reference/proxy) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$components$2f$shared$2f$subscriptions$2f$SubscriptionList$2e$tsx__$28$client__reference$2f$proxy$29$__ = __turbopack_context__.i("[project]/app/components/shared/subscriptions/SubscriptionList.tsx (client reference/proxy)");
;
__turbopack_context__.n(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$components$2f$shared$2f$subscriptions$2f$SubscriptionList$2e$tsx__$28$client__reference$2f$proxy$29$__);
}}),
"[project]/app/components/shared/subscriptions/index.ts [app-rsc] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// Export all shared subscription components
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$components$2f$shared$2f$subscriptions$2f$SubscriptionCard$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/components/shared/subscriptions/SubscriptionCard.tsx [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$components$2f$shared$2f$subscriptions$2f$SubscriptionCardSkeleton$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/components/shared/subscriptions/SubscriptionCardSkeleton.tsx [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$components$2f$shared$2f$subscriptions$2f$SubscriptionSearch$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/components/shared/subscriptions/SubscriptionSearch.tsx [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$components$2f$shared$2f$subscriptions$2f$SubscriptionPagination$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/components/shared/subscriptions/SubscriptionPagination.tsx [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$components$2f$shared$2f$subscriptions$2f$SubscriptionList$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/components/shared/subscriptions/SubscriptionList.tsx [app-rsc] (ecmascript)");
;
;
;
;
;
}}),
"[project]/app/components/shared/subscriptions/index.ts [app-rsc] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$components$2f$shared$2f$subscriptions$2f$SubscriptionCard$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/components/shared/subscriptions/SubscriptionCard.tsx [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$components$2f$shared$2f$subscriptions$2f$SubscriptionCardSkeleton$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/components/shared/subscriptions/SubscriptionCardSkeleton.tsx [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$components$2f$shared$2f$subscriptions$2f$SubscriptionSearch$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/components/shared/subscriptions/SubscriptionSearch.tsx [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$components$2f$shared$2f$subscriptions$2f$SubscriptionPagination$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/components/shared/subscriptions/SubscriptionPagination.tsx [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$components$2f$shared$2f$subscriptions$2f$SubscriptionList$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/components/shared/subscriptions/SubscriptionList.tsx [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$components$2f$shared$2f$subscriptions$2f$index$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/app/components/shared/subscriptions/index.ts [app-rsc] (ecmascript) <locals>");
}}),
"[project]/app/(dashboard)/dashboard/business/subscriptions/page.tsx [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>BusinessSubscriptionsPage),
    "metadata": (()=>metadata)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/supabase/server.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$api$2f$navigation$2e$react$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/next/dist/api/navigation.react-server.js [app-rsc] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$components$2f$navigation$2e$react$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/client/components/navigation.react-server.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$alert$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/ui/alert.tsx [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$skeleton$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/ui/skeleton.tsx [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$triangle$2d$alert$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__default__as__AlertTriangle$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/triangle-alert.js [app-rsc] (ecmascript) <export default as AlertTriangle>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$users$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__default__as__Users$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/users.js [app-rsc] (ecmascript) <export default as Users>");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$dashboard$292f$dashboard$2f$business$2f$subscriptions$2f$components$2f$BusinessSubscriptionsPageClient$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/(dashboard)/dashboard/business/subscriptions/components/BusinessSubscriptionsPageClient.tsx [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$components$2f$shared$2f$subscriptions$2f$index$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/app/components/shared/subscriptions/index.ts [app-rsc] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$components$2f$shared$2f$subscriptions$2f$SubscriptionCardSkeleton$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/components/shared/subscriptions/SubscriptionCardSkeleton.tsx [app-rsc] (ecmascript)");
// Import the fetch functions
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$dashboard$292f$dashboard$2f$business$2f$subscriptions$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/(dashboard)/dashboard/business/subscriptions/actions.ts [app-rsc] (ecmascript)");
;
;
;
;
;
;
;
;
;
;
const metadata = {
    title: "Subscriptions",
    robots: "noindex, nofollow"
};
async function BusinessSubscriptionsPage({ searchParams }) {
    // Properly await searchParams to fix the error
    const { tab, search, page: pageParam } = await searchParams;
    const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])();
    const page = pageParam ? parseInt(pageParam) : 1;
    const searchTerm = search || "";
    const activeTab = tab || "subscribers";
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$components$2f$navigation$2e$react$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["redirect"])('/login?message=Please log in to view your subscriptions.');
    }
    try {
        // Always fetch both counts for tab display, but only fetch detailed data for active tab
        const [subscribersCountResult, followingCountResult] = await Promise.all([
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$dashboard$292f$dashboard$2f$business$2f$subscriptions$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fetchBusinessSubscribers"])(user.id, 1, 1),
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$dashboard$292f$dashboard$2f$business$2f$subscriptions$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fetchBusinessFollowing"])(user.id, 1, 1, "")
        ]);
        // Get the total counts
        const subscribersCount = subscribersCountResult.totalCount;
        const followingCount = followingCountResult.totalCount;
        // Fetch detailed data based on active tab
        let subscribersResult = null;
        let followingResult = null;
        if (activeTab === "subscribers") {
            subscribersResult = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$dashboard$292f$dashboard$2f$business$2f$subscriptions$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fetchBusinessSubscribers"])(user.id, page, 10 // 10 items per page
            );
        } else {
            followingResult = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$dashboard$292f$dashboard$2f$business$2f$subscriptions$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fetchBusinessFollowing"])(user.id, page, 10, searchTerm);
        }
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "space-y-8",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Suspense"], {
                fallback: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "space-y-8",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex flex-col sm:flex-row sm:items-center gap-4 sm:gap-6",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "p-3 rounded-xl bg-muted hidden sm:block",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$users$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__default__as__Users$3e$__["Users"], {
                                        className: "w-6 h-6 text-foreground"
                                    }, void 0, false, {
                                        fileName: "[project]/app/(dashboard)/dashboard/business/subscriptions/page.tsx",
                                        lineNumber: 79,
                                        columnNumber: 17
                                    }, void 0)
                                }, void 0, false, {
                                    fileName: "[project]/app/(dashboard)/dashboard/business/subscriptions/page.tsx",
                                    lineNumber: 78,
                                    columnNumber: 15
                                }, void 0),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "flex-1",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$skeleton$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Skeleton"], {
                                            className: "h-8 w-48 mb-2"
                                        }, void 0, false, {
                                            fileName: "[project]/app/(dashboard)/dashboard/business/subscriptions/page.tsx",
                                            lineNumber: 82,
                                            columnNumber: 17
                                        }, void 0),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$skeleton$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Skeleton"], {
                                            className: "h-4 w-64"
                                        }, void 0, false, {
                                            fileName: "[project]/app/(dashboard)/dashboard/business/subscriptions/page.tsx",
                                            lineNumber: 83,
                                            columnNumber: 17
                                        }, void 0)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/app/(dashboard)/dashboard/business/subscriptions/page.tsx",
                                    lineNumber: 81,
                                    columnNumber: 15
                                }, void 0)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/app/(dashboard)/dashboard/business/subscriptions/page.tsx",
                            lineNumber: 77,
                            columnNumber: 13
                        }, void 0),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex space-x-1 border-b border-border",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$skeleton$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Skeleton"], {
                                    className: "h-10 w-32 rounded-t-md"
                                }, void 0, false, {
                                    fileName: "[project]/app/(dashboard)/dashboard/business/subscriptions/page.tsx",
                                    lineNumber: 89,
                                    columnNumber: 15
                                }, void 0),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$skeleton$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Skeleton"], {
                                    className: "h-10 w-32 rounded-t-md"
                                }, void 0, false, {
                                    fileName: "[project]/app/(dashboard)/dashboard/business/subscriptions/page.tsx",
                                    lineNumber: 90,
                                    columnNumber: 15
                                }, void 0)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/app/(dashboard)/dashboard/business/subscriptions/page.tsx",
                            lineNumber: 88,
                            columnNumber: 13
                        }, void 0),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$skeleton$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Skeleton"], {
                            className: "h-10 w-full rounded-md"
                        }, void 0, false, {
                            fileName: "[project]/app/(dashboard)/dashboard/business/subscriptions/page.tsx",
                            lineNumber: 94,
                            columnNumber: 13
                        }, void 0),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$components$2f$shared$2f$subscriptions$2f$SubscriptionCardSkeleton$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["SubscriptionListSkeleton"], {}, void 0, false, {
                            fileName: "[project]/app/(dashboard)/dashboard/business/subscriptions/page.tsx",
                            lineNumber: 97,
                            columnNumber: 13
                        }, void 0)
                    ]
                }, void 0, true, {
                    fileName: "[project]/app/(dashboard)/dashboard/business/subscriptions/page.tsx",
                    lineNumber: 75,
                    columnNumber: 11
                }, void 0),
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$dashboard$292f$dashboard$2f$business$2f$subscriptions$2f$components$2f$BusinessSubscriptionsPageClient$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"], {
                    initialSubscribers: subscribersResult?.items || [],
                    subscribersCount: subscribersCount,
                    subscribersCurrentPage: subscribersResult?.currentPage || 1,
                    initialFollowing: followingResult?.items || [],
                    followingCount: followingCount,
                    followingCurrentPage: followingResult?.currentPage || 1,
                    searchTerm: searchTerm,
                    activeTab: activeTab
                }, void 0, false, {
                    fileName: "[project]/app/(dashboard)/dashboard/business/subscriptions/page.tsx",
                    lineNumber: 100,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/app/(dashboard)/dashboard/business/subscriptions/page.tsx",
                lineNumber: 74,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/app/(dashboard)/dashboard/business/subscriptions/page.tsx",
            lineNumber: 73,
            columnNumber: 7
        }, this);
    } catch (_error) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$alert$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Alert"], {
            variant: "destructive",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$triangle$2d$alert$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__default__as__AlertTriangle$3e$__["AlertTriangle"], {
                    className: "h-4 w-4"
                }, void 0, false, {
                    fileName: "[project]/app/(dashboard)/dashboard/business/subscriptions/page.tsx",
                    lineNumber: 116,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$alert$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["AlertTitle"], {
                    children: "Error"
                }, void 0, false, {
                    fileName: "[project]/app/(dashboard)/dashboard/business/subscriptions/page.tsx",
                    lineNumber: 117,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$alert$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["AlertDescription"], {
                    children: "Could not load subscription data. Please try again later."
                }, void 0, false, {
                    fileName: "[project]/app/(dashboard)/dashboard/business/subscriptions/page.tsx",
                    lineNumber: 118,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/app/(dashboard)/dashboard/business/subscriptions/page.tsx",
            lineNumber: 115,
            columnNumber: 7
        }, this);
    }
}
}}),
"[project]/app/(dashboard)/dashboard/business/subscriptions/page.tsx [app-rsc] (ecmascript, Next.js server component)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.n(__turbopack_context__.i("[project]/app/(dashboard)/dashboard/business/subscriptions/page.tsx [app-rsc] (ecmascript)"));
}}),

};

//# sourceMappingURL=_6990599f._.js.map