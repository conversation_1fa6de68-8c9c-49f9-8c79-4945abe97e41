(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/utils/supabase/server.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "createClient": (()=>createClient)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$ssr$2f$dist$2f$module$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@supabase/ssr/dist/module/index.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$ssr$2f$dist$2f$module$2f$createServerClient$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@supabase/ssr/dist/module/createServerClient.js [app-client] (ecmascript)");
;
async function createClient() {
    const supabaseUrl = ("TURBOPACK compile-time value", "https://rnjolcoecogzgglnblqn.supabase.co");
    const supabaseAnonKey = ("TURBOPACK compile-time value", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJuam9sY29lY29nemdnbG5ibHFuIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDMwNTIwNTYsImV4cCI6MjA1ODYyODA1Nn0.k8DuvOrrKQlvxGb5qD78_vXDqIRkmk7ZRUj1Hb5PL4o");
    if ("TURBOPACK compile-time falsy", 0) {
        "TURBOPACK unreachable";
    }
    // Check if we're in a test environment
    let headersList = null;
    let cookieStore = null;
    try {
        // Dynamically import next/headers to avoid issues in edge runtime
        const { headers, cookies } = await __turbopack_context__.r("[project]/node_modules/next/headers.js [app-client] (ecmascript, async loader)")(__turbopack_context__.i);
        headersList = await headers();
        cookieStore = await cookies();
    } catch (error) {
        // If next/headers is not available (e.g., in edge runtime), continue without it
        console.warn('next/headers not available in this context, using fallback');
    }
    const isTestEnvironment = ("TURBOPACK compile-time value", "development") === 'test' || __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].env.PLAYWRIGHT_TESTING === 'true' || headersList && headersList.get('x-playwright-testing') === 'true';
    if (isTestEnvironment && headersList) {
        // Return a mocked Supabase client for testing
        return createMockSupabaseClient(headersList);
    }
    // If cookies are not available, create a basic server client
    if (!cookieStore) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$ssr$2f$dist$2f$module$2f$createServerClient$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createServerClient"])(supabaseUrl, supabaseAnonKey, {
            cookies: {
                getAll () {
                    return [];
                },
                setAll () {
                // No-op when cookies are not available
                }
            }
        });
    }
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$ssr$2f$dist$2f$module$2f$createServerClient$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createServerClient"])(supabaseUrl, supabaseAnonKey, {
        cookies: {
            async getAll () {
                return await cookieStore.getAll();
            },
            async setAll (cookiesToSet) {
                try {
                    for (const { name, value, options } of cookiesToSet){
                        await cookieStore.set(name, value, options);
                    }
                } catch  {
                // The `setAll` method was called from a Server Component.
                // This can be ignored if you have middleware refreshing
                // user sessions.
                }
            }
        }
    });
}
function createMockSupabaseClient(headersList) {
    const testAuthState = headersList.get('x-test-auth-state');
    const testUserType = headersList.get('x-test-user-type');
    const testHasProfile = testUserType === 'customer' || testUserType === 'business';
    const testBusinessSlug = headersList.get('x-test-business-slug');
    const testPlanId = headersList.get('x-test-plan-id') || 'free';
    return {
        auth: {
            getUser: async ()=>{
                if (testAuthState === 'authenticated') {
                    return {
                        data: {
                            user: {
                                id: 'test-user-id',
                                email: '<EMAIL>'
                            }
                        },
                        error: null
                    };
                }
                return {
                    data: {
                        user: null
                    },
                    error: {
                        message: 'Unauthorized',
                        name: 'AuthApiError',
                        status: 401
                    }
                };
            },
            getSession: async ()=>{
                if (testAuthState === 'authenticated') {
                    return {
                        data: {
                            session: {
                                user: {
                                    id: 'test-user-id',
                                    email: '<EMAIL>'
                                }
                            }
                        },
                        error: null
                    };
                }
                return {
                    data: {
                        session: null
                    },
                    error: {
                        message: 'Unauthorized',
                        name: 'AuthApiError',
                        status: 401
                    }
                };
            },
            signInWithOtp: async ()=>({
                    data: {
                        user: null,
                        session: null
                    },
                    error: null
                }),
            signOut: async ()=>({
                    error: null
                })
        },
        from: (table)=>createMockQueryBuilder(table, testUserType, testHasProfile, testBusinessSlug, testPlanId)
    };
}
function createMockQueryBuilder(table, testUserType, testHasProfile, testBusinessSlug, testPlanId) {
    const getMockData = ()=>getMockTableData(table, testUserType, testHasProfile, testBusinessSlug, testPlanId);
    const createChainableMock = (data)=>({
            select: (_columns)=>createChainableMock(data),
            eq: (_column, _value)=>createChainableMock(data),
            neq: (_column, _value)=>createChainableMock(data),
            gt: (_column, _value)=>createChainableMock(data),
            gte: (_column, _value)=>createChainableMock(data),
            lt: (_column, _value)=>createChainableMock(data),
            lte: (_column, _value)=>createChainableMock(data),
            like: (_column, _pattern)=>createChainableMock(data),
            ilike: (_column, _pattern)=>createChainableMock(data),
            is: (_column, _value)=>createChainableMock(data),
            in: (_column, _values)=>createChainableMock(data),
            contains: (_column, _value)=>createChainableMock(data),
            containedBy: (_column, _value)=>createChainableMock(data),
            rangeGt: (_column, _value)=>createChainableMock(data),
            rangeGte: (_column, _value)=>createChainableMock(data),
            rangeLt: (_column, _value)=>createChainableMock(data),
            rangeLte: (_column, _value)=>createChainableMock(data),
            rangeAdjacent: (_column, _value)=>createChainableMock(data),
            overlaps: (_column, _value)=>createChainableMock(data),
            textSearch: (_column, _query)=>createChainableMock(data),
            match: (_query)=>createChainableMock(data),
            not: (_column, _operator, _value)=>createChainableMock(data),
            or: (_filters)=>createChainableMock(data),
            filter: (_column, _operator, _value)=>createChainableMock(data),
            order: (_column, _options)=>createChainableMock(data),
            limit: (_count, _options)=>createChainableMock(data),
            range: (_from, _to, _options)=>createChainableMock(data),
            abortSignal: (_signal)=>createChainableMock(data),
            single: async ()=>getMockData(),
            maybeSingle: async ()=>getMockData(),
            then: async (callback)=>{
                const result = getMockData();
                return callback ? callback(result) : result;
            },
            data: data || [],
            error: null,
            count: data ? data.length : 0,
            status: 200,
            statusText: 'OK'
        });
    return {
        select: (_columns)=>createChainableMock(),
        insert: (data)=>({
                select: (_columns)=>({
                        single: async ()=>({
                                data: Array.isArray(data) ? data[0] : data,
                                error: null
                            }),
                        maybeSingle: async ()=>({
                                data: Array.isArray(data) ? data[0] : data,
                                error: null
                            }),
                        then: async (_callback)=>{
                            const result = {
                                data: Array.isArray(data) ? data : [
                                    data
                                ],
                                error: null
                            };
                            return _callback ? _callback(result) : result;
                        }
                    }),
                then: async (_callback)=>{
                    const result = {
                        data: Array.isArray(data) ? data : [
                            data
                        ],
                        error: null
                    };
                    return _callback ? _callback(result) : result;
                }
            }),
        update: (data)=>createChainableMock(data),
        upsert: (data)=>createChainableMock(data),
        delete: ()=>createChainableMock(),
        rpc: (_functionName, _params)=>createChainableMock()
    };
}
/**
 * Helper function to get mock table data based on test state
 */ function getMockTableData(table, testUserType, testHasProfile, testBusinessSlug, testPlanId) {
    if (table === 'customer_profiles') {
        const hasCustomerProfile = testHasProfile && testUserType === 'customer';
        return {
            data: hasCustomerProfile ? {
                id: 'test-user-id',
                name: 'Test Customer',
                avatar_url: null,
                phone: '+1234567890',
                email: '<EMAIL>',
                address: 'Test Address',
                city: 'Test City',
                state: 'Test State',
                pincode: '123456'
            } : null,
            error: null
        };
    }
    if (table === 'business_profiles') {
        const hasBusinessProfile = testHasProfile && testUserType === 'business';
        return {
            data: hasBusinessProfile ? {
                id: 'test-user-id',
                business_slug: testBusinessSlug || null,
                trial_end_date: null,
                has_active_subscription: true,
                business_name: 'Test Business',
                city_slug: 'test-city',
                state_slug: 'test-state',
                locality_slug: 'test-locality',
                pincode: '123456',
                business_description: 'Test business description',
                business_category: 'retail',
                phone: '+1234567890',
                email: '<EMAIL>',
                website: 'https://testbusiness.com'
            } : null,
            error: null
        };
    }
    if (table === 'payment_subscriptions') {
        return {
            data: testUserType === 'business' ? {
                id: 'test-subscription-id',
                plan_id: testPlanId,
                business_profile_id: 'test-user-id',
                status: 'active',
                created_at: '2024-01-01T00:00:00Z'
            } : null,
            error: null
        };
    }
    if (table === 'products') {
        return {
            data: testUserType === 'business' ? [
                {
                    id: 'test-product-1',
                    name: 'Test Product 1',
                    price: 100,
                    business_profile_id: 'test-user-id',
                    available: true
                },
                {
                    id: 'test-product-2',
                    name: 'Test Product 2',
                    price: 200,
                    business_profile_id: 'test-user-id',
                    available: false
                }
            ] : [],
            error: null
        };
    }
    // Default return for unknown tables
    return {
        data: null,
        error: null
    };
}
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/lib/utils/storage-paths.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/**
 * Scalable Storage Path Utilities
 *
 * This module provides utilities for generating scalable storage paths
 * that can handle billions of users efficiently using hash-based distribution.
 */ /**
 * Generate scalable user path using hash-based distribution
 *
 * @param userId - The user's UUID
 * @returns Scalable path: users/{prefix}/{midfix}/{userId}
 *
 * Example:
 * - Input: "a1b2c3d4-e5f6-7890-abcd-ef1234567890"
 * - Output: "users/a1/b2/a1b2c3d4-e5f6-7890-abcd-ef1234567890"
 */ __turbopack_context__.s({
    "PathValidator": (()=>PathValidator),
    "StorageAnalytics": (()=>StorageAnalytics),
    "getCustomAdImagePath": (()=>getCustomAdImagePath),
    "getCustomHeaderImagePath": (()=>getCustomHeaderImagePath),
    "getCustomerAvatarPath": (()=>getCustomerAvatarPath),
    "getCustomerPostImagePath": (()=>getCustomerPostImagePath),
    "getGalleryImagePath": (()=>getGalleryImagePath),
    "getPostFolderPath": (()=>getPostFolderPath),
    "getPostImagePath": (()=>getPostImagePath),
    "getProductBaseImagePath": (()=>getProductBaseImagePath),
    "getProductImagePath": (()=>getProductImagePath),
    "getProductVariantImagePath": (()=>getProductVariantImagePath),
    "getProfileImagePath": (()=>getProfileImagePath),
    "getScalableUserPath": (()=>getScalableUserPath),
    "getThemeSpecificHeaderImagePath": (()=>getThemeSpecificHeaderImagePath)
});
function getScalableUserPath(userId) {
    if (!userId || typeof userId !== 'string') {
        throw new Error(`Invalid userId: expected string, got ${typeof userId}. Value: ${userId}`);
    }
    if (userId.length < 4) {
        throw new Error(`Invalid userId: must be at least 4 characters long. Got: ${userId}`);
    }
    const prefix = userId.substring(0, 2).toLowerCase();
    const midfix = userId.substring(2, 4).toLowerCase();
    return `users/${prefix}/${midfix}/${userId}`;
}
function getProfileImagePath(userId, timestamp) {
    const userPath = getScalableUserPath(userId);
    return `${userPath}/profile/logo_${timestamp}.webp`;
}
function getProductImagePath(userId, productId, imageIndex, timestamp) {
    const userPath = getScalableUserPath(userId);
    return `${userPath}/products/${productId}/image_${imageIndex}_${timestamp}.webp`;
}
function getProductBaseImagePath(userId, productId, imageIndex, timestamp) {
    const userPath = getScalableUserPath(userId);
    return `${userPath}/products/${productId}/base/image_${imageIndex}_${timestamp}.webp`;
}
function getProductVariantImagePath(userId, productId, variantId, imageIndex, timestamp) {
    const userPath = getScalableUserPath(userId);
    return `${userPath}/products/${productId}/${variantId}/image_${imageIndex}_${timestamp}.webp`;
}
function getGalleryImagePath(userId, timestamp) {
    const userPath = getScalableUserPath(userId);
    return `${userPath}/gallery/gallery_${timestamp}.webp`;
}
function getPostImagePath(userId, postId, imageIndex, timestamp, createdAt) {
    const userPath = getScalableUserPath(userId);
    // Use post creation date if provided, otherwise use current date (for backward compatibility)
    const dateToUse = createdAt ? new Date(createdAt) : new Date();
    const year = dateToUse.getFullYear();
    const month = String(dateToUse.getMonth() + 1).padStart(2, '0');
    return `${userPath}/posts/${year}/${month}/${postId}/image_${imageIndex}_${timestamp}.webp`;
}
function getPostFolderPath(userId, postId, createdAt) {
    const userPath = getScalableUserPath(userId);
    const postDate = new Date(createdAt);
    const year = postDate.getFullYear();
    const month = String(postDate.getMonth() + 1).padStart(2, '0');
    return `${userPath}/posts/${year}/${month}/${postId}`;
}
function getCustomerAvatarPath(userId, timestamp) {
    const userPath = getScalableUserPath(userId);
    return `${userPath}/avatar/avatar_${timestamp}.webp`;
}
function getCustomerPostImagePath(userId, postId, imageIndex, timestamp, createdAt) {
    const userPath = getScalableUserPath(userId);
    // Use post creation date if provided, otherwise use current date (for backward compatibility)
    const dateToUse = createdAt ? new Date(createdAt) : new Date();
    const year = dateToUse.getFullYear();
    const month = String(dateToUse.getMonth() + 1).padStart(2, '0');
    return `${userPath}/posts/${year}/${month}/${postId}/image_${imageIndex}_${timestamp}.webp`;
}
function getCustomAdImagePath(userId, timestamp) {
    const userPath = getScalableUserPath(userId);
    return `${userPath}/ads/custom_ad_${timestamp}.webp`;
}
function getCustomHeaderImagePath(userId, timestamp) {
    const userPath = getScalableUserPath(userId);
    return `${userPath}/branding/header_${timestamp}.webp`;
}
function getThemeSpecificHeaderImagePath(userId, timestamp, theme) {
    const userPath = getScalableUserPath(userId);
    return `${userPath}/branding/header_${theme}_${timestamp}.webp`;
}
class PathValidator {
    /**
   * Validate if a path follows the new scalable structure
   */ static isScalablePath(path) {
        return path.startsWith('users/') && path.split('/').length >= 4;
    }
    /**
   * Extract user ID from scalable path
   */ static extractUserIdFromPath(path) {
        if (!this.isScalablePath(path)) {
            return null;
        }
        const parts = path.split('/');
        return parts[3]; // users/{prefix}/{midfix}/{userId}/...
    }
    /**
   * Validate path structure integrity
   */ static validatePathStructure(userId, path) {
        const expectedUserPath = getScalableUserPath(userId);
        return path.startsWith(expectedUserPath);
    }
}
class StorageAnalytics {
    /**
   * Get storage distribution info for monitoring
   */ static getDistributionInfo(userId) {
        const prefix = userId.substring(0, 2).toLowerCase();
        const midfix = userId.substring(2, 4).toLowerCase();
        // Estimate number of users in same bucket (assuming even distribution)
        const totalBuckets = 16 * 16 * 16 * 16; // 65,536 buckets
        const estimatedPeers = Math.floor(1000000 / totalBuckets); // Estimate for 1M users
        return {
            prefix,
            midfix,
            bucket: `${prefix}/${midfix}`,
            estimatedPeers
        };
    }
}
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/lib/actions/shared/delete-customer-post-media.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "deleteCustomerPostMedia": (()=>deleteCustomerPostMedia)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/supabase/server.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2f$storage$2d$paths$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/utils/storage-paths.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/supabase/constants.ts [app-client] (ecmascript)");
;
;
;
async function deleteCustomerPostMedia(userId, postId, createdAt) {
    // Use admin client for storage operations to bypass RLS
    const adminSupabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createClient"])();
    try {
        const bucketName = __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["BUCKETS"].CUSTOMERS;
        const postFolderPath = (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2f$storage$2d$paths$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getPostFolderPath"])(userId, postId, createdAt);
        // List all files in the post folder
        const { data: files, error: listError } = await adminSupabase.storage.from(bucketName).list(postFolderPath, {
            limit: 1000,
            sortBy: {
                column: 'name',
                order: 'asc'
            }
        });
        if (listError) {
            console.error("Error listing customer post folder contents:", listError);
            return {
                success: false,
                error: `Failed to list customer post folder: ${listError.message}`
            };
        }
        if (!files || files.length === 0) {
            // No files to delete, consider it successful
            return {
                success: true
            };
        }
        // Create full paths for all files in the folder
        const filePaths = files.map((file)=>`${postFolderPath}/${file.name}`);
        // Delete all files in the post folder using admin client
        // In object storage, deleting all files effectively removes the folder
        const { error: deleteError } = await adminSupabase.storage.from(bucketName).remove(filePaths);
        if (deleteError) {
            console.error("Error deleting customer post folder contents:", deleteError);
            return {
                success: false,
                error: `Failed to delete customer post folder: ${deleteError.message}`
            };
        }
        return {
            success: true
        };
    } catch (error) {
        console.error("Error in deleteCustomerPostMedia:", error);
        return {
            success: false,
            error: "An unexpected error occurred while deleting customer post folder."
        };
    }
}
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=_28823ebb._.js.map