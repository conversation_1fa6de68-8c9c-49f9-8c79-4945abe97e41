module.exports = {

"[project]/app/auth/actions.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ [{"0027aa970a57d5cbd11af21933b24fca73bbd99631":"signOutUser"},"",""] */ __turbopack_context__.s({
    "signOutUser": (()=>signOutUser)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/server-reference.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$app$2d$render$2f$encryption$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/app-render/encryption.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/supabase/server.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$api$2f$navigation$2e$react$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/next/dist/api/navigation.react-server.js [app-rsc] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$components$2f$navigation$2e$react$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/client/components/navigation.react-server.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-validate.js [app-rsc] (ecmascript)");
;
;
;
;
async function signOutUser() {
    const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])();
    try {
        const { error: _error } = await supabase.auth.signOut();
        // Note: Sign out errors are typically not critical for user experience
        // The user will be redirected to login regardless
        // Explicitly clear auth cookies to ensure logout
        const cookieStore = await __turbopack_context__.r("[project]/node_modules/next/headers.js [app-rsc] (ecmascript, async loader)")(__turbopack_context__.i).then((m)=>m.cookies());
        const cookiesToClear = [
            "sb-access-token",
            "sb-refresh-token"
        ];
        for (const cookieName of cookiesToClear){
            try {
                cookieStore.set(cookieName, "", {
                    expires: new Date(0),
                    maxAge: -1
                });
            } catch  {
            // Cookie clearing errors are not critical for sign out
            // Continue with the sign out process
            }
        }
    } catch  {
    // Even if sign out fails, redirect to login for security
    // User will be treated as logged out
    }
    // Redirect to login with a flag to prevent middleware redirect loop
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$components$2f$navigation$2e$react$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["redirect"])("/login?logged_out=true");
}
;
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ensureServerEntryExports"])([
    signOutUser
]);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(signOutUser, "0027aa970a57d5cbd11af21933b24fca73bbd99631", null);
}}),
"[project]/lib/utils/addressValidation.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Customer address validation utility
 * Checks if customer has complete address information
 */ __turbopack_context__.s({
    "getAddressValidationMessage": (()=>getAddressValidationMessage),
    "getMissingAddressFields": (()=>getMissingAddressFields),
    "getMissingProfileFields": (()=>getMissingProfileFields),
    "getProfileValidationMessage": (()=>getProfileValidationMessage),
    "isCustomerAddressComplete": (()=>isCustomerAddressComplete),
    "isCustomerNameComplete": (()=>isCustomerNameComplete),
    "isCustomerProfileComplete": (()=>isCustomerProfileComplete)
});
function isCustomerAddressComplete(addressData) {
    const { pincode, state, city, locality } = addressData;
    // Check if required fields are present and not empty
    return !!(pincode && pincode.trim() !== '' && state && state.trim() !== '' && city && city.trim() !== '' && locality && locality.trim() !== '');
}
function getMissingAddressFields(addressData) {
    const missing = [];
    if (!addressData.pincode || addressData.pincode.trim() === '') {
        missing.push('pincode');
    }
    if (!addressData.state || addressData.state.trim() === '') {
        missing.push('state');
    }
    if (!addressData.city || addressData.city.trim() === '') {
        missing.push('city');
    }
    if (!addressData.locality || addressData.locality.trim() === '') {
        missing.push('locality');
    }
    return missing;
}
function getAddressValidationMessage(missingFields) {
    if (missingFields.length === 0) {
        return '';
    }
    const fieldNames = missingFields.map((field)=>{
        switch(field){
            case 'pincode':
                return 'Pincode';
            case 'state':
                return 'State';
            case 'city':
                return 'City';
            case 'locality':
                return 'Locality';
            default:
                return field;
        }
    });
    if (fieldNames.length === 1) {
        return `Please update your ${fieldNames[0]} in your profile.`;
    } else if (fieldNames.length === 2) {
        return `Please update your ${fieldNames.join(' and ')} in your profile.`;
    } else {
        const lastField = fieldNames.pop();
        return `Please update your ${fieldNames.join(', ')}, and ${lastField} in your profile.`;
    }
}
function isCustomerNameComplete(name) {
    return !!(name && name.trim() !== '');
}
function isCustomerProfileComplete(profileData) {
    return isCustomerNameComplete(profileData.name) && isCustomerAddressComplete(profileData);
}
function getMissingProfileFields(profileData) {
    const missing = [];
    // Check name
    if (!isCustomerNameComplete(profileData.name)) {
        missing.push('name');
    }
    // Check address fields
    const missingAddressFields = getMissingAddressFields(profileData);
    missing.push(...missingAddressFields);
    return missing;
}
function getProfileValidationMessage(missingFields) {
    if (missingFields.length === 0) {
        return '';
    }
    const fieldNames = missingFields.map((field)=>{
        switch(field){
            case 'name':
                return 'Name';
            case 'pincode':
                return 'Pincode';
            case 'state':
                return 'State';
            case 'city':
                return 'City';
            case 'locality':
                return 'Locality';
            default:
                return field;
        }
    });
    if (fieldNames.length === 1) {
        return `Please update your ${fieldNames[0]} in your profile.`;
    } else if (fieldNames.length === 2) {
        return `Please update your ${fieldNames.join(' and ')} in your profile.`;
    } else {
        const lastField = fieldNames.pop();
        return `Please update your ${fieldNames.join(', ')}, and ${lastField} in your profile.`;
    }
}
}}),
"[project]/lib/actions/customerProfiles/addressValidation.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ [{"400df42a7734311425e09521ebcc4d895b5368c63a":"requireCompleteName","40746d48fecf14ef44441f42ba6d4270f14c867a68":"getCustomerAddressData","407770ce7befdbdc280da755c304531d3f7ea6b5dd":"validateCustomerName","40c05b55f995304a112e3e7557409d074df1c34bf2":"requireCompleteAddress","40d8cab922554d01cea4f763d816fb4734270ab290":"validateCustomerAddress","60510e8a33e98dfcb8dbb5099888e59125201d5a12":"requireCompleteProfile"},"",""] */ __turbopack_context__.s({
    "getCustomerAddressData": (()=>getCustomerAddressData),
    "requireCompleteAddress": (()=>requireCompleteAddress),
    "requireCompleteName": (()=>requireCompleteName),
    "requireCompleteProfile": (()=>requireCompleteProfile),
    "validateCustomerAddress": (()=>validateCustomerAddress),
    "validateCustomerName": (()=>validateCustomerName)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/server-reference.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$app$2d$render$2f$encryption$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/app-render/encryption.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/supabase/server.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2f$addressValidation$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/utils/addressValidation.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$api$2f$navigation$2e$react$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/next/dist/api/navigation.react-server.js [app-rsc] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$components$2f$navigation$2e$react$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/client/components/navigation.react-server.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-validate.js [app-rsc] (ecmascript)");
;
;
;
;
;
async function validateCustomerAddress(userId) {
    const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])();
    try {
        // Fetch customer address data
        const { data: profile, error } = await supabase.from('customer_profiles').select('pincode, state, city, locality, address').eq('id', userId).single();
        if (error) {
            console.error('Error fetching customer profile for address validation:', error);
            // If we can't fetch the profile, assume invalid and redirect
            return {
                isValid: false,
                message: 'Unable to verify your address information. Please update your profile.',
                redirectUrl: '/dashboard/customer/profile?message=Please update your address information'
            };
        }
        const addressData = {
            pincode: profile?.pincode,
            state: profile?.state,
            city: profile?.city,
            locality: profile?.locality,
            address: profile?.address
        };
        const isValid = (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2f$addressValidation$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isCustomerAddressComplete"])(addressData);
        if (!isValid) {
            const missingFields = (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2f$addressValidation$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getMissingAddressFields"])(addressData);
            const message = (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2f$addressValidation$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getAddressValidationMessage"])(missingFields);
            const redirectUrl = `/dashboard/customer/profile?message=${encodeURIComponent(message)}`;
            return {
                isValid: false,
                missingFields,
                message,
                redirectUrl
            };
        }
        return {
            isValid: true
        };
    } catch (error) {
        console.error('Unexpected error during address validation:', error);
        return {
            isValid: false,
            message: 'An error occurred while validating your address. Please update your profile.',
            redirectUrl: '/dashboard/customer/profile?message=Please update your address information'
        };
    }
}
async function requireCompleteAddress(userId) {
    const validation = await validateCustomerAddress(userId);
    if (!validation.isValid && validation.redirectUrl) {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$components$2f$navigation$2e$react$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["redirect"])(validation.redirectUrl);
    }
}
async function validateCustomerName(userId) {
    const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])();
    try {
        // Fetch customer name data
        const { data: profile, error } = await supabase.from('customer_profiles').select('name').eq('id', userId).single();
        if (error) {
            console.error('Error fetching customer profile for name validation:', error);
            // If we can't fetch the profile, assume invalid and redirect
            return {
                isValid: false,
                message: 'Unable to verify your profile information. Please update your profile.',
                redirectUrl: '/dashboard/customer/profile?message=Please update your profile information'
            };
        }
        // Check if name is present and not empty
        const isValid = !!(profile?.name && profile.name.trim() !== '');
        if (!isValid) {
            const message = 'Please complete your name in your profile to access the dashboard.';
            const redirectUrl = `/dashboard/customer/profile?message=${encodeURIComponent(message)}`;
            return {
                isValid: false,
                message,
                redirectUrl
            };
        }
        return {
            isValid: true
        };
    } catch (error) {
        console.error('Unexpected error during name validation:', error);
        return {
            isValid: false,
            message: 'An error occurred while validating your profile. Please update your profile.',
            redirectUrl: '/dashboard/customer/profile?message=Please update your profile information'
        };
    }
}
async function requireCompleteName(userId) {
    const validation = await validateCustomerName(userId);
    if (!validation.isValid && validation.redirectUrl) {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$components$2f$navigation$2e$react$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["redirect"])(validation.redirectUrl);
    }
}
async function requireCompleteProfile(userId, exemptFromAddressValidation = false) {
    // Always check name (required for all dashboard access)
    await requireCompleteName(userId);
    // Only check address if not exempt (settings page is exempt)
    if (!exemptFromAddressValidation) {
        await requireCompleteAddress(userId);
    }
}
async function getCustomerAddressData(userId) {
    const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])();
    try {
        const { data: profile, error } = await supabase.from('customer_profiles').select('pincode, state, city, locality, address').eq('id', userId).single();
        if (error) {
            console.error('Error fetching customer address data:', error);
            return {
                error: 'Failed to fetch address data'
            };
        }
        return {
            data: {
                pincode: profile?.pincode,
                state: profile?.state,
                city: profile?.city,
                locality: profile?.locality,
                address: profile?.address
            }
        };
    } catch (error) {
        console.error('Unexpected error fetching address data:', error);
        return {
            error: 'An unexpected error occurred'
        };
    }
}
;
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ensureServerEntryExports"])([
    validateCustomerAddress,
    requireCompleteAddress,
    validateCustomerName,
    requireCompleteName,
    requireCompleteProfile,
    getCustomerAddressData
]);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(validateCustomerAddress, "40d8cab922554d01cea4f763d816fb4734270ab290", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(requireCompleteAddress, "40c05b55f995304a112e3e7557409d074df1c34bf2", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(validateCustomerName, "407770ce7befdbdc280da755c304531d3f7ea6b5dd", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(requireCompleteName, "400df42a7734311425e09521ebcc4d895b5368c63a", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(requireCompleteProfile, "60510e8a33e98dfcb8dbb5099888e59125201d5a12", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(getCustomerAddressData, "40746d48fecf14ef44441f42ba6d4270f14c867a68", null);
}}),
"[project]/lib/actions/products/fetchProductsByIds.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ [{"4074673ff6785399019cf7458cf6a4a38c07def56e":"fetchProductsByIds"},"",""] */ __turbopack_context__.s({
    "fetchProductsByIds": (()=>fetchProductsByIds)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/server-reference.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$app$2d$render$2f$encryption$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/app-render/encryption.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/supabase/server.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-validate.js [app-rsc] (ecmascript)");
;
;
;
async function fetchProductsByIds(productIds) {
    if (!productIds || productIds.length === 0) {
        return {
            success: true,
            data: []
        };
    }
    try {
        // Use admin client to bypass RLS policies
        const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])();
        const { data, error } = await supabase.from("products_services").select("id, name, base_price, discounted_price, image_url, slug").in("id", productIds).eq("is_available", true);
        if (error) {
            console.error("Error fetching products by IDs:", error);
            return {
                success: false,
                error: "Failed to fetch products"
            };
        }
        return {
            success: true,
            data: data || []
        };
    } catch (error) {
        console.error("Error in fetchProductsByIds:", error);
        return {
            success: false,
            error: "An unexpected error occurred"
        };
    }
}
;
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ensureServerEntryExports"])([
    fetchProductsByIds
]);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(fetchProductsByIds, "4074673ff6785399019cf7458cf6a4a38c07def56e", null);
}}),
"[project]/lib/utils/storage-paths.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Scalable Storage Path Utilities
 *
 * This module provides utilities for generating scalable storage paths
 * that can handle billions of users efficiently using hash-based distribution.
 */ /**
 * Generate scalable user path using hash-based distribution
 *
 * @param userId - The user's UUID
 * @returns Scalable path: users/{prefix}/{midfix}/{userId}
 *
 * Example:
 * - Input: "a1b2c3d4-e5f6-7890-abcd-ef1234567890"
 * - Output: "users/a1/b2/a1b2c3d4-e5f6-7890-abcd-ef1234567890"
 */ __turbopack_context__.s({
    "PathValidator": (()=>PathValidator),
    "StorageAnalytics": (()=>StorageAnalytics),
    "getCustomAdImagePath": (()=>getCustomAdImagePath),
    "getCustomHeaderImagePath": (()=>getCustomHeaderImagePath),
    "getCustomerAvatarPath": (()=>getCustomerAvatarPath),
    "getCustomerPostImagePath": (()=>getCustomerPostImagePath),
    "getGalleryImagePath": (()=>getGalleryImagePath),
    "getPostFolderPath": (()=>getPostFolderPath),
    "getPostImagePath": (()=>getPostImagePath),
    "getProductBaseImagePath": (()=>getProductBaseImagePath),
    "getProductImagePath": (()=>getProductImagePath),
    "getProductVariantImagePath": (()=>getProductVariantImagePath),
    "getProfileImagePath": (()=>getProfileImagePath),
    "getScalableUserPath": (()=>getScalableUserPath),
    "getThemeSpecificHeaderImagePath": (()=>getThemeSpecificHeaderImagePath)
});
function getScalableUserPath(userId) {
    if (!userId || typeof userId !== 'string') {
        throw new Error(`Invalid userId: expected string, got ${typeof userId}. Value: ${userId}`);
    }
    if (userId.length < 4) {
        throw new Error(`Invalid userId: must be at least 4 characters long. Got: ${userId}`);
    }
    const prefix = userId.substring(0, 2).toLowerCase();
    const midfix = userId.substring(2, 4).toLowerCase();
    return `users/${prefix}/${midfix}/${userId}`;
}
function getProfileImagePath(userId, timestamp) {
    const userPath = getScalableUserPath(userId);
    return `${userPath}/profile/logo_${timestamp}.webp`;
}
function getProductImagePath(userId, productId, imageIndex, timestamp) {
    const userPath = getScalableUserPath(userId);
    return `${userPath}/products/${productId}/image_${imageIndex}_${timestamp}.webp`;
}
function getProductBaseImagePath(userId, productId, imageIndex, timestamp) {
    const userPath = getScalableUserPath(userId);
    return `${userPath}/products/${productId}/base/image_${imageIndex}_${timestamp}.webp`;
}
function getProductVariantImagePath(userId, productId, variantId, imageIndex, timestamp) {
    const userPath = getScalableUserPath(userId);
    return `${userPath}/products/${productId}/${variantId}/image_${imageIndex}_${timestamp}.webp`;
}
function getGalleryImagePath(userId, timestamp) {
    const userPath = getScalableUserPath(userId);
    return `${userPath}/gallery/gallery_${timestamp}.webp`;
}
function getPostImagePath(userId, postId, imageIndex, timestamp, createdAt) {
    const userPath = getScalableUserPath(userId);
    // Use post creation date if provided, otherwise use current date (for backward compatibility)
    const dateToUse = createdAt ? new Date(createdAt) : new Date();
    const year = dateToUse.getFullYear();
    const month = String(dateToUse.getMonth() + 1).padStart(2, '0');
    return `${userPath}/posts/${year}/${month}/${postId}/image_${imageIndex}_${timestamp}.webp`;
}
function getPostFolderPath(userId, postId, createdAt) {
    const userPath = getScalableUserPath(userId);
    const postDate = new Date(createdAt);
    const year = postDate.getFullYear();
    const month = String(postDate.getMonth() + 1).padStart(2, '0');
    return `${userPath}/posts/${year}/${month}/${postId}`;
}
function getCustomerAvatarPath(userId, timestamp) {
    const userPath = getScalableUserPath(userId);
    return `${userPath}/avatar/avatar_${timestamp}.webp`;
}
function getCustomerPostImagePath(userId, postId, imageIndex, timestamp, createdAt) {
    const userPath = getScalableUserPath(userId);
    // Use post creation date if provided, otherwise use current date (for backward compatibility)
    const dateToUse = createdAt ? new Date(createdAt) : new Date();
    const year = dateToUse.getFullYear();
    const month = String(dateToUse.getMonth() + 1).padStart(2, '0');
    return `${userPath}/posts/${year}/${month}/${postId}/image_${imageIndex}_${timestamp}.webp`;
}
function getCustomAdImagePath(userId, timestamp) {
    const userPath = getScalableUserPath(userId);
    return `${userPath}/ads/custom_ad_${timestamp}.webp`;
}
function getCustomHeaderImagePath(userId, timestamp) {
    const userPath = getScalableUserPath(userId);
    return `${userPath}/branding/header_${timestamp}.webp`;
}
function getThemeSpecificHeaderImagePath(userId, timestamp, theme) {
    const userPath = getScalableUserPath(userId);
    return `${userPath}/branding/header_${theme}_${timestamp}.webp`;
}
class PathValidator {
    /**
   * Validate if a path follows the new scalable structure
   */ static isScalablePath(path) {
        return path.startsWith('users/') && path.split('/').length >= 4;
    }
    /**
   * Extract user ID from scalable path
   */ static extractUserIdFromPath(path) {
        if (!this.isScalablePath(path)) {
            return null;
        }
        const parts = path.split('/');
        return parts[3]; // users/{prefix}/{midfix}/{userId}/...
    }
    /**
   * Validate path structure integrity
   */ static validatePathStructure(userId, path) {
        const expectedUserPath = getScalableUserPath(userId);
        return path.startsWith(expectedUserPath);
    }
}
class StorageAnalytics {
    /**
   * Get storage distribution info for monitoring
   */ static getDistributionInfo(userId) {
        const prefix = userId.substring(0, 2).toLowerCase();
        const midfix = userId.substring(2, 4).toLowerCase();
        // Estimate number of users in same bucket (assuming even distribution)
        const totalBuckets = 16 * 16 * 16 * 16; // 65,536 buckets
        const estimatedPeers = Math.floor(1000000 / totalBuckets); // Estimate for 1M users
        return {
            prefix,
            midfix,
            bucket: `${prefix}/${midfix}`,
            estimatedPeers
        };
    }
}
}}),
"[project]/lib/actions/shared/upload-post-media.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ [{"70230247a2615eac963af3c0185b53314dbb627700":"uploadPostImage","703053c6c06d06766412f5c8f20f43881982d58462":"deletePostMedia"},"",""] */ __turbopack_context__.s({
    "deletePostMedia": (()=>deletePostMedia),
    "uploadPostImage": (()=>uploadPostImage)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/server-reference.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$app$2d$render$2f$encryption$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/app-render/encryption.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/supabase/server.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2f$storage$2d$paths$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/utils/storage-paths.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-validate.js [app-rsc] (ecmascript)");
;
;
;
;
async function uploadPostImage(formData, postId, postCreatedAt) {
    const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])();
    // Get the current user
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
        return {
            success: false,
            error: "User not authenticated."
        };
    }
    const userId = user.id;
    const imageFile = formData.get("imageFile");
    if (!imageFile) {
        return {
            success: false,
            error: "No image file provided."
        };
    }
    // Use the provided post creation date for consistent folder structure
    // Validate file type (strict backend validation)
    const allowedTypes = [
        "image/jpeg",
        "image/png",
        "image/gif",
        "image/webp"
    ];
    const allowedExtensions = [
        ".jpg",
        ".jpeg",
        ".png",
        ".gif",
        ".webp"
    ];
    if (!allowedTypes.includes(imageFile.type)) {
        return {
            success: false,
            error: "Invalid file type. Only JPEG, PNG, GIF, and WebP images are allowed."
        };
    }
    // Validate file extension as additional security measure
    const fileName = imageFile.name.toLowerCase();
    const hasValidExtension = allowedExtensions.some((ext)=>fileName.endsWith(ext));
    if (!hasValidExtension) {
        return {
            success: false,
            error: "Invalid file extension. Please upload files with .jpg, .jpeg, .png, .gif, or .webp extensions."
        };
    }
    // Backend size validation (critical security check)
    const maxSize = 15 * 1024 * 1024; // 15MB - matches industry standards
    if (imageFile.size > maxSize) {
        const fileSizeMB = (imageFile.size / (1024 * 1024)).toFixed(2);
        return {
            success: false,
            error: `File size (${fileSizeMB}MB) exceeds the 15MB limit. Please choose a smaller image.`
        };
    }
    // Additional security: Check for minimum file size (avoid empty files)
    if (imageFile.size < 100) {
        return {
            success: false,
            error: "File appears to be empty or corrupted. Please try a different image."
        };
    }
    try {
        // Create scalable path structure for billions of users
        const timestamp = Date.now() + Math.floor(Math.random() * 1000);
        const bucketName = "business";
        const imagePath = (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2f$storage$2d$paths$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getPostImagePath"])(userId, postId, 0, timestamp, postCreatedAt);
        // File is already compressed on client-side, just upload it
        const fileBuffer = Buffer.from(await imageFile.arrayBuffer());
        // Use admin client for storage operations to bypass RLS
        const adminSupabase = (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])();
        // Upload to Supabase Storage using admin client
        const client = await adminSupabase;
        const { error: uploadError } = await client.storage.from(bucketName).upload(imagePath, fileBuffer, {
            contentType: imageFile.type,
            upsert: true
        });
        if (uploadError) {
            console.error("Post Image Upload Error:", uploadError);
            return {
                success: false,
                error: `Failed to upload image: ${uploadError.message}`
            };
        }
        // Get the public URL using admin client
        const { data: urlData } = client.storage.from(bucketName).getPublicUrl(imagePath);
        if (!urlData?.publicUrl) {
            return {
                success: false,
                error: "Could not retrieve public URL after upload."
            };
        }
        return {
            success: true,
            url: urlData.publicUrl
        };
    } catch (error) {
        console.error("Error processing post image:", error);
        return {
            success: false,
            error: "Failed to process image. Please try a different image."
        };
    }
}
async function deletePostMedia(userId, postId, createdAt) {
    // Use admin client for storage operations to bypass RLS
    const adminSupabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])();
    try {
        const bucketName = "business";
        const postFolderPath = (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2f$storage$2d$paths$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getPostFolderPath"])(userId, postId, createdAt);
        // List all files in the post folder
        const { data: files, error: listError } = await adminSupabase.storage.from(bucketName).list(postFolderPath, {
            limit: 1000,
            sortBy: {
                column: 'name',
                order: 'asc'
            }
        });
        if (listError) {
            // If folder doesn't exist, consider it successful (already deleted)
            if (listError.message?.includes('not found') || listError.message?.includes('does not exist') || listError.message?.includes('The resource was not found')) {
                return {
                    success: true
                };
            }
            console.error("Error listing post media files:", listError);
            return {
                success: false,
                error: `Failed to list media files: ${listError.message}`
            };
        }
        if (!files || files.length === 0) {
            // No files to delete, folder is already empty or doesn't exist
            return {
                success: true
            };
        }
        // Create full paths for all files in the folder
        const filePaths = files.map((file)=>`${postFolderPath}/${file.name}`);
        // Delete all files in the post folder using admin client
        // In object storage, deleting all files effectively removes the folder
        const { error: deleteError } = await adminSupabase.storage.from(bucketName).remove(filePaths);
        if (deleteError) {
            console.error("Error deleting post folder contents:", deleteError);
            return {
                success: false,
                error: `Failed to delete post folder: ${deleteError.message}`
            };
        }
        return {
            success: true
        };
    } catch (error) {
        console.error("Error in deletePostMedia:", error);
        return {
            success: false,
            error: "An unexpected error occurred while deleting post folder."
        };
    }
}
;
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ensureServerEntryExports"])([
    uploadPostImage,
    deletePostMedia
]);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(uploadPostImage, "70230247a2615eac963af3c0185b53314dbb627700", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(deletePostMedia, "703053c6c06d06766412f5c8f20f43881982d58462", null);
}}),
"[project]/lib/actions/posts/crud.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ [{"4096d0d7de5aea68cba63046dd8d3ed685639d0cbe":"deletePost","40cbc8f36fd78c063171b360a6f8611fd75cefca5b":"createPost","6012d28a2943cafb935e310ad9b16ba56318e58ec2":"updatePostProducts","603e96bb33edacdc2a0d6c61e873d3910c299d2133":"updatePostContent","60fc954e7f9c455ce54d6f381c487478578ea7c3c5":"updatePost"},"",""] */ __turbopack_context__.s({
    "createPost": (()=>createPost),
    "deletePost": (()=>deletePost),
    "updatePost": (()=>updatePost),
    "updatePostContent": (()=>updatePostContent),
    "updatePostProducts": (()=>updatePostProducts)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/server-reference.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$app$2d$render$2f$encryption$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/app-render/encryption.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/supabase/server.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/cache.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$shared$2f$upload$2d$post$2d$media$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/actions/shared/upload-post-media.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-validate.js [app-rsc] (ecmascript)");
;
;
;
;
;
async function createPost(formData) {
    const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])();
    // Get the current user
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    if (userError || !user) {
        return {
            success: false,
            message: 'Authentication required',
            error: 'You must be logged in to create a post'
        };
    }
    // Get the user's business profile
    const { data: businessProfile, error: profileError } = await supabase.from('business_profiles').select('id, city_slug, state_slug, locality_slug, pincode, logo_url').eq('id', user.id).single();
    if (profileError || !businessProfile) {
        return {
            success: false,
            message: 'Business profile not found',
            error: 'You must have a business profile to create a post'
        };
    }
    // Prepare post data
    const postData = {
        business_id: user.id,
        content: formData.content,
        image_url: formData.image_url || null,
        city_slug: businessProfile.city_slug,
        state_slug: businessProfile.state_slug,
        locality_slug: businessProfile.locality_slug,
        pincode: businessProfile.pincode,
        product_ids: formData.product_ids || [],
        mentioned_business_ids: formData.mentioned_business_ids || [],
        author_avatar: businessProfile.logo_url
    };
    // Insert the post
    const { data, error } = await supabase.from('business_posts').insert(postData).select().single();
    if (error) {
        console.error('Error creating post:', error);
        return {
            success: false,
            message: 'Failed to create post',
            error: error.message
        };
    }
    // Revalidate the feed pages
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revalidatePath"])('/dashboard/business/feed');
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revalidatePath"])('/dashboard/customer/feed');
    return {
        success: true,
        message: 'Post created successfully',
        data
    };
}
async function updatePostContent(postId, content) {
    const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])();
    // Get the current user
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    if (userError || !user) {
        return {
            success: false,
            message: 'Authentication required',
            error: 'You must be logged in to update a post'
        };
    }
    // Check if the post exists and belongs to the user
    const { data: existingPost, error: postError } = await supabase.from('business_posts').select('id').eq('id', postId).eq('business_id', user.id).single();
    if (postError || !existingPost) {
        return {
            success: false,
            message: 'Post not found',
            error: 'The post does not exist or you do not have permission to update it'
        };
    }
    // Update only the content and timestamp
    const { data, error } = await supabase.from('business_posts').update({
        content: content.trim(),
        updated_at: new Date().toISOString()
    }).eq('id', postId).select().single();
    if (error) {
        console.error('Error updating post content:', error);
        return {
            success: false,
            message: 'Failed to update post',
            error: error.message
        };
    }
    // Revalidate the feed pages
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revalidatePath"])('/dashboard/business/feed');
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revalidatePath"])('/dashboard/customer/feed');
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revalidatePath"])('/dashboard/business/posts');
    return {
        success: true,
        message: 'Post updated successfully',
        data
    };
}
async function updatePostProducts(postId, productIds) {
    const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])();
    // Get the current user
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    if (userError || !user) {
        return {
            success: false,
            message: 'Authentication required',
            error: 'You must be logged in to update a post'
        };
    }
    // Check if the post exists and belongs to the user
    const { data: existingPost, error: postError } = await supabase.from('business_posts').select('id').eq('id', postId).eq('business_id', user.id).single();
    if (postError || !existingPost) {
        return {
            success: false,
            message: 'Post not found',
            error: 'The post does not exist or you do not have permission to update it'
        };
    }
    // Update only the product_ids and timestamp
    const { data, error } = await supabase.from('business_posts').update({
        product_ids: productIds,
        updated_at: new Date().toISOString()
    }).eq('id', postId).select().single();
    if (error) {
        console.error('Error updating post products:', error);
        return {
            success: false,
            message: 'Failed to update post products',
            error: error.message
        };
    }
    // Revalidate the feed pages
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revalidatePath"])('/dashboard/business/feed');
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revalidatePath"])('/dashboard/customer/feed');
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revalidatePath"])('/dashboard/business/posts');
    return {
        success: true,
        message: 'Post products updated successfully',
        data
    };
}
async function updatePost(postId, formData) {
    const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])();
    // Get the current user
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    if (userError || !user) {
        return {
            success: false,
            message: 'Authentication required',
            error: 'You must be logged in to update a post'
        };
    }
    // Check if the post exists and belongs to the user
    const { data: existingPost, error: postError } = await supabase.from('business_posts').select('id').eq('id', postId).eq('business_id', user.id).single();
    if (postError || !existingPost) {
        return {
            success: false,
            message: 'Post not found',
            error: 'The post does not exist or you do not have permission to update it'
        };
    }
    // Prepare update data
    const updateData = {
        content: formData.content,
        image_url: formData.image_url || null,
        product_ids: formData.product_ids || [],
        mentioned_business_ids: formData.mentioned_business_ids || [],
        updated_at: new Date().toISOString()
    };
    // Update the post
    const { data, error } = await supabase.from('business_posts').update(updateData).eq('id', postId).select().single();
    if (error) {
        console.error('Error updating post:', error);
        return {
            success: false,
            message: 'Failed to update post',
            error: error.message
        };
    }
    // Revalidate the feed pages
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revalidatePath"])('/dashboard/business/feed');
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revalidatePath"])('/dashboard/customer/feed');
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revalidatePath"])('/dashboard/business/posts');
    return {
        success: true,
        message: 'Post updated successfully',
        data
    };
}
async function deletePost(postId) {
    const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])();
    // Get the current user
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    if (userError || !user) {
        return {
            success: false,
            message: 'Authentication required',
            error: 'You must be logged in to delete a post'
        };
    }
    // Check if the post exists and belongs to the user, get creation date for media deletion
    const { data: existingPost, error: postError } = await supabase.from('business_posts').select('id, created_at, image_url').eq('id', postId).eq('business_id', user.id).single();
    if (postError || !existingPost) {
        return {
            success: false,
            message: 'Post not found',
            error: 'The post does not exist or you do not have permission to delete it'
        };
    }
    // First, attempt to delete the post folder from storage
    // This ensures we clean up any files that might exist, regardless of image_url status
    try {
        const mediaDeleteResult = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$shared$2f$upload$2d$post$2d$media$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["deletePostMedia"])(user.id, postId, existingPost.created_at);
        if (!mediaDeleteResult.success && mediaDeleteResult.error) {
            console.error('Error deleting business post media:', mediaDeleteResult.error);
            return {
                success: false,
                message: 'Failed to delete post images',
                error: `Cannot delete post: ${mediaDeleteResult.error}`
            };
        }
    } catch (mediaError) {
        console.error('Error deleting business post media:', mediaError);
        return {
            success: false,
            message: 'Failed to delete post images',
            error: 'Cannot delete post: Failed to clean up associated images'
        };
    }
    // Only delete the post after successful media deletion
    const { error } = await supabase.from('business_posts').delete().eq('id', postId);
    if (error) {
        console.error('Error deleting post:', error);
        return {
            success: false,
            message: 'Failed to delete post',
            error: error.message
        };
    }
    // Revalidate the feed pages
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revalidatePath"])('/dashboard/business/feed');
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revalidatePath"])('/dashboard/customer/feed');
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revalidatePath"])('/dashboard/business/posts');
    return {
        success: true,
        message: 'Post deleted successfully'
    };
}
;
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ensureServerEntryExports"])([
    createPost,
    updatePostContent,
    updatePostProducts,
    updatePost,
    deletePost
]);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(createPost, "40cbc8f36fd78c063171b360a6f8611fd75cefca5b", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(updatePostContent, "603e96bb33edacdc2a0d6c61e873d3910c299d2133", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(updatePostProducts, "6012d28a2943cafb935e310ad9b16ba56318e58ec2", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(updatePost, "60fc954e7f9c455ce54d6f381c487478578ea7c3c5", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(deletePost, "4096d0d7de5aea68cba63046dd8d3ed685639d0cbe", null);
}}),
"[project]/lib/actions/shared/productActions.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ [{"400c2b15a1606fb3bc5a82890f745fed792732b08b":"getSelectedProducts","4059cdac55c061418f03f8c61148e29c690821267a":"searchBusinessProducts"},"",""] */ __turbopack_context__.s({
    "getSelectedProducts": (()=>getSelectedProducts),
    "searchBusinessProducts": (()=>searchBusinessProducts)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/server-reference.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$app$2d$render$2f$encryption$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/app-render/encryption.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/supabase/server.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-validate.js [app-rsc] (ecmascript)");
;
;
;
async function searchBusinessProducts(query) {
    try {
        // Validate input
        if (!query || query.trim().length < 2) {
            return {
                success: false,
                error: "Search query must be at least 2 characters long"
            };
        }
        // Get authenticated user
        const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])();
        const { data: { user }, error: authError } = await supabase.auth.getUser();
        if (authError || !user) {
            return {
                success: false,
                error: "Authentication required"
            };
        }
        // Use admin client to bypass RLS policies
        const adminSupabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])();
        // Search products for the current user's business only
        const { data, error } = await adminSupabase.from("products_services").select("*").eq("business_id", user.id) // Ensure user can only see their own products
        .eq("is_available", true).ilike("name", `%${query.trim()}%`).order("name", {
            ascending: true
        }).limit(10); // Limit search results to 10 items
        if (error) {
            console.error("Error searching products:", error);
            return {
                success: false,
                error: "Failed to search products"
            };
        }
        return {
            success: true,
            data: data || []
        };
    } catch (error) {
        console.error("Error in searchBusinessProducts:", error);
        return {
            success: false,
            error: "An unexpected error occurred"
        };
    }
}
async function getSelectedProducts(productIds) {
    try {
        // Validate input
        if (!productIds || productIds.length === 0) {
            return {
                success: true,
                data: []
            };
        }
        // Get authenticated user
        const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])();
        const { data: { user }, error: authError } = await supabase.auth.getUser();
        if (authError || !user) {
            return {
                success: false,
                error: "Authentication required"
            };
        }
        // Use regular client - accessing user's own products with authentication
        // Get products by IDs, but only for the current user's business
        const { data, error } = await supabase.from("products_services").select("*").in("id", productIds).eq("business_id", user.id) // Ensure user can only access their own products
        .order("name", {
            ascending: true
        });
        if (error) {
            console.error("Error getting selected products:", error);
            return {
                success: false,
                error: "Failed to get selected products"
            };
        }
        return {
            success: true,
            data: data || []
        };
    } catch (error) {
        console.error("Error in getSelectedProducts:", error);
        return {
            success: false,
            error: "An unexpected error occurred"
        };
    }
}
;
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ensureServerEntryExports"])([
    searchBusinessProducts,
    getSelectedProducts
]);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(searchBusinessProducts, "4059cdac55c061418f03f8c61148e29c690821267a", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(getSelectedProducts, "400c2b15a1606fb3bc5a82890f745fed792732b08b", null);
}}),
"[project]/lib/actions/shared/upload-customer-post-media.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ [{"7017ee8ddfe84b9f7339ea8f795be250cec915af07":"uploadCustomerPostImage"},"",""] */ __turbopack_context__.s({
    "uploadCustomerPostImage": (()=>uploadCustomerPostImage)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/server-reference.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$app$2d$render$2f$encryption$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/app-render/encryption.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/supabase/server.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2f$storage$2d$paths$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/utils/storage-paths.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-validate.js [app-rsc] (ecmascript)");
;
;
;
;
async function uploadCustomerPostImage(formData, postId, postCreatedAt) {
    const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])();
    // Get the current user
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
        return {
            success: false,
            error: "User not authenticated."
        };
    }
    const userId = user.id;
    const imageFile = formData.get("imageFile");
    if (!imageFile) {
        return {
            success: false,
            error: "No image file provided."
        };
    }
    // Validate file type
    const allowedTypes = [
        'image/jpeg',
        'image/jpg',
        'image/png',
        'image/webp',
        'image/gif'
    ];
    if (!allowedTypes.includes(imageFile.type)) {
        return {
            success: false,
            error: "Invalid file type. Only JPEG, PNG, WebP, and GIF images are allowed."
        };
    }
    // Validate file size (15MB limit)
    const maxSize = 15 * 1024 * 1024; // 15MB
    if (imageFile.size > maxSize) {
        return {
            success: false,
            error: "File size exceeds 15MB limit."
        };
    }
    // Validate that the post belongs to the user
    const { data: existingPost, error: postError } = await supabase.from('customer_posts').select('id, customer_id').eq('id', postId).eq('customer_id', userId).single();
    if (postError || !existingPost) {
        return {
            success: false,
            error: "Post not found or you don't have permission to upload images for this post."
        };
    }
    try {
        // Create scalable path structure for billions of users
        const timestamp = Date.now() + Math.floor(Math.random() * 1000);
        const bucketName = "customers";
        const imagePath = (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2f$storage$2d$paths$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getCustomerPostImagePath"])(userId, postId, 0, timestamp, postCreatedAt);
        // File is already compressed on client-side, just upload it
        const fileBuffer = Buffer.from(await imageFile.arrayBuffer());
        // Use admin client for storage operations to bypass RLS
        const adminSupabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])();
        // Upload to Supabase Storage using admin client
        const { error: uploadError } = await adminSupabase.storage.from(bucketName).upload(imagePath, fileBuffer, {
            contentType: imageFile.type,
            upsert: true
        });
        if (uploadError) {
            console.error("Customer Post Image Upload Error:", uploadError);
            return {
                success: false,
                error: `Failed to upload image: ${uploadError.message}`
            };
        }
        // Get the public URL using admin client
        const { data: urlData } = adminSupabase.storage.from(bucketName).getPublicUrl(imagePath);
        if (!urlData?.publicUrl) {
            return {
                success: false,
                error: "Could not retrieve public URL after upload."
            };
        }
        return {
            success: true,
            url: urlData.publicUrl
        };
    } catch (error) {
        console.error("Error processing customer post image:", error);
        return {
            success: false,
            error: "Failed to process image. Please try a different image."
        };
    }
}
;
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ensureServerEntryExports"])([
    uploadCustomerPostImage
]);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(uploadCustomerPostImage, "7017ee8ddfe84b9f7339ea8f795be250cec915af07", null);
}}),
"[project]/.next-internal/server/app/(dashboard)/dashboard/customer/page/actions.js { ACTIONS_MODULE0 => \"[project]/app/auth/actions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/lib/actions/customerProfiles/addressValidation.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/lib/actions/products/fetchProductsByIds.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE3 => \"[project]/lib/actions/posts/crud.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE4 => \"[project]/lib/actions/shared/productActions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE5 => \"[project]/lib/actions/shared/upload-customer-post-media.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$auth$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/auth/actions.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$customerProfiles$2f$addressValidation$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/actions/customerProfiles/addressValidation.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$products$2f$fetchProductsByIds$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/actions/products/fetchProductsByIds.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$posts$2f$crud$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/actions/posts/crud.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$shared$2f$productActions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/actions/shared/productActions.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$shared$2f$upload$2d$customer$2d$post$2d$media$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/actions/shared/upload-customer-post-media.ts [app-rsc] (ecmascript)");
;
;
;
;
;
;
;
;
;
;
;
;
;
}}),
"[project]/.next-internal/server/app/(dashboard)/dashboard/customer/page/actions.js { ACTIONS_MODULE0 => \"[project]/app/auth/actions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/lib/actions/customerProfiles/addressValidation.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/lib/actions/products/fetchProductsByIds.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE3 => \"[project]/lib/actions/posts/crud.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE4 => \"[project]/lib/actions/shared/productActions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE5 => \"[project]/lib/actions/shared/upload-customer-post-media.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$auth$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/auth/actions.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$customerProfiles$2f$addressValidation$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/actions/customerProfiles/addressValidation.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$products$2f$fetchProductsByIds$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/actions/products/fetchProductsByIds.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$posts$2f$crud$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/actions/posts/crud.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$shared$2f$productActions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/actions/shared/productActions.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$shared$2f$upload$2d$customer$2d$post$2d$media$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/actions/shared/upload-customer-post-media.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f28$dashboard$292f$dashboard$2f$customer$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$app$2f$auth$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$customerProfiles$2f$addressValidation$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$products$2f$fetchProductsByIds$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE3__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$posts$2f$crud$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE4__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$shared$2f$productActions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE5__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$shared$2f$upload$2d$customer$2d$post$2d$media$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i('[project]/.next-internal/server/app/(dashboard)/dashboard/customer/page/actions.js { ACTIONS_MODULE0 => "[project]/app/auth/actions.ts [app-rsc] (ecmascript)", ACTIONS_MODULE1 => "[project]/lib/actions/customerProfiles/addressValidation.ts [app-rsc] (ecmascript)", ACTIONS_MODULE2 => "[project]/lib/actions/products/fetchProductsByIds.ts [app-rsc] (ecmascript)", ACTIONS_MODULE3 => "[project]/lib/actions/posts/crud.ts [app-rsc] (ecmascript)", ACTIONS_MODULE4 => "[project]/lib/actions/shared/productActions.ts [app-rsc] (ecmascript)", ACTIONS_MODULE5 => "[project]/lib/actions/shared/upload-customer-post-media.ts [app-rsc] (ecmascript)" } [app-rsc] (server actions loader, ecmascript) <locals>');
}}),
"[project]/.next-internal/server/app/(dashboard)/dashboard/customer/page/actions.js { ACTIONS_MODULE0 => \"[project]/app/auth/actions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/lib/actions/customerProfiles/addressValidation.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/lib/actions/products/fetchProductsByIds.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE3 => \"[project]/lib/actions/posts/crud.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE4 => \"[project]/lib/actions/shared/productActions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE5 => \"[project]/lib/actions/shared/upload-customer-post-media.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript) <exports>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "0027aa970a57d5cbd11af21933b24fca73bbd99631": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$auth$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["signOutUser"]),
    "400c2b15a1606fb3bc5a82890f745fed792732b08b": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$shared$2f$productActions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getSelectedProducts"]),
    "400df42a7734311425e09521ebcc4d895b5368c63a": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$customerProfiles$2f$addressValidation$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["requireCompleteName"]),
    "4059cdac55c061418f03f8c61148e29c690821267a": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$shared$2f$productActions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["searchBusinessProducts"]),
    "4074673ff6785399019cf7458cf6a4a38c07def56e": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$products$2f$fetchProductsByIds$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fetchProductsByIds"]),
    "40746d48fecf14ef44441f42ba6d4270f14c867a68": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$customerProfiles$2f$addressValidation$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getCustomerAddressData"]),
    "407770ce7befdbdc280da755c304531d3f7ea6b5dd": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$customerProfiles$2f$addressValidation$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["validateCustomerName"]),
    "4096d0d7de5aea68cba63046dd8d3ed685639d0cbe": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$posts$2f$crud$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["deletePost"]),
    "40c05b55f995304a112e3e7557409d074df1c34bf2": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$customerProfiles$2f$addressValidation$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["requireCompleteAddress"]),
    "40d8cab922554d01cea4f763d816fb4734270ab290": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$customerProfiles$2f$addressValidation$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["validateCustomerAddress"]),
    "60510e8a33e98dfcb8dbb5099888e59125201d5a12": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$customerProfiles$2f$addressValidation$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["requireCompleteProfile"]),
    "60fc954e7f9c455ce54d6f381c487478578ea7c3c5": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$posts$2f$crud$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["updatePost"]),
    "7017ee8ddfe84b9f7339ea8f795be250cec915af07": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$shared$2f$upload$2d$customer$2d$post$2d$media$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["uploadCustomerPostImage"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$auth$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/auth/actions.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$customerProfiles$2f$addressValidation$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/actions/customerProfiles/addressValidation.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$products$2f$fetchProductsByIds$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/actions/products/fetchProductsByIds.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$posts$2f$crud$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/actions/posts/crud.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$shared$2f$productActions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/actions/shared/productActions.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$shared$2f$upload$2d$customer$2d$post$2d$media$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/actions/shared/upload-customer-post-media.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f28$dashboard$292f$dashboard$2f$customer$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$app$2f$auth$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$customerProfiles$2f$addressValidation$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$products$2f$fetchProductsByIds$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE3__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$posts$2f$crud$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE4__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$shared$2f$productActions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE5__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$shared$2f$upload$2d$customer$2d$post$2d$media$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i('[project]/.next-internal/server/app/(dashboard)/dashboard/customer/page/actions.js { ACTIONS_MODULE0 => "[project]/app/auth/actions.ts [app-rsc] (ecmascript)", ACTIONS_MODULE1 => "[project]/lib/actions/customerProfiles/addressValidation.ts [app-rsc] (ecmascript)", ACTIONS_MODULE2 => "[project]/lib/actions/products/fetchProductsByIds.ts [app-rsc] (ecmascript)", ACTIONS_MODULE3 => "[project]/lib/actions/posts/crud.ts [app-rsc] (ecmascript)", ACTIONS_MODULE4 => "[project]/lib/actions/shared/productActions.ts [app-rsc] (ecmascript)", ACTIONS_MODULE5 => "[project]/lib/actions/shared/upload-customer-post-media.ts [app-rsc] (ecmascript)" } [app-rsc] (server actions loader, ecmascript) <locals>');
}}),
"[project]/.next-internal/server/app/(dashboard)/dashboard/customer/page/actions.js { ACTIONS_MODULE0 => \"[project]/app/auth/actions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/lib/actions/customerProfiles/addressValidation.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/lib/actions/products/fetchProductsByIds.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE3 => \"[project]/lib/actions/posts/crud.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE4 => \"[project]/lib/actions/shared/productActions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE5 => \"[project]/lib/actions/shared/upload-customer-post-media.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "0027aa970a57d5cbd11af21933b24fca73bbd99631": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f28$dashboard$292f$dashboard$2f$customer$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$app$2f$auth$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$customerProfiles$2f$addressValidation$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$products$2f$fetchProductsByIds$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE3__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$posts$2f$crud$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE4__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$shared$2f$productActions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE5__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$shared$2f$upload$2d$customer$2d$post$2d$media$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["0027aa970a57d5cbd11af21933b24fca73bbd99631"]),
    "400c2b15a1606fb3bc5a82890f745fed792732b08b": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f28$dashboard$292f$dashboard$2f$customer$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$app$2f$auth$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$customerProfiles$2f$addressValidation$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$products$2f$fetchProductsByIds$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE3__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$posts$2f$crud$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE4__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$shared$2f$productActions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE5__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$shared$2f$upload$2d$customer$2d$post$2d$media$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["400c2b15a1606fb3bc5a82890f745fed792732b08b"]),
    "400df42a7734311425e09521ebcc4d895b5368c63a": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f28$dashboard$292f$dashboard$2f$customer$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$app$2f$auth$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$customerProfiles$2f$addressValidation$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$products$2f$fetchProductsByIds$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE3__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$posts$2f$crud$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE4__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$shared$2f$productActions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE5__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$shared$2f$upload$2d$customer$2d$post$2d$media$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["400df42a7734311425e09521ebcc4d895b5368c63a"]),
    "4059cdac55c061418f03f8c61148e29c690821267a": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f28$dashboard$292f$dashboard$2f$customer$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$app$2f$auth$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$customerProfiles$2f$addressValidation$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$products$2f$fetchProductsByIds$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE3__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$posts$2f$crud$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE4__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$shared$2f$productActions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE5__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$shared$2f$upload$2d$customer$2d$post$2d$media$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["4059cdac55c061418f03f8c61148e29c690821267a"]),
    "4074673ff6785399019cf7458cf6a4a38c07def56e": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f28$dashboard$292f$dashboard$2f$customer$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$app$2f$auth$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$customerProfiles$2f$addressValidation$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$products$2f$fetchProductsByIds$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE3__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$posts$2f$crud$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE4__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$shared$2f$productActions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE5__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$shared$2f$upload$2d$customer$2d$post$2d$media$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["4074673ff6785399019cf7458cf6a4a38c07def56e"]),
    "40746d48fecf14ef44441f42ba6d4270f14c867a68": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f28$dashboard$292f$dashboard$2f$customer$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$app$2f$auth$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$customerProfiles$2f$addressValidation$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$products$2f$fetchProductsByIds$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE3__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$posts$2f$crud$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE4__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$shared$2f$productActions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE5__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$shared$2f$upload$2d$customer$2d$post$2d$media$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["40746d48fecf14ef44441f42ba6d4270f14c867a68"]),
    "407770ce7befdbdc280da755c304531d3f7ea6b5dd": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f28$dashboard$292f$dashboard$2f$customer$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$app$2f$auth$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$customerProfiles$2f$addressValidation$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$products$2f$fetchProductsByIds$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE3__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$posts$2f$crud$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE4__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$shared$2f$productActions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE5__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$shared$2f$upload$2d$customer$2d$post$2d$media$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["407770ce7befdbdc280da755c304531d3f7ea6b5dd"]),
    "4096d0d7de5aea68cba63046dd8d3ed685639d0cbe": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f28$dashboard$292f$dashboard$2f$customer$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$app$2f$auth$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$customerProfiles$2f$addressValidation$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$products$2f$fetchProductsByIds$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE3__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$posts$2f$crud$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE4__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$shared$2f$productActions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE5__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$shared$2f$upload$2d$customer$2d$post$2d$media$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["4096d0d7de5aea68cba63046dd8d3ed685639d0cbe"]),
    "40c05b55f995304a112e3e7557409d074df1c34bf2": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f28$dashboard$292f$dashboard$2f$customer$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$app$2f$auth$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$customerProfiles$2f$addressValidation$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$products$2f$fetchProductsByIds$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE3__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$posts$2f$crud$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE4__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$shared$2f$productActions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE5__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$shared$2f$upload$2d$customer$2d$post$2d$media$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["40c05b55f995304a112e3e7557409d074df1c34bf2"]),
    "40d8cab922554d01cea4f763d816fb4734270ab290": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f28$dashboard$292f$dashboard$2f$customer$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$app$2f$auth$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$customerProfiles$2f$addressValidation$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$products$2f$fetchProductsByIds$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE3__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$posts$2f$crud$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE4__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$shared$2f$productActions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE5__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$shared$2f$upload$2d$customer$2d$post$2d$media$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["40d8cab922554d01cea4f763d816fb4734270ab290"]),
    "60510e8a33e98dfcb8dbb5099888e59125201d5a12": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f28$dashboard$292f$dashboard$2f$customer$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$app$2f$auth$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$customerProfiles$2f$addressValidation$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$products$2f$fetchProductsByIds$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE3__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$posts$2f$crud$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE4__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$shared$2f$productActions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE5__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$shared$2f$upload$2d$customer$2d$post$2d$media$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["60510e8a33e98dfcb8dbb5099888e59125201d5a12"]),
    "60fc954e7f9c455ce54d6f381c487478578ea7c3c5": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f28$dashboard$292f$dashboard$2f$customer$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$app$2f$auth$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$customerProfiles$2f$addressValidation$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$products$2f$fetchProductsByIds$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE3__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$posts$2f$crud$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE4__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$shared$2f$productActions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE5__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$shared$2f$upload$2d$customer$2d$post$2d$media$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["60fc954e7f9c455ce54d6f381c487478578ea7c3c5"]),
    "7017ee8ddfe84b9f7339ea8f795be250cec915af07": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f28$dashboard$292f$dashboard$2f$customer$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$app$2f$auth$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$customerProfiles$2f$addressValidation$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$products$2f$fetchProductsByIds$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE3__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$posts$2f$crud$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE4__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$shared$2f$productActions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE5__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$shared$2f$upload$2d$customer$2d$post$2d$media$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["7017ee8ddfe84b9f7339ea8f795be250cec915af07"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f28$dashboard$292f$dashboard$2f$customer$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$app$2f$auth$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$customerProfiles$2f$addressValidation$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$products$2f$fetchProductsByIds$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE3__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$posts$2f$crud$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE4__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$shared$2f$productActions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE5__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$shared$2f$upload$2d$customer$2d$post$2d$media$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i('[project]/.next-internal/server/app/(dashboard)/dashboard/customer/page/actions.js { ACTIONS_MODULE0 => "[project]/app/auth/actions.ts [app-rsc] (ecmascript)", ACTIONS_MODULE1 => "[project]/lib/actions/customerProfiles/addressValidation.ts [app-rsc] (ecmascript)", ACTIONS_MODULE2 => "[project]/lib/actions/products/fetchProductsByIds.ts [app-rsc] (ecmascript)", ACTIONS_MODULE3 => "[project]/lib/actions/posts/crud.ts [app-rsc] (ecmascript)", ACTIONS_MODULE4 => "[project]/lib/actions/shared/productActions.ts [app-rsc] (ecmascript)", ACTIONS_MODULE5 => "[project]/lib/actions/shared/upload-customer-post-media.ts [app-rsc] (ecmascript)" } [app-rsc] (server actions loader, ecmascript) <module evaluation>');
var __TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f28$dashboard$292f$dashboard$2f$customer$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$app$2f$auth$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$customerProfiles$2f$addressValidation$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$products$2f$fetchProductsByIds$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE3__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$posts$2f$crud$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE4__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$shared$2f$productActions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE5__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$shared$2f$upload$2d$customer$2d$post$2d$media$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__ = __turbopack_context__.i('[project]/.next-internal/server/app/(dashboard)/dashboard/customer/page/actions.js { ACTIONS_MODULE0 => "[project]/app/auth/actions.ts [app-rsc] (ecmascript)", ACTIONS_MODULE1 => "[project]/lib/actions/customerProfiles/addressValidation.ts [app-rsc] (ecmascript)", ACTIONS_MODULE2 => "[project]/lib/actions/products/fetchProductsByIds.ts [app-rsc] (ecmascript)", ACTIONS_MODULE3 => "[project]/lib/actions/posts/crud.ts [app-rsc] (ecmascript)", ACTIONS_MODULE4 => "[project]/lib/actions/shared/productActions.ts [app-rsc] (ecmascript)", ACTIONS_MODULE5 => "[project]/lib/actions/shared/upload-customer-post-media.ts [app-rsc] (ecmascript)" } [app-rsc] (server actions loader, ecmascript) <exports>');
}}),
"[project]/app/favicon.ico.mjs { IMAGE => \"[project]/app/favicon.ico (static in ecmascript)\" } [app-rsc] (structured image object, ecmascript, Next.js server component)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.n(__turbopack_context__.i("[project]/app/favicon.ico.mjs { IMAGE => \"[project]/app/favicon.ico (static in ecmascript)\" } [app-rsc] (structured image object, ecmascript)"));
}}),
"[project]/app/opengraph-image.png.mjs { IMAGE => \"[project]/app/opengraph-image.png (static in ecmascript)\" } [app-rsc] (structured image object, ecmascript, Next.js server component)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.n(__turbopack_context__.i("[project]/app/opengraph-image.png.mjs { IMAGE => \"[project]/app/opengraph-image.png (static in ecmascript)\" } [app-rsc] (structured image object, ecmascript)"));
}}),
"[project]/app/layout.tsx [app-rsc] (ecmascript, Next.js server component)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.n(__turbopack_context__.i("[project]/app/layout.tsx [app-rsc] (ecmascript)"));
}}),
"[project]/app/(dashboard)/dashboard/customer/layout.tsx [app-rsc] (ecmascript, Next.js server component)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.n(__turbopack_context__.i("[project]/app/(dashboard)/dashboard/customer/layout.tsx [app-rsc] (ecmascript)"));
}}),
"[project]/components/feed/ModernCustomerFeedList.tsx (client reference/proxy) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server-edge.js [app-rsc] (ecmascript)");
;
const __TURBOPACK__default__export__ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call the default export of [project]/components/feed/ModernCustomerFeedList.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/components/feed/ModernCustomerFeedList.tsx <module evaluation>", "default");
}}),
"[project]/components/feed/ModernCustomerFeedList.tsx (client reference/proxy)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server-edge.js [app-rsc] (ecmascript)");
;
const __TURBOPACK__default__export__ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call the default export of [project]/components/feed/ModernCustomerFeedList.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/components/feed/ModernCustomerFeedList.tsx", "default");
}}),
"[project]/components/feed/ModernCustomerFeedList.tsx [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$feed$2f$ModernCustomerFeedList$2e$tsx__$28$client__reference$2f$proxy$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/components/feed/ModernCustomerFeedList.tsx (client reference/proxy) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$feed$2f$ModernCustomerFeedList$2e$tsx__$28$client__reference$2f$proxy$29$__ = __turbopack_context__.i("[project]/components/feed/ModernCustomerFeedList.tsx (client reference/proxy)");
;
__turbopack_context__.n(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$feed$2f$ModernCustomerFeedList$2e$tsx__$28$client__reference$2f$proxy$29$__);
}}),
"[project]/utils/supabase/client.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "createClient": (()=>createClient)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$ssr$2f$dist$2f$module$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@supabase/ssr/dist/module/index.js [app-rsc] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$ssr$2f$dist$2f$module$2f$createBrowserClient$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@supabase/ssr/dist/module/createBrowserClient.js [app-rsc] (ecmascript)");
;
function createClient() {
    const supabaseUrl = ("TURBOPACK compile-time value", "https://rnjolcoecogzgglnblqn.supabase.co");
    const supabaseAnonKey = ("TURBOPACK compile-time value", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJuam9sY29lY29nemdnbG5ibHFuIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDMwNTIwNTYsImV4cCI6MjA1ODYyODA1Nn0.k8DuvOrrKQlvxGb5qD78_vXDqIRkmk7ZRUj1Hb5PL4o");
    if ("TURBOPACK compile-time falsy", 0) {
        "TURBOPACK unreachable";
    }
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$ssr$2f$dist$2f$module$2f$createBrowserClient$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createBrowserClient"])(supabaseUrl, supabaseAnonKey);
}
}}),
"[project]/lib/supabase/constants.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// lib/supabase/constants.ts
__turbopack_context__.s({
    "BUCKETS": (()=>BUCKETS),
    "COLUMNS": (()=>COLUMNS),
    "TABLES": (()=>TABLES)
});
const TABLES = {
    BLOGS: "blogs",
    BUSINESS_ACTIVITIES: "business_activities",
    BUSINESS_PROFILES: "business_profiles",
    CARD_VISITS: "card_visits",
    CUSTOMER_POSTS: "customer_posts",
    CUSTOMER_PROFILES: "customer_profiles",
    LIKES: "likes",
    PAYMENT_SUBSCRIPTIONS: "payment_subscriptions",
    PINCODES: "pincodes",
    PRODUCTS_SERVICES: "products_services",
    PRODUCT_VARIANTS: "product_variants",
    STORAGE_CLEANUP_CONFIG: "storage_cleanup_config",
    STORAGE_CLEANUP_PROGRESS: "storage_cleanup_progress",
    SUBSCRIPTIONS: "subscriptions",
    SYSTEM_ALERTS: "system_alerts",
    RATINGS_REVIEWS: "ratings_reviews"
};
const BUCKETS = {
    BUSINESS: "business",
    CUSTOMERS: "customers"
};
const COLUMNS = {
    ID: "id",
    CREATED_AT: "created_at",
    UPDATED_AT: "updated_at",
    NAME: "name",
    EMAIL: "email",
    PHONE: "phone",
    CITY: "city",
    STATE: "state",
    PINCODE: "pincode",
    PLAN_ID: "plan_id",
    LOCALITY: "locality",
    CITY_SLUG: "city_slug",
    STATE_SLUG: "state_slug",
    LOCALITY_SLUG: "locality_slug",
    LOGO_URL: "logo_url",
    IMAGE_URL: "image_url",
    IMAGES: "images",
    SLUG: "slug",
    STATUS: "status",
    CONTENT: "content",
    GALLERY: "gallery",
    DESCRIPTION: "description",
    TITLE: "title",
    USER_ID: "user_id",
    BUSINESS_ID: "business_id",
    BUSINESS_NAME: "business_name",
    BUSINESS_SLUG: "business_slug",
    PRODUCT_ID: "product_id",
    PRODUCT_TYPE: "product_type",
    BUSINESS_PROFILE_ID: "business_profile_id",
    RAZORPAY_SUBSCRIPTION_ID: "razorpay_subscription_id",
    SUBSCRIPTION_STATUS: "subscription_status",
    RATING: "rating",
    REVIEW_TEXT: "review_text",
    AVATAR_URL: "avatar_url",
    ADDRESS_LINE: "address_line"
};
}}),
"[project]/lib/utils/feed/planPrioritizer.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "PLAN_PRIORITY": (()=>PLAN_PRIORITY),
    "createBusinessPriorityGroups": (()=>createBusinessPriorityGroups),
    "distributePrioritizedBusinessPosts": (()=>distributePrioritizedBusinessPosts),
    "getPlanDisplayName": (()=>getPlanDisplayName),
    "hasPremiumFeatures": (()=>hasPremiumFeatures)
});
const PLAN_PRIORITY = {
    'enterprise': 5,
    'pro': 4,
    'growth': 3,
    'basic': 2,
    'free': 1
};
function createBusinessPriorityGroups(businessPosts) {
    // Group posts by business author
    const businessPostsByAuthor = new Map();
    businessPosts.forEach((post)=>{
        if (!businessPostsByAuthor.has(post.author_id)) {
            businessPostsByAuthor.set(post.author_id, []);
        }
        businessPostsByAuthor.get(post.author_id).push(post);
    });
    // Sort posts within each business group chronologically (latest first)
    businessPostsByAuthor.forEach((posts, authorId)=>{
        businessPostsByAuthor.set(authorId, posts.sort((a, b)=>new Date(b.created_at).getTime() - new Date(a.created_at).getTime()));
    });
    // Create priority groups
    return Array.from(businessPostsByAuthor.entries()).map(([authorId, authorPosts])=>{
        const latestPost = authorPosts[0];
        const priority = PLAN_PRIORITY[latestPost.business_plan || 'free'] || 1;
        return {
            authorId,
            priority,
            latestPostTime: new Date(latestPost.created_at).getTime(),
            posts: authorPosts
        };
    }).sort((a, b)=>{
        // Sort by plan priority first
        if (a.priority !== b.priority) {
            return b.priority - a.priority; // Higher priority first
        }
        // If same plan, sort by latest post timestamp
        return b.latestPostTime - a.latestPostTime;
    });
}
function distributePrioritizedBusinessPosts(businessGroups) {
    const result = [];
    // Group businesses by plan priority
    const businessesByPlan = new Map();
    businessGroups.forEach((business)=>{
        if (!businessesByPlan.has(business.priority)) {
            businessesByPlan.set(business.priority, []);
        }
        businessesByPlan.get(business.priority).push(business);
    });
    // Sort plan priorities (highest first)
    const sortedPlanPriorities = Array.from(businessesByPlan.keys()).sort((a, b)=>b - a);
    // Distribute posts: round-robin within each plan tier
    for (const planPriority of sortedPlanPriorities){
        const businessesInPlan = businessesByPlan.get(planPriority);
        // Create queues for round-robin distribution
        const businessPostQueues = businessesInPlan.map((business)=>({
                ...business,
                remainingPosts: [
                    ...business.posts
                ]
            }));
        // Round-robin within this plan tier until all posts are distributed
        while(businessPostQueues.some((queue)=>queue.remainingPosts.length > 0)){
            businessPostQueues.forEach((business)=>{
                if (business.remainingPosts.length > 0) {
                    const post = business.remainingPosts.shift();
                    result.push(post);
                }
            });
        }
    }
    return result;
}
function getPlanDisplayName(planId) {
    const planNames = {
        'enterprise': 'Enterprise',
        'pro': 'Pro',
        'growth': 'Growth',
        'basic': 'Basic',
        'free': 'Free'
    };
    return planNames[planId] || 'Free';
}
function hasPremiumFeatures(planId) {
    const priority = PLAN_PRIORITY[planId] || 1;
    return priority >= PLAN_PRIORITY.growth; // Growth and above
}
}}),
"[project]/lib/utils/feed/diversityEngine.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "applyDiversityRules": (()=>applyDiversityRules),
    "groupPostsByAuthor": (()=>groupPostsByAuthor),
    "roundRobinDistribution": (()=>roundRobinDistribution)
});
function applyDiversityRules(posts, options = {}) {
    const { maxConsecutiveFromSameAuthor = 1 } = options;
    if (posts.length <= 1) return posts;
    const diversifiedPosts = [];
    const remainingPosts = [
        ...posts
    ];
    let lastAuthorId = null;
    let consecutiveCount = 0;
    while(remainingPosts.length > 0){
        let selectedIndex = -1;
        // First, try to find a post from a different author
        for(let i = 0; i < remainingPosts.length; i++){
            const post = remainingPosts[i];
            if (post.author_id !== lastAuthorId) {
                selectedIndex = i;
                break;
            }
        }
        // If no different author found, or we haven't exceeded consecutive limit
        if (selectedIndex === -1 && consecutiveCount < maxConsecutiveFromSameAuthor) {
            selectedIndex = 0; // Take the first available post
        }
        // If still no selection, force diversity by taking first different author
        if (selectedIndex === -1) {
            for(let i = 0; i < remainingPosts.length; i++){
                if (remainingPosts[i].author_id !== lastAuthorId) {
                    selectedIndex = i;
                    break;
                }
            }
            // If still no different author, take first available (edge case)
            if (selectedIndex === -1) selectedIndex = 0;
        }
        const selectedPost = remainingPosts.splice(selectedIndex, 1)[0];
        diversifiedPosts.push(selectedPost);
        // Update tracking variables
        if (selectedPost.author_id === lastAuthorId) {
            consecutiveCount++;
        } else {
            consecutiveCount = 1;
            lastAuthorId = selectedPost.author_id;
        }
    }
    return diversifiedPosts;
}
function groupPostsByAuthor(posts) {
    const grouped = new Map();
    posts.forEach((post)=>{
        if (!grouped.has(post.author_id)) {
            grouped.set(post.author_id, []);
        }
        grouped.get(post.author_id).push(post);
    });
    // Sort posts within each group chronologically (latest first)
    grouped.forEach((authorPosts, authorId)=>{
        grouped.set(authorId, authorPosts.sort((a, b)=>new Date(b.created_at).getTime() - new Date(a.created_at).getTime()));
    });
    return grouped;
}
function roundRobinDistribution(groupedPosts) {
    const result = [];
    const queues = Array.from(groupedPosts.entries()).map(([authorId, posts])=>({
            authorId,
            posts: [
                ...posts
            ]
        }));
    // Continue until all queues are empty
    while(queues.some((queue)=>queue.posts.length > 0)){
        queues.forEach((queue)=>{
            if (queue.posts.length > 0) {
                const post = queue.posts.shift();
                result.push(post);
            }
        });
    }
    return result;
}
}}),
"[project]/lib/utils/feed/optimizedHybridAlgorithm.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "getOptimizedAlgorithmStats": (()=>getOptimizedAlgorithmStats),
    "processOptimizedHybrid": (()=>processOptimizedHybrid),
    "processOptimizedHybridWithInterleaving": (()=>processOptimizedHybridWithInterleaving),
    "validateOptimizedAlgorithm": (()=>validateOptimizedAlgorithm)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2f$feed$2f$planPrioritizer$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/utils/feed/planPrioritizer.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2f$feed$2f$diversityEngine$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/utils/feed/diversityEngine.ts [app-rsc] (ecmascript)");
;
;
function processOptimizedHybrid(posts, options = {}) {
    const { enableDiversity = true, maintainChronologicalFlow = true } = options;
    if (posts.length === 0) return [];
    // Separate customer and business posts
    const customerPosts = posts.filter((post)=>post.post_source === 'customer');
    const businessPosts = posts.filter((post)=>post.post_source === 'business');
    // Process customer posts (maintain chronological order)
    const processedCustomerPosts = processCustomerPostsOptimized(customerPosts);
    // Process business posts (apply plan prioritization)
    const processedBusinessPosts = processBusinessPostsOptimized(businessPosts);
    // Merge both types
    const mergedPosts = mergeOptimizedPosts(processedCustomerPosts, processedBusinessPosts, maintainChronologicalFlow);
    // Apply diversity rules if enabled
    return enableDiversity ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2f$feed$2f$diversityEngine$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["applyDiversityRules"])(mergedPosts) : mergedPosts;
}
/**
 * Process customer posts - simple chronological sort
 */ function processCustomerPostsOptimized(customerPosts) {
    if (customerPosts.length === 0) return [];
    // Sort chronologically (latest first)
    return customerPosts.sort((a, b)=>new Date(b.created_at).getTime() - new Date(a.created_at).getTime());
}
/**
 * Process business posts - apply plan prioritization ONLY to latest post per business
 * Other posts from same business compete purely on timestamp
 */ function processBusinessPostsOptimized(businessPosts) {
    if (businessPosts.length === 0) return [];
    // Group posts by business (author_id)
    const postsByBusiness = new Map();
    businessPosts.forEach((post)=>{
        if (!postsByBusiness.has(post.author_id)) {
            postsByBusiness.set(post.author_id, []);
        }
        postsByBusiness.get(post.author_id).push(post);
    });
    // Sort posts within each business by timestamp (latest first)
    postsByBusiness.forEach((posts, businessId)=>{
        postsByBusiness.set(businessId, posts.sort((a, b)=>new Date(b.created_at).getTime() - new Date(a.created_at).getTime()));
    });
    // Separate latest posts (get plan priority) from other posts (time-based only)
    const latestPostsPerBusiness = [];
    const otherPostsFromBusinesses = [];
    postsByBusiness.forEach((posts, _businessId)=>{
        if (posts.length > 0) {
            // First post is latest (already sorted)
            latestPostsPerBusiness.push(posts[0]);
            // Rest are other posts from same business
            if (posts.length > 1) {
                otherPostsFromBusinesses.push(...posts.slice(1));
            }
        }
    });
    // Sort latest posts by plan priority + timestamp
    const prioritizedLatestPosts = latestPostsPerBusiness.sort((a, b)=>{
        const planA = a.business_plan || 'free';
        const planB = b.business_plan || 'free';
        const priorityA = __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2f$feed$2f$planPrioritizer$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["PLAN_PRIORITY"][planA] || 1;
        const priorityB = __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2f$feed$2f$planPrioritizer$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["PLAN_PRIORITY"][planB] || 1;
        // Sort by plan priority first
        if (priorityA !== priorityB) {
            return priorityB - priorityA; // Higher priority first
        }
        // If same plan, sort by timestamp (latest first)
        return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
    });
    // Sort other posts purely by timestamp (no plan priority)
    const timeBasedOtherPosts = otherPostsFromBusinesses.sort((a, b)=>new Date(b.created_at).getTime() - new Date(a.created_at).getTime());
    // Return prioritized latest posts first, then time-based other posts
    return [
        ...prioritizedLatestPosts,
        ...timeBasedOtherPosts
    ];
}
/**
 * Merge customer and business posts with equal treatment
 * No priority between customer vs business - only plan priority within business posts
 */ function mergeOptimizedPosts(customerPosts, businessPosts, maintainChronologicalFlow) {
    if (customerPosts.length === 0) return businessPosts;
    if (businessPosts.length === 0) return customerPosts;
    if (maintainChronologicalFlow) {
        // Merge all posts by timestamp - equal treatment
        return [
            ...customerPosts,
            ...businessPosts
        ].sort((a, b)=>new Date(b.created_at).getTime() - new Date(a.created_at).getTime());
    } else {
        // Business posts first (due to plan prioritization), then customer posts
        return [
            ...businessPosts,
            ...customerPosts
        ];
    }
}
function processOptimizedHybridWithInterleaving(posts, options = {}) {
    const { enableDiversity = true, maintainChronologicalFlow = true } = options;
    if (posts.length === 0) return [];
    // Separate and process posts
    const customerPosts = posts.filter((post)=>post.post_source === 'customer');
    const businessPosts = posts.filter((post)=>post.post_source === 'business');
    const processedCustomerPosts = processCustomerPostsOptimized(customerPosts);
    const processedBusinessPosts = processBusinessPostsOptimized(businessPosts);
    // Intelligent interleaving
    const interleavedPosts = intelligentInterleave(processedCustomerPosts, processedBusinessPosts, maintainChronologicalFlow);
    // Apply diversity rules if enabled
    return enableDiversity ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2f$feed$2f$diversityEngine$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["applyDiversityRules"])(interleavedPosts) : interleavedPosts;
}
/**
 * Intelligent interleaving of customer and business posts
 */ function intelligentInterleave(customerPosts, businessPosts, respectTimestamp) {
    if (customerPosts.length === 0) return businessPosts;
    if (businessPosts.length === 0) return customerPosts;
    const result = [];
    let customerIndex = 0;
    let businessIndex = 0;
    // Interleave posts while respecting timestamps if enabled
    while(customerIndex < customerPosts.length || businessIndex < businessPosts.length){
        const customerPost = customerPosts[customerIndex];
        const businessPost = businessPosts[businessIndex];
        if (!customerPost && businessPost) {
            // Only business posts left
            result.push(businessPost);
            businessIndex++;
        } else if (customerPost && !businessPost) {
            // Only customer posts left
            result.push(customerPost);
            customerIndex++;
        } else if (customerPost && businessPost) {
            // Both available - decide based on timestamp or alternating pattern
            if (respectTimestamp) {
                const customerTime = new Date(customerPost.created_at).getTime();
                const businessTime = new Date(businessPost.created_at).getTime();
                if (businessTime >= customerTime) {
                    result.push(businessPost);
                    businessIndex++;
                } else {
                    result.push(customerPost);
                    customerIndex++;
                }
            } else {
                // Alternating pattern - business posts get slight preference due to plan prioritization
                if (result.length % 2 === 0) {
                    result.push(businessPost);
                    businessIndex++;
                } else {
                    result.push(customerPost);
                    customerIndex++;
                }
            }
        }
    }
    return result;
}
function getOptimizedAlgorithmStats(originalPosts, processedPosts) {
    const planDistribution = {};
    const businessPosts = processedPosts.filter((p)=>p.post_source === 'business');
    const customerPosts = processedPosts.filter((p)=>p.post_source === 'customer');
    businessPosts.forEach((post)=>{
        const plan = post.business_plan || 'free';
        planDistribution[plan] = (planDistribution[plan] || 0) + 1;
    });
    const postsLost = originalPosts.length - processedPosts.length;
    const efficiency = processedPosts.length / originalPosts.length;
    return {
        originalCount: originalPosts.length,
        processedCount: processedPosts.length,
        customerPosts: customerPosts.length,
        businessPosts: businessPosts.length,
        planDistribution,
        postsLost,
        efficiency
    };
}
function validateOptimizedAlgorithm(originalPosts, processedPosts) {
    const issues = [];
    if (originalPosts.length !== processedPosts.length) {
        issues.push(`Post count mismatch: ${originalPosts.length} → ${processedPosts.length}`);
    }
    const originalIds = new Set(originalPosts.map((p)=>p.id));
    const processedIds = new Set(processedPosts.map((p)=>p.id));
    const lostPosts = [];
    originalIds.forEach((id)=>{
        if (!processedIds.has(id)) {
            lostPosts.push(id);
        }
    });
    if (lostPosts.length > 0) {
        issues.push(`Lost posts: ${lostPosts.join(', ')}`);
    }
    const efficiency = processedPosts.length / originalPosts.length;
    return {
        isValid: issues.length === 0,
        issues,
        efficiency
    };
}
}}),
"[project]/lib/utils/feed/postCreationHandler.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "PostCreationStateManager": (()=>PostCreationStateManager),
    "clearPostCreationState": (()=>clearPostCreationState),
    "createPostCreationState": (()=>createPostCreationState),
    "handlePostCreationFeed": (()=>handlePostCreationFeed),
    "isPostCreationStateValid": (()=>isPostCreationStateValid),
    "markPostAsJustCreated": (()=>markPostAsJustCreated),
    "processFeedWithCreationHandling": (()=>processFeedWithCreationHandling),
    "shouldShowJustPostedIndicator": (()=>shouldShowJustPostedIndicator),
    "usePostCreationState": (()=>usePostCreationState)
});
function handlePostCreationFeed(algorithmicPosts, creationState) {
    if (!creationState.justCreatedPostId) {
        // No recent post creation, return normal algorithmic feed
        return {
            posts: algorithmicPosts,
            hasJustCreatedPost: false
        };
    }
    // Find the just-created post in the algorithmic results
    const justCreatedPost = algorithmicPosts.find((post)=>post.id === creationState.justCreatedPostId);
    if (!justCreatedPost) {
        // Post not found in current page, return normal feed
        // (Post might be on a different page due to algorithm)
        return {
            posts: algorithmicPosts,
            hasJustCreatedPost: false
        };
    }
    // Remove the post from its algorithmic position
    const otherPosts = algorithmicPosts.filter((post)=>post.id !== creationState.justCreatedPostId);
    // Show just-created post at the top
    return {
        posts: [
            justCreatedPost,
            ...otherPosts
        ],
        hasJustCreatedPost: true,
        justCreatedPost
    };
}
function createPostCreationState(postId, sessionId) {
    return {
        justCreatedPostId: postId,
        sessionId: sessionId || generateSessionId(),
        createdAt: new Date().toISOString()
    };
}
function isPostCreationStateValid(creationState, currentSessionId) {
    if (!creationState.justCreatedPostId) return false;
    // Check if it's the same session
    if (creationState.sessionId && currentSessionId) {
        return creationState.sessionId === currentSessionId;
    }
    // Check if creation was recent (within last 5 minutes as fallback)
    if (creationState.createdAt) {
        const createdTime = new Date(creationState.createdAt).getTime();
        const now = new Date().getTime();
        const fiveMinutes = 5 * 60 * 1000;
        return now - createdTime < fiveMinutes;
    }
    return false;
}
function clearPostCreationState() {
    return {};
}
/**
 * Generate a simple session ID for tracking
 */ function generateSessionId() {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}
function processFeedWithCreationHandling(algorithmicPosts, totalCount, hasMore, creationState) {
    if (!creationState || !creationState.justCreatedPostId) {
        // No post creation state, return normal feed
        return {
            success: true,
            message: 'Posts fetched successfully',
            data: {
                items: algorithmicPosts,
                totalCount,
                hasMore,
                hasJustCreatedPost: false
            }
        };
    }
    // Handle post creation display
    const feedWithCreation = handlePostCreationFeed(algorithmicPosts, creationState);
    return {
        success: true,
        message: 'Posts fetched successfully',
        data: {
            items: feedWithCreation.posts,
            totalCount,
            hasMore,
            hasJustCreatedPost: feedWithCreation.hasJustCreatedPost,
            justCreatedPost: feedWithCreation.justCreatedPost,
            creationState
        }
    };
}
const PostCreationStateManager = {
    /**
   * Save post creation state to session storage
   */ save (state) {
        if ("TURBOPACK compile-time falsy", 0) {
            "TURBOPACK unreachable";
        }
    },
    /**
   * Load post creation state from session storage
   */ load () {
        if ("TURBOPACK compile-time falsy", 0) {
            "TURBOPACK unreachable";
        }
        return {};
    },
    /**
   * Clear post creation state from session storage
   */ clear () {
        if ("TURBOPACK compile-time falsy", 0) {
            "TURBOPACK unreachable";
        }
    },
    /**
   * Check if current state is valid and clear if not
   */ validateAndClean () {
        const state = this.load();
        const currentSessionId = this.getCurrentSessionId();
        if (!isPostCreationStateValid(state, currentSessionId)) {
            this.clear();
            return {};
        }
        return state;
    },
    /**
   * Get or create current session ID
   */ getCurrentSessionId () {
        if ("TURBOPACK compile-time falsy", 0) {
            "TURBOPACK unreachable";
        }
        return generateSessionId();
    }
};
function usePostCreationState() {
    const load = ()=>PostCreationStateManager.validateAndClean();
    const save = (state)=>PostCreationStateManager.save(state);
    const clear = ()=>PostCreationStateManager.clear();
    return {
        load,
        save,
        clear
    };
}
function markPostAsJustCreated(postId) {
    const state = createPostCreationState(postId, PostCreationStateManager.getCurrentSessionId());
    PostCreationStateManager.save(state);
}
function shouldShowJustPostedIndicator(post, creationState) {
    if (!creationState || !creationState.justCreatedPostId) return false;
    return post.id === creationState.justCreatedPostId;
}
}}),
"[project]/lib/actions/posts/unifiedFeed.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "getUnifiedFeedPosts": (()=>getUnifiedFeedPosts),
    "getUnifiedFeedPostsWithAuthors": (()=>getUnifiedFeedPostsWithAuthors)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$client$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/supabase/client.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/supabase/constants.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2f$feed$2f$optimizedHybridAlgorithm$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/utils/feed/optimizedHybridAlgorithm.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2f$feed$2f$postCreationHandler$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/utils/feed/postCreationHandler.ts [app-rsc] (ecmascript)");
;
;
;
;
// getUserProfile
async function getUserProfile(userId) {
    const supabase = (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$client$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])();
    try {
        const { data, error } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].CUSTOMER_PROFILES).select("*").eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID, userId).single();
        if (error) {
            console.error("Error fetching user profile:", error.message);
            return {
                data: null,
                error: error.message
            };
        }
        return {
            data,
            error: null
        };
    } catch (err) {
        console.error("Unexpected error fetching user profile:", err);
        return {
            data: null,
            error: "An unexpected error occurred."
        };
    }
}
async function getUnifiedFeedPosts(params, creationState) {
    const supabase = (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$client$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])();
    const { filter = 'smart', page = 1, limit = 10, city_slug, state_slug, locality_slug, pincode } = params;
    try {
        // Get current user for smart and subscribed filters
        const { data: { user } } = await supabase.auth.getUser();
        // Build base query
        let query = supabase.from('unified_posts').select('*', {
            count: 'exact'
        });
        // Apply filters based on filter type
        switch(filter){
            case 'smart':
                if (user) {
                    // Get user's subscribed businesses for smart feed
                    const { data: subscriptions } = await supabase.from('subscriptions').select('business_profile_id').eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].USER_ID, user.id);
                    const subscribedBusinessIds = subscriptions?.map((s)=>s.business_profile_id) || [];
                    // Try to get user's location from both customer and business profiles
                    const [customerProfile, businessProfile] = await Promise.all([
                        getUserProfile(user.id),
                        supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].BUSINESS_PROFILES).select(`${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].CITY_SLUG}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].STATE_SLUG}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].LOCALITY_SLUG}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].PINCODE}`).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID, user.id).single()
                    ]);
                    // Use whichever profile exists
                    const userProfile = customerProfile.data || businessProfile.data;
                    // Build smart feed conditions
                    const conditions = [];
                    // Subscribed businesses
                    if (subscribedBusinessIds.length > 0) {
                        conditions.push(`and(post_source.eq.business,author_id.in.(${subscribedBusinessIds.join(',')}))`);
                    }
                    // User's own posts (check both customer and business posts)
                    conditions.push(`and(post_source.eq.customer,author_id.eq.${user.id})`);
                    conditions.push(`and(post_source.eq.business,author_id.eq.${user.id})`);
                    // Local posts based on user location
                    if (userProfile?.locality_slug) {
                        conditions.push(`${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].LOCALITY_SLUG}.eq.${userProfile.locality_slug}`);
                    }
                    if (userProfile?.pincode) {
                        conditions.push(`${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].PINCODE}.eq.${userProfile.pincode}`);
                    }
                    if (userProfile?.city_slug) {
                        conditions.push(`${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].CITY_SLUG}.eq.${userProfile.city_slug}`);
                    }
                    if (conditions.length > 0) {
                        query = query.or(conditions.join(','));
                    }
                }
                break;
            case 'subscribed':
                if (user) {
                    const { data: subscriptions } = await supabase.from('subscriptions').select('business_profile_id').eq('user_id', user.id);
                    const subscribedBusinessIds = subscriptions?.map((s)=>s.business_profile_id) || [];
                    if (subscribedBusinessIds.length > 0) {
                        query = query.eq('post_source', 'business').in('author_id', subscribedBusinessIds);
                    } else {
                        // No subscriptions, return empty result
                        return {
                            success: true,
                            message: 'No subscribed businesses found',
                            data: {
                                items: [],
                                totalCount: 0,
                                hasMore: false,
                                hasJustCreatedPost: false
                            }
                        };
                    }
                }
                break;
            case 'locality':
                if (locality_slug) {
                    query = query.eq('locality_slug', locality_slug);
                } else if (user) {
                    // If no locality_slug provided, get user's locality from their profile
                    const [customerProfile, businessProfile] = await Promise.all([
                        supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].CUSTOMER_PROFILES).select(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].LOCALITY_SLUG).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID, user.id).single(),
                        supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].BUSINESS_PROFILES).select(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].LOCALITY_SLUG).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID, user.id).single()
                    ]);
                    const userLocality = customerProfile.data?.locality_slug || businessProfile.data?.locality_slug;
                    if (userLocality) {
                        query = query.eq('locality_slug', userLocality);
                    }
                }
                break;
            case 'pincode':
                if (pincode) {
                    query = query.eq('pincode', pincode);
                } else if (user) {
                    // If no pincode provided, get user's pincode from their profile
                    const [customerProfile, businessProfile] = await Promise.all([
                        supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].CUSTOMER_PROFILES).select(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].PINCODE).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID, user.id).single(),
                        supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].BUSINESS_PROFILES).select(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].PINCODE).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID, user.id).single()
                    ]);
                    const userPincode = customerProfile.data?.pincode || businessProfile.data?.pincode;
                    if (userPincode) {
                        query = query.eq('pincode', userPincode);
                    }
                }
                break;
            case 'city':
                if (city_slug) {
                    query = query.eq('city_slug', city_slug);
                } else if (user) {
                    // If no city_slug provided, get user's city from their profile
                    const [customerProfile, businessProfile] = await Promise.all([
                        supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].CUSTOMER_PROFILES).select(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].CITY_SLUG).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID, user.id).single(),
                        supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].BUSINESS_PROFILES).select(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].CITY_SLUG).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID, user.id).single()
                    ]);
                    const userCity = customerProfile.data?.city_slug || businessProfile.data?.city_slug;
                    if (userCity) {
                        query = query.eq('city_slug', userCity);
                    }
                }
                break;
            case 'state':
                if (state_slug) {
                    query = query.eq('state_slug', state_slug);
                } else if (user) {
                    // If no state_slug provided, get user's state from their profile
                    const [customerProfile, businessProfile] = await Promise.all([
                        supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].CUSTOMER_PROFILES).select(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].STATE_SLUG).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID, user.id).single(),
                        supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].BUSINESS_PROFILES).select(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].STATE_SLUG).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID, user.id).single()
                    ]);
                    const userState = customerProfile.data?.state_slug || businessProfile.data?.state_slug;
                    if (userState) {
                        query = query.eq('state_slug', userState);
                    }
                }
                break;
            case 'all':
                break;
        }
        // Fetch exactly the target number of posts to prevent post loss
        // Algorithm will arrange these posts optimally without losing any content
        const from = (page - 1) * limit; // Standard pagination
        const to = from + limit - 1;
        // Execute query with chronological ordering (prioritization applied client-side)
        const { data, error, count } = await query.order('created_at', {
            ascending: false
        }).range(from, to);
        if (error) {
            console.error('Error fetching unified feed posts:', error);
            return {
                success: false,
                message: 'Failed to fetch posts',
                error: error.message
            };
        }
        // Apply optimized hybrid algorithm to ALL feed types
        // Processes exactly the fetched posts without losing any content
        // Business posts get plan prioritization, customer posts maintain chronological order
        // Works with location filters (locality, pincode, city, state, all)
        const prioritizedData = data ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2f$feed$2f$optimizedHybridAlgorithm$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["processOptimizedHybrid"])(data, {
            enableDiversity: true,
            maintainChronologicalFlow: true
        }) : [];
        const totalCount = count || 0;
        // Standard pagination logic - no posts lost
        const hasMore = prioritizedData.length === limit && from + limit < totalCount;
        // Handle post creation state for immediate feedback
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2f$feed$2f$postCreationHandler$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["processFeedWithCreationHandling"])(prioritizedData, totalCount, hasMore, creationState);
    } catch (error) {
        console.error('Unexpected error in getUnifiedFeedPosts:', error);
        return {
            success: false,
            message: 'An unexpected error occurred',
            error: error instanceof Error ? error.message : 'Unknown error'
        };
    }
}
async function getUnifiedFeedPostsWithAuthors(params, creationState) {
    // Since author information is now included in the unified_posts view,
    // we can just return the result from getUnifiedFeedPosts directly
    return await getUnifiedFeedPosts(params, creationState);
}
}}),
"[project]/app/(dashboard)/dashboard/customer/page.tsx [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>CustomerDashboardPage),
    "metadata": (()=>metadata)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/supabase/server.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$api$2f$navigation$2e$react$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/next/dist/api/navigation.react-server.js [app-rsc] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$components$2f$navigation$2e$react$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/client/components/navigation.react-server.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$feed$2f$ModernCustomerFeedList$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/feed/ModernCustomerFeedList.tsx [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$customerProfiles$2f$addressValidation$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/actions/customerProfiles/addressValidation.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$posts$2f$unifiedFeed$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/actions/posts/unifiedFeed.ts [app-rsc] (ecmascript)");
;
;
;
;
;
;
const metadata = {
    title: "Feed",
    description: "View your personalized feed and stay updated"
};
async function CustomerDashboardPage() {
    const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])();
    // Check if user is authenticated
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$components$2f$navigation$2e$react$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["redirect"])('/login?message=Please log in to view your dashboard');
    }
    // Check if customer has complete address
    await (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$customerProfiles$2f$addressValidation$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["requireCompleteProfile"])(user.id);
    // Get the user's customer profile
    const { data: customerProfile, error: profileError } = await supabase.from('customer_profiles').select('name').eq('id', user.id).single();
    if (profileError) {
        console.error('Error fetching customer profile:', profileError);
    }
    // Get the user's city from subscribed businesses
    let citySlug;
    const { data: subscriptions } = await supabase.from('subscriptions').select(`
      business_profiles (
        city_slug
      )
    `).eq('user_id', user.id).limit(1);
    if (subscriptions && subscriptions.length > 0 && subscriptions[0].business_profiles) {
        const profile = subscriptions[0].business_profiles;
        citySlug = typeof profile === 'object' && profile !== null && 'city_slug' in profile ? profile.city_slug || undefined : undefined;
    }
    // Use smart feed as the default filter
    const initialFilter = 'smart';
    // Get initial posts using the unified smart feed algorithm
    const initialFeedResult = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$posts$2f$unifiedFeed$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getUnifiedFeedPostsWithAuthors"])({
        filter: initialFilter,
        page: 1,
        limit: 10
    });
    const posts = initialFeedResult.success ? initialFeedResult.data?.items || [] : [];
    const hasMore = initialFeedResult.success ? initialFeedResult.data?.hasMore || false : false;
    if (!initialFeedResult.success) {
        console.error('Error fetching initial posts:', initialFeedResult.error);
    }
    const customerName = customerProfile?.name || "Valued Customer";
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$feed$2f$ModernCustomerFeedList$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"], {
        initialPosts: posts,
        initialTotalCount: 0,
        initialHasMore: hasMore,
        initialFilter: initialFilter,
        citySlug: citySlug,
        userName: customerName
    }, void 0, false, {
        fileName: "[project]/app/(dashboard)/dashboard/customer/page.tsx",
        lineNumber: 79,
        columnNumber: 5
    }, this);
}
}}),
"[project]/app/(dashboard)/dashboard/customer/page.tsx [app-rsc] (ecmascript, Next.js server component)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.n(__turbopack_context__.i("[project]/app/(dashboard)/dashboard/customer/page.tsx [app-rsc] (ecmascript)"));
}}),

};

//# sourceMappingURL=_b1186b30._.js.map