{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 24, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/actions/shared/upload-customer-post-media.ts"], "sourcesContent": ["\"use server\";\n\nimport { createClient } from \"@/utils/supabase/server\";\nimport { SupabaseClient } from \"@supabase/supabase-js\";\nimport { Database } from \"@/types/supabase\";\n\nimport { getCustomerPostImagePath } from \"@/lib/utils/storage-paths\";\n\nexport interface CustomerPostMediaUploadResult {\n  success: boolean;\n  url?: string;\n  error?: string;\n}\n\n/**\n * Upload and process image for customer post\n * Future-proof structure: {userId}/posts/{year}/{month}/{postId}/image_0_{timestamp}.webp\n */\nexport async function uploadCustomerPostImage(\n  formData: FormData,\n  postId: string,\n  postCreatedAt?: string\n): Promise<CustomerPostMediaUploadResult> {\n  const supabase = await createClient();\n\n  // Get the current user\n  const {\n    data: { user },\n    error: authError,\n  } = await supabase.auth.getUser();\n\n  if (authError || !user) {\n    return { success: false, error: \"User not authenticated.\" };\n  }\n\n  const userId = user.id;\n  const imageFile = formData.get(\"imageFile\") as File | null;\n\n  if (!imageFile) {\n    return { success: false, error: \"No image file provided.\" };\n  }\n\n  // Validate file type\n  const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/gif'];\n  if (!allowedTypes.includes(imageFile.type)) {\n    return {\n      success: false,\n      error: \"Invalid file type. Only JPEG, PNG, WebP, and GIF images are allowed.\"\n    };\n  }\n\n  // Validate file size (15MB limit)\n  const maxSize = 15 * 1024 * 1024; // 15MB\n  if (imageFile.size > maxSize) {\n    return {\n      success: false,\n      error: \"File size exceeds 15MB limit.\"\n    };\n  }\n\n  // Validate that the post belongs to the user\n  const { data: existingPost, error: postError } = await supabase\n    .from('customer_posts')\n    .select('id, customer_id')\n    .eq('id', postId)\n    .eq('customer_id', userId)\n    .single();\n\n  if (postError || !existingPost) {\n    return {\n      success: false,\n      error: \"Post not found or you don't have permission to upload images for this post.\"\n    };\n  }\n\n  try {\n    // Create scalable path structure for billions of users\n    const timestamp = Date.now() + Math.floor(Math.random() * 1000);\n    const bucketName = \"customers\";\n    const imagePath = getCustomerPostImagePath(userId, postId, 0, timestamp, postCreatedAt);\n\n    // File is already compressed on client-side, just upload it\n    const fileBuffer = Buffer.from(await imageFile.arrayBuffer());\n\n    // Use admin client for storage operations to bypass RLS\n    const adminSupabase = await createClient() as SupabaseClient<Database>;\n\n    // Upload to Supabase Storage using admin client\n    const { error: uploadError } = await adminSupabase.storage\n      .from(bucketName)\n      .upload(imagePath, fileBuffer, {\n        contentType: imageFile.type, // Use original file type (already compressed)\n        upsert: true\n      });\n\n    if (uploadError) {\n      console.error(\"Customer Post Image Upload Error:\", uploadError);\n      return {\n        success: false,\n        error: `Failed to upload image: ${uploadError.message}`,\n      };\n    }\n\n    // Get the public URL using admin client\n    const { data: urlData } = adminSupabase.storage\n      .from(bucketName)\n      .getPublicUrl(imagePath);\n\n    if (!urlData?.publicUrl) {\n      return {\n        success: false,\n        error: \"Could not retrieve public URL after upload.\",\n      };\n    }\n\n    return {\n      success: true,\n      url: urlData.publicUrl,\n    };\n\n  } catch (error) {\n    console.error(\"Error processing customer post image:\", error);\n    return {\n      success: false,\n      error: \"Failed to process image. Please try a different image.\"\n    };\n  }\n}\n"], "names": [], "mappings": ";;;;;;IAkBsB,0BAAA,WAAA,GAAA,CAAA,GAAA,yNAAA,CAAA,wBAAA,EAAA,8CAAA,yNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,yNAAA,CAAA,mBAAA,EAAA", "debugId": null}}]}