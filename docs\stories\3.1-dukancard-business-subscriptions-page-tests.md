---
Status: Draft
Story: |
  As a developer, I need to implement comprehensive unit and integration tests for the `BusinessSubscriptionsPage` server component in the `dukancard` project. This page is responsible for fetching and displaying "Subscribers" and "Following" data for a business user, including handling authentication, pagination, search, and tab switching. The tests should ensure the page behaves correctly under various conditions, including successful data loads, empty states, error conditions, and proper handling of URL parameters.

Acceptance Criteria:
- **Authentication:**
    - Given an unauthenticated user, when accessing `/dashboard/business/subscriptions`, then the user is redirected to `/login`.
    - Given an authenticated business user, when accessing `/dashboard/business/subscriptions`, then the page loads successfully.
- **Initial Data Fetching & Rendering:**
    - Given a business user with existing "Subscribers" and "Following" data, when the page loads, then `fetchBusinessSubscribers` and `fetchBusinessFollowing` are called with correct initial parameters (user ID, page 1, limit 1, empty search term for counts).
    - And the `BusinessSubscriptionsPageClient` component receives the correct `initialSubscribers`, `subscribersCount`, `subscribersCurrentPage`, `initialFollowing`, `followingCount`, `followingCurrentPage`, `searchTerm`, and `activeTab` props.
    - And the page correctly displays the "Subscribers" tab content by default.
    - And the counts for "Subscribers" and "Following" are accurately displayed on their respective tab buttons.
- **Tab Switching:**
    - Given the page is loaded on the "Subscribers" tab, when the "Following" tab is clicked, then the `BusinessSubscriptionsPageClient` receives updated props reflecting the "Following" tab, and the URL `tab` parameter is set to `following`.
    - And the search term is cleared and pagination is reset to page 1 when switching tabs.
    - Given the page is loaded on the "Following" tab, when the "Subscribers" tab is clicked, then the `BusinessSubscriptionsPageClient` receives updated props reflecting the "Subscribers" tab, and the URL `tab` parameter is set to `subscribers`.
    - And the search term is cleared and pagination is reset to page 1 when switching tabs.
- **Pagination:**
    - Given the "Subscribers" tab is active and there are multiple pages of data, when a user navigates to page 2 (e.g., via URL parameter), then `fetchBusinessSubscribers` is called with `page: 2`.
    - Given the "Following" tab is active and there are multiple pages of data, when a user navigates to page 3, then `fetchBusinessFollowing` is called with `page: 3`.
- **Search Functionality (Following Tab Only):**
    - Given the "Following" tab is active, when a user enters a search term, then `fetchBusinessFollowing` is called with the correct `searchTerm`.
    - And pagination is reset to page 1 when a search term is applied.
    - Given the "Following" tab is active and a search term is applied, when the search term is cleared, then `fetchBusinessFollowing` is called with an empty `searchTerm`.
    *   And pagination is reset to page 1 when the search term is cleared.
- **Loading States:**
    - Given the page is loading data, then the `SubscriptionListSkeleton` is displayed within the `Suspense` fallback.
    - Given the page has finished loading, then the `SubscriptionListSkeleton` is no longer displayed.
- **Error Handling:**
    - Given `fetchBusinessSubscribers` or `fetchBusinessFollowing` throws an error, then an `Alert` component with `variant="destructive"` is displayed, showing an appropriate error message.
    - And the page does not crash.
- **Edge Cases:**
    - Given no subscription data exists for either tab, then the respective lists are empty and appropriate empty states are displayed (handled by client components).
    - Given `searchParams` are missing or invalid, then default values are used (page 1, empty search term, 'subscribers' tab).

Tasks:
1. Create a new test file: `C:\web-app\dukancard\__tests__\app\(dashboard)\dashboard\business\subscriptions\page.test.tsx`.
2. Set up a testing environment that can mock Supabase server-side functions (`createClient`, `supabase.auth.getUser`, `supabase.from`).
3. Write unit tests for the `BusinessSubscriptionsPage` component covering all acceptance criteria.
4. Ensure tests use `render` from `@testing-library/react` or similar for component rendering.
5. Mock the `fetchBusinessSubscribers` and `fetchBusinessFollowing` functions to control data responses (success, empty, error).
6. Simulate `searchParams` changes to test pagination, search, and tab switching.
7. Verify redirects for unauthenticated users.
8. Verify correct data is passed to `BusinessSubscriptionsPageClient`.
9. Verify error message display on data fetching failures.
10. Ensure the `Suspense` fallback (skeleton) is shown during loading.

File: `C:\web-app\dukancard\app\(dashboard)\dashboard\business\subscriptions\page.tsx`
Platform: dukancard
---