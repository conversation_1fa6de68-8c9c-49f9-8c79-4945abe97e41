---
Status: Draft
Story: |
  As a developer, I need to implement comprehensive unit and integration tests for the `CustomerReviewsPage` server component in the `dukancard` project. This page is responsible for fetching and displaying "My Reviews" data for a customer user, including handling authentication, profile completion checks, and fetching review counts. The tests should ensure the page behaves correctly under various conditions, including successful data loads, empty states, error conditions, and proper redirection.

Acceptance Criteria:
- **Authentication & Profile Completion:**
    - Given an unauthenticated user, when accessing `/dashboard/customer/reviews`, then the user is redirected to `/login`.
    - Given an authenticated user with an incomplete profile, when accessing `/dashboard/customer/reviews`, then `requireCompleteProfile` is called.
    - Given an authenticated user with a complete profile, when accessing `/dashboard/customer/reviews`, then the page loads successfully.
- **Initial Data Fetching & Rendering:**
    - Given a customer user with existing reviews, when the page loads, then it fetches the count of "My Reviews" (reviews where `user_id` matches authenticated user's ID).
    - And the `ReviewsPageClient` component receives the correct `reviewsCount` prop.
    - And the page correctly displays the `ReviewsPageClient`.
- **Error Handling:**
    - Given an error occurs during fetching review counts, then the `ReviewsPageClient` is still rendered with `reviewsCount` set to 0.
    - And the page does not crash.

Tasks:
1. Create a new test file: `C:\web-app\dukancard\__tests__\app\(dashboard)\dashboard\customer\reviews\page.test.tsx`.
2. Set up a testing environment that can mock Supabase server-side functions (`createClient`, `supabase.auth.getUser`), `next/navigation` (`redirect`), and `@/lib/actions/customerProfiles/addressValidation` (`requireCompleteProfile`).
3. Write unit tests for the `CustomerReviewsPage` component covering all acceptance criteria.
4. Mock the `supabase.auth.getUser` to simulate authenticated and unauthenticated users.
5. Mock `supabase.from('ratings_reviews').select().eq().count()` to simulate various review count scenarios (e.g., zero reviews, some reviews, error during fetch).
6. Verify redirects for unauthenticated users.
7. Verify `requireCompleteProfile` is called.
8. Verify correct props are passed to `ReviewsPageClient`.
9. Verify error handling when fetching review counts.

File: `C:\web-app\dukancard\app\(dashboard)\dashboard\customer\reviews\page.tsx`
Platform: dukancard
---