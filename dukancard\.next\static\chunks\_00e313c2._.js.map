{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/business/plan/components/PaymentMethodLimitationsDialog.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { <PERSON><PERSON> } from \"@/components/ui/button\";\r\nimport {\r\n  Dialog,\r\n  DialogContent,\r\n  DialogDescription,\r\n  DialogFooter,\r\n  DialogHeader,\r\n  DialogTitle,\r\n} from \"@/components/ui/dialog\";\r\nimport { AlertCircle } from \"lucide-react\";\r\n\r\ninterface PaymentMethodLimitationsDialogProps {\r\n  open: boolean;\r\n  onOpenChange: (_open: boolean) => void;\r\n  onContinue: () => void;\r\n  paymentMethod: string;\r\n}\r\n\r\nexport function PaymentMethodLimitationsDialog({\r\n  open,\r\n  onOpenChange,\r\n  onContinue,\r\n  paymentMethod,\r\n}: PaymentMethodLimitationsDialogProps) {\r\n  return (\r\n    <Dialog open={open} onOpenChange={onOpenChange}>\r\n      <DialogContent className=\"sm:max-w-md\">\r\n        <DialogHeader>\r\n          <DialogTitle className=\"flex items-center gap-2\">\r\n            <AlertCircle className=\"h-5 w-5 text-yellow-500\" />\r\n            Payment Method Limitation\r\n          </DialogTitle>\r\n          <DialogDescription>\r\n            Important: Due to Razorpay and RBI regulations, subscriptions with{\" \"}\r\n            {paymentMethod} payment methods require a special process for\r\n            changes.\r\n          </DialogDescription>\r\n        </DialogHeader>\r\n        <div className=\"space-y-4 py-4 text-sm\">\r\n          <p>\r\n            When you click &quot;Continue&quot;, the following process will\r\n            happen:\r\n          </p>\r\n          <ol className=\"list-decimal pl-5 space-y-2\">\r\n            <li>\r\n              We&apos;ll create a new subscription with your selected plan\r\n            </li>\r\n            <li>You&apos;ll need to complete the payment authorization</li>\r\n            <li>\r\n              <strong>Only after the new subscription is active</strong>,\r\n              we&apos;ll automatically cancel your previous subscription\r\n            </li>\r\n          </ol>\r\n          <p className=\"text-muted-foreground\">\r\n            This ensures you don&apos;t lose access to your current subscription\r\n            if the new payment fails or is abandoned. Your current subscription\r\n            will remain active until the new one is successfully set up.\r\n          </p>\r\n        </div>\r\n        <DialogFooter>\r\n          <Button variant=\"outline\" onClick={() => onOpenChange(false)}>\r\n            Cancel\r\n          </Button>\r\n          <Button onClick={onContinue}>Continue</Button>\r\n        </DialogFooter>\r\n      </DialogContent>\r\n    </Dialog>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAQA;AAXA;;;;;AAoBO,SAAS,+BAA+B,EAC7C,IAAI,EACJ,YAAY,EACZ,UAAU,EACV,aAAa,EACuB;IACpC,qBACE,6LAAC,8HAAA,CAAA,SAAM;QAAC,MAAM;QAAM,cAAc;kBAChC,cAAA,6LAAC,8HAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,6LAAC,8HAAA,CAAA,eAAY;;sCACX,6LAAC,8HAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,6LAAC,uNAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;gCAA4B;;;;;;;sCAGrD,6LAAC,8HAAA,CAAA,oBAAiB;;gCAAC;gCACkD;gCAClE;gCAAc;;;;;;;;;;;;;8BAInB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;sCAAE;;;;;;sCAIH,6LAAC;4BAAG,WAAU;;8CACZ,6LAAC;8CAAG;;;;;;8CAGJ,6LAAC;8CAAG;;;;;;8CACJ,6LAAC;;sDACC,6LAAC;sDAAO;;;;;;wCAAkD;;;;;;;;;;;;;sCAI9D,6LAAC;4BAAE,WAAU;sCAAwB;;;;;;;;;;;;8BAMvC,6LAAC,8HAAA,CAAA,eAAY;;sCACX,6LAAC,8HAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAU,SAAS,IAAM,aAAa;sCAAQ;;;;;;sCAG9D,6LAAC,8HAAA,CAAA,SAAM;4BAAC,SAAS;sCAAY;;;;;;;;;;;;;;;;;;;;;;;AAKvC;KAlDgB", "debugId": null}}, {"offset": {"line": 181, "column": 0}, "map": {"version": 3, "file": "circle-alert.js", "sources": ["file://C%3A/web-app/dukancard/node_modules/lucide-react/src/icons/circle-alert.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['circle', { cx: '12', cy: '12', r: '10', key: '1mglay' }],\n  ['line', { x1: '12', x2: '12', y1: '8', y2: '12', key: '1pkeuh' }],\n  ['line', { x1: '12', x2: '12.01', y1: '16', y2: '16', key: '4dfq90' }],\n];\n\n/**\n * @component @name CircleAlert\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSIxMCIgLz4KICA8bGluZSB4MT0iMTIiIHgyPSIxMiIgeTE9IjgiIHkyPSIxMiIgLz4KICA8bGluZSB4MT0iMTIiIHgyPSIxMi4wMSIgeTE9IjE2IiB5Mj0iMTYiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/circle-alert\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst CircleAlert = createLucideIcon('CircleAlert', __iconNode);\n\nexport default CircleAlert;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA,CAAA;IAClC;QAAC,QAAU,CAAA;QAAA,CAAA;YAAE,EAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAI,CAAA,CAAA,CAAA,IAAA,CAAM;YAAA,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA,CAAA;IACzD;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAE;YAAA,CAAA,CAAA,EAAI,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAI,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,EAAA,CAAI,CAAA,CAAA,CAAA,CAAK,CAAA;YAAA,CAAA,CAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAM;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA,CAAA;IACjE;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAE;YAAA,CAAA,CAAA,EAAI,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAI,CAAS,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,EAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAM,CAAA;YAAA,CAAA,CAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAM;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;CACvE,CAAA;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,WAAA,CAAc,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAe,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}]}