---
Status: Draft
Story: |
  As a developer, I need to enhance the unit tests for the `actions.ts` file within the `dukancard` project's business likes module. This file contains server actions (`fetchBusinessLikesReceived` and `fetchBusinessMyLikes`) responsible for fetching like data from the `socialService`. The tests should ensure these actions correctly interact with the `socialService`, handle various data scenarios (including pagination and search), and gracefully manage authentication and service-level errors.

Acceptance Criteria:
- **`fetchBusinessMyLikes` Functionality:**
    - Given a valid `businessId`, `page`, `limit`, and `searchTerm`, when `fetchBusinessMyLikes` is called, then `socialService.likesService.fetchLikes` is invoked with the correct parameters.
    - Given `socialService.likesService.fetchLikes` returns data, then `fetchBusinessMyLikes` returns the data correctly, including `items`, `totalCount`, `hasMore`, and `currentPage`.
    - Given `socialService.likesService.fetchLikes` returns an empty array, then `fetchBusinessMyLikes` returns an empty `items` array, `totalCount` of 0, `hasMore` as `false`, and `currentPage` as the requested page.
    - Given `socialService.likesService.fetchLikes` throws an error, then `fetchBusinessMyLikes` catches the error and returns an empty `items` array, `totalCount` of 0, `hasMore` as `false`, and `currentPage` as 1.
    - Given the user is not authenticated, then `fetchBusinessMyLikes` returns an empty `items` array, `totalCount` of 0, `hasMore` as `false`, and `currentPage` as 1.
    - Given `searchTerm` is provided, then `socialService.likesService.fetchLikes` is called with the correct `searchTerm`.
    - Given `page` and `limit` are provided, then `socialService.likesService.fetchLikes` is called with the correct pagination parameters.
- **`fetchBusinessLikesReceived` Functionality:**
    - Given a valid `businessId`, `page`, and `limit`, when `fetchBusinessLikesReceived` is called, then `socialService.likesService.fetchBusinessLikesReceived` is invoked with the correct parameters.
    - Given `socialService.likesService.fetchBusinessLikesReceived` returns data, then `fetchBusinessLikesReceived` returns the data correctly, including `items`, `totalCount`, `hasMore`, and `currentPage`.
    - Given `socialService.likesService.fetchBusinessLikesReceived` returns an empty array, then `fetchBusinessLikesReceived` returns an empty `items` array, `totalCount` of 0, `hasMore` as `false`, and `currentPage` as the requested page.
    - Given `socialService.likesService.fetchBusinessLikesReceived` throws an error, then `fetchBusinessLikesReceived` re-throws the error.
    - Given the user is not authenticated, then `fetchBusinessLikesReceived` returns an empty `items` array, `totalCount` of 0, `hasMore` as `false`, and `currentPage` as 1.
    - Given `page` and `limit` are provided, then `socialService.likesService.fetchBusinessLikesReceived` is called with the correct pagination parameters.
- **Error Handling Consistency:**
    - Ensure that all error paths (authentication, service errors) are explicitly tested for both functions.

Tasks:
1. Update the existing test file: `C:\web-app\dukancard\__tests__\app\(dashboard)\dashboard\business\likes\actions.test.ts`.
2. Expand existing test cases to cover:
    - Empty data responses from `socialService`.
    - Pagination logic: verify correct `page` and `limit` parameters are passed.
    - `hasMore` flag propagation.
    - Edge cases for `totalCount` (e.g., when `totalCount` is 0).
3. Add new test cases for:
    - `fetchBusinessMyLikes` when `socialService.likesService.fetchLikes` returns an empty array.
    - `fetchBusinessLikesReceived` when `socialService.likesService.fetchBusinessLikesReceived` returns an empty array.
    - Explicitly test the return values (`items`, `totalCount`, `hasMore`, `currentPage`) for all success and error scenarios.
4. Ensure mocks for `socialService` and `createClient` are robust enough to simulate various scenarios.

File: `C:\web-app\dukancard\app\(dashboard)\dashboard\business\likes\actions.ts`
Platform: dukancard
---